{"system": "Windows", "version": "1.2", "lite": false, "py_version": "3.10", "py_version_micro": "3.10.16", "packages": {"markupsafe": "2.1.5", "pysocks": "1.7.1", "pyyaml": "6.0.2", "absl-py": "2.2.2", "accelerate": "0.34.2", "adagio": "0.2.6", "aiofiles": "23.2.1", "aiohappyeyeballs": "2.6.1", "aiohttp": "3.11.18", "aiohttp-cors": "0.8.1", "aiosignal": "1.3.2", "alembic": "1.15.2", "annotated-types": "0.7.0", "antlr4-python3-runtime": "4.9.3", "anyio": "4.8.0", "appdirs": "1.4.4", "asttokens": "3.0.0", "async-timeout": "5.0.1", "attrs": "25.3.0", "autogluon": "1.2", "autogluon.common": "1.2", "autogluon.core": "1.2", "autogluon.features": "1.2", "autogluon.multimodal": "1.2", "autogluon.tabular": "1.2", "autogluon.timeseries": "1.2", "beautifulsoup4": "4.13.4", "blis": "0.7.11", "boto3": "1.38.3", "botocore": "1.38.3", "cachetools": "5.5.2", "catalogue": "2.0.10", "catboost": "1.2.8", "certifi": "2025.1.31", "chardet": "5.2.0", "charset-normalizer": "3.4.1", "click": "8.1.8", "cloudpathlib": "0.21.0", "cloudpickle": "3.1.1", "colorama": "0.4.6", "colorful": "0.5.6", "colorlog": "6.9.0", "comm": "0.2.2", "confection": "0.1.5", "contourpy": "1.3.2", "coreforecast": "0.0.12", "cycler": "0.12.1", "cymem": "2.0.11", "datasets": "3.5.0", "debugpy": "1.8.12", "decorator": "5.2.1", "defusedxml": "0.7.1", "dill": "0.3.8", "distlib": "0.3.9", "einops": "0.8.1", "et-xmlfile": "2.0.0", "evaluate": "0.4.3", "exceptiongroup": "1.2.2", "executing": "2.1.0", "fastai": "2.7.19", "fastapi": "0.115.8", "fastcore": "1.7.29", "fastdownload": "0.0.7", "fastprogress": "1.0.3", "ffmpy": "0.5.0", "filelock": "3.17.0", "fonttools": "4.57.0", "frozenlist": "1.6.0", "fs": "2.4.16", "fsspec": "2024.12.0", "fugue": "0.9.1", "future": "1.0.0", "gdown": "5.2.0", "gluonts": "0.16.1", "google-api-core": "2.24.2", "google-auth": "2.39.0", "googleapis-common-protos": "1.70.0", "gradio": "5.18.0", "gradio-client": "1.7.2", "graphviz": "0.20.3", "greenlet": "3.2.1", "grpcio": "1.72.0", "h11": "0.14.0", "httpcore": "1.0.7", "httpx": "0.28.1", "huggingface-hub": "0.30.2", "hyperopt": "0.2.7", "idna": "3.10", "imageio": "2.37.0", "importlib-metadata": "8.6.1", "ipykernel": "6.29.5", "ipython": "8.32.0", "jedi": "0.19.2", "jinja2": "3.1.5", "jmespath": "1.0.1", "joblib": "1.4.2", "jsonschema": "4.21.1", "jsonschema-specifications": "2025.4.1", "jupyter-client": "8.6.3", "jupyter-core": "5.7.2", "kiwisolver": "1.4.8", "langcodes": "3.5.0", "language-data": "1.3.0", "lazy-loader": "0.4", "lightgbm": "4.5.0", "lightning": "2.5.1.post0", "lightning-utilities": "0.14.3", "llvmlite": "0.44.0", "mako": "1.3.10", "marisa-trie": "1.2.1", "markdown": "3.8", "markdown-it-py": "3.0.0", "matplotlib": "3.10.1", "matplotlib-inline": "0.1.7", "mdurl": "0.1.2", "mlforecast": "0.13.4", "model-index": "0.1.11", "mpmath": "1.3.0", "msgpack": "1.1.0", "multidict": "6.4.3", "multiprocess": "0.70.16", "murmurhash": "1.0.12", "narwhals": "1.36.0", "nest-asyncio": "1.6.0", "network": "0.1", "networkx": "3.4.2", "nlpaug": "1.1.11", "nltk": "3.8.1", "numba": "0.61.2", "numpy": "2.2.5", "nvidia-ml-py3": "7.352.0", "omegaconf": "2.2.3", "opencensus": "0.11.4", "opencensus-context": "0.1.3", "opendatalab": "0.0.10", "openmim": "0.3.9", "openpyxl": "3.1.5", "openxlab": "0.0.11", "optuna": "4.3.0", "ordered-set": "4.1.0", "orjson": "3.10.15", "packaging": "24.2", "pandas": "2.2.3", "parso": "0.8.4", "patsy": "1.0.1", "pdf2image": "1.17.0", "pickleshare": "0.7.5", "pillow": "11.1.0", "pip": "25.0.1", "platformdirs": "4.3.6", "plotly": "6.0.1", "preshed": "3.0.9", "prometheus-client": "0.21.1", "prompt-toolkit": "3.0.50", "propcache": "0.3.1", "proto-plus": "1.26.1", "protobuf": "6.30.2", "psutil": "6.1.1", "pure-eval": "0.2.3", "py4j": "********", "py-spy": "0.4.0", "pyarrow": "20.0.0", "pyasn1": "0.6.1", "pyasn1-modules": "0.4.2", "pycryptodome": "3.22.0", "pydantic": "2.10.6", "pydantic-core": "2.27.2", "pydub": "0.25.1", "pygments": "2.19.1", "pyparsing": "3.2.3", "pytesseract": "0.3.10", "python-dateutil": "2.9.0.post0", "python-multipart": "0.0.20", "pytorch-lightning": "2.5.1.post0", "pytorch-metric-learning": "2.3.0", "pytz": "2025.1", "pywin32": "307", "pyzmq": "26.2.1", "ray": "2.39.0", "referencing": "0.36.2", "regex": "2024.11.6", "requests": "2.32.3", "rich": "13.9.4", "rpds-py": "0.24.0", "rsa": "4.9.1", "ruff": "0.9.7", "s3transfer": "0.12.0", "safehttpx": "0.1.6", "safetensors": "0.5.3", "scikit-image": "0.24.0", "scikit-learn": "1.5.2", "scipy": "1.15.2", "seaborn": "0.13.2", "semantic-version": "2.10.0", "sentencepiece": "0.2.0", "seqeval": "1.2.2", "setuptools": "75.8.0", "shellingham": "1.5.4", "six": "1.17.0", "smart-open": "7.1.0", "sniffio": "1.3.1", "soupsieve": "2.7", "spacy": "3.7.6", "spacy-legacy": "3.0.12", "spacy-loggers": "1.0.5", "sqlalchemy": "2.0.40", "srsly": "2.5.1", "stack-data": "0.6.3", "starlette": "0.45.3", "statsforecast": "1.7.8", "statsmodels": "0.14.4", "sympy": "1.13.1", "tabulate": "0.9.0", "tensorboard": "2.19.0", "tensorboardx": "*******", "tensorboard-data-server": "0.7.2", "text-unidecode": "1.3", "thinc": "8.2.5", "threadpoolctl": "3.6.0", "tifffile": "2025.3.30", "timm": "1.0.3", "tokenizers": "0.21.1", "tomlkit": "0.13.2", "toolz": "0.12.1", "torch": "2.5.1", "torchmetrics": "1.2.1", "torchvision": "0.20.1", "tornado": "6.4.2", "tqdm": "4.67.1", "traitlets": "5.14.3", "transformers": "4.51.3", "triad": "0.9.8", "typer": "0.15.1", "typing-extensions": "4.12.2", "tzdata": "2025.1", "urllib3": "2.3.0", "utilsforecast": "0.2.4", "uvicorn": "0.34.0", "virtualenv": "20.30.0", "wasabi": "1.1.3", "wcwidth": "0.2.13", "weasel": "0.4.1", "websockets": "15.0", "werkzeug": "3.1.3", "wheel": "0.45.1", "window-ops": "0.0.15", "wrapt": "1.17.2", "xgboost": "2.1.4", "xlrd": "2.0.1", "xxhash": "3.5.0", "yarl": "1.20.0", "zipp": "3.21.0", "autocommand": "2.2.2", "backports.tarfile": "1.2.0", "inflect": "7.3.1", "jaraco.collections": "5.1.0", "jaraco.context": "5.3.0", "jaraco.functools": "4.0.1", "jaraco.text": "3.12.1", "more-itertools": "10.3.0", "tomli": "2.0.1", "typeguard": "4.3.0"}}