# 🚀 耐应力腐蚀油套管智能预测系统 - 部署说明

## 📦 发布包内容

本发布包包含两个版本的系统，可根据客户环境选择合适的版本：

### 1. 可执行文件版（推荐）
- **位置**：`可执行文件版/` 文件夹
- **特点**：无需Python环境，双击即可运行
- **适用**：没有Python环境的客户
- **文件大小**：约50-100MB

### 2. 便携版
- **位置**：`便携版/` 文件夹  
- **特点**：需要Python 3.8+环境
- **适用**：有Python环境的客户
- **文件大小**：约5MB

## 🎯 部署步骤

### 方案一：可执行文件版部署

1. **复制文件**
   ```
   将整个"可执行文件版"文件夹复制到客户计算机
   ```

2. **运行系统**
   ```
   双击"耐应力腐蚀油套管智能预测系统.exe"
   ```

3. **访问系统**
   ```
   浏览器会自动打开并访问 http://localhost:8080
   如未自动打开，请手动访问该地址
   ```

### 方案二：便携版部署

1. **检查Python环境**
   ```bash
   python --version
   # 需要Python 3.8或更高版本
   ```

2. **复制文件**
   ```
   将整个"便携版"文件夹复制到客户计算机
   ```

3. **运行系统**
   ```
   Windows: 双击"启动系统.bat"
   Linux/Mac: 运行"启动系统.sh"
   ```

## 🔧 系统配置

### 默认配置
- **服务器地址**：localhost
- **服务器端口**：8080
- **运行模式**：演示模式（使用模拟预测算法）

### 端口冲突解决
如果8080端口被占用，可以修改配置：

1. **可执行文件版**：暂不支持修改端口
2. **便携版**：编辑`启动程序.py`中的`CONFIG`字典

```python
CONFIG = {
    'HOST': 'localhost',
    'PORT': 8081,  # 修改为其他端口
    'APP_NAME': '耐应力腐蚀油套管智能预测系统',
    'VERSION': '2.0 独立版'
}
```

## 🌐 网络配置

### 本地访问（默认）
- 地址：http://localhost:8080
- 仅本机可访问

### 局域网访问（可选）
如需局域网内其他计算机访问，修改配置：

```python
CONFIG = {
    'HOST': '0.0.0.0',  # 允许所有IP访问
    'PORT': 8080,
    # ...
}
```

访问地址变为：http://[服务器IP]:8080

## 🛡️ 安全注意事项

1. **防火墙设置**
   - 确保8080端口未被防火墙阻止
   - 如需局域网访问，需开放相应端口

2. **杀毒软件**
   - 可执行文件可能被误报为病毒
   - 请添加到杀毒软件白名单

3. **权限要求**
   - 一般情况下无需管理员权限
   - 如遇权限问题，尝试以管理员身份运行

## 📊 功能验证

### 基本功能测试
1. **启动测试**
   - 系统能正常启动
   - 浏览器能自动打开
   - 界面显示正常

2. **模型选择测试**
   - 能看到5个预测模型
   - 模型信息显示正确
   - 能成功选择模型

3. **数据上传测试**
   - 能下载测试数据
   - 能上传CSV/Excel文件
   - 数据验证功能正常

4. **预测功能测试**
   - 能执行预测分析
   - 显示预测结果
   - 统计信息正确

5. **导出功能测试**
   - 能下载Excel结果
   - 能下载CSV结果
   - 数据格式正确

### 引导功能测试
1. 点击"操作引导"按钮
2. 测试引导步骤切换
3. 尝试"一键体验"功能
4. 验证引导内容准确性

## 🔍 故障诊断

### 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 程序无法启动 | 端口被占用 | 更换端口或关闭占用程序 |
| 浏览器未打开 | 防火墙阻止 | 手动访问localhost:8080 |
| 界面显示异常 | 浏览器兼容性 | 使用Chrome/Firefox/Edge |
| 文件上传失败 | 文件格式错误 | 检查文件格式和大小 |
| 预测结果异常 | 数据格式问题 | 使用测试数据验证 |

### 日志信息
系统运行时会在控制台显示详细日志：
- 启动信息
- 请求日志  
- 错误信息
- API调用记录

### 技术支持
如遇无法解决的问题，请收集以下信息联系技术支持：
- 操作系统版本
- 浏览器版本
- 错误截图
- 控制台日志

## 📈 性能优化

### 系统资源
- **内存使用**：约50-200MB
- **CPU使用**：低（仅在预测时较高）
- **磁盘空间**：100MB-1GB（取决于版本）

### 优化建议
1. 关闭不必要的后台程序
2. 确保足够的可用内存
3. 使用SSD硬盘提升启动速度
4. 定期清理临时文件

## 🔄 更新维护

### 版本更新
- 当前版本：2.0 独立版
- 更新方式：替换整个程序文件
- 数据兼容性：向后兼容

### 备份建议
- 备份用户数据文件
- 保存系统配置
- 记录自定义设置

## 📞 联系信息

**技术支持**：宝武钢铁集团技术团队
**系统版本**：2.0 独立版
**发布日期**：2024年
**支持平台**：Windows 7/8/10/11

---

© 2024 宝武钢铁集团 - 耐应力腐蚀油套管智能预测系统
