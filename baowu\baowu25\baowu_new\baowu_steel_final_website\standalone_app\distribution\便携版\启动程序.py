#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
耐应力腐蚀油套管智能预测系统 - 独立运行版本
无需额外Python环境，可直接运行的完整应用
"""

import os
import sys
import json
import webbrowser
import threading
import time
from pathlib import Path
import tempfile
import shutil

# 添加内置的HTTP服务器
try:
    from http.server import HTTPServer, SimpleHTTPRequestHandler
    from urllib.parse import urlparse, parse_qs
    import socketserver
except ImportError:
    print("❌ 系统不支持内置HTTP服务器")
    sys.exit(1)

# 全局配置
CONFIG = {
    'HOST': 'localhost',
    'PORT': 8080,
    'APP_NAME': '耐应力腐蚀油套管智能预测系统',
    'VERSION': '2.0 独立版'
}

class StandaloneHandler(SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=get_web_root(), **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self.path = '/index.html'
        elif self.path.startswith('/api/'):
            self.handle_api_request()
            return
        
        super().do_GET()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404)
    
    def handle_api_request(self):
        """处理API请求"""
        try:
            if self.path == '/api/health':
                self.send_json_response({'status': 'ok', 'message': '系统运行正常'})
            elif self.path == '/api/models':
                self.send_json_response(get_demo_models())
            elif self.path == '/api/guide':
                self.send_json_response(get_guide_info())
            elif self.path == '/api/test_data_list':
                self.send_json_response(get_test_data_list())
            elif self.path.startswith('/api/test_data/'):
                stage = self.path.split('/')[-1]
                self.send_test_data(stage)
            elif self.path == '/api/predict':
                self.handle_predict_request()
            elif self.path == '/api/validate_data':
                self.handle_validate_request()
            else:
                self.send_error(404, "API接口不存在")
        except Exception as e:
            self.send_error(500, f"服务器错误: {str(e)}")
    
    def send_json_response(self, data):
        """发送JSON响应"""
        response = json.dumps(data, ensure_ascii=False, indent=2)
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))
    
    def send_test_data(self, stage):
        """发送测试数据文件"""
        test_file = get_web_root() / 'test_data' / f'test_{stage}.csv'
        if test_file.exists():
            self.send_response(200)
            self.send_header('Content-Type', 'text/csv; charset=utf-8')
            self.send_header('Content-Disposition', f'attachment; filename="test_{stage}.csv"')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            with open(test_file, 'rb') as f:
                self.wfile.write(f.read())
        else:
            self.send_error(404, "测试数据文件不存在")
    
    def handle_predict_request(self):
        """处理预测请求"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            request_data = json.loads(post_data.decode('utf-8'))
            result = generate_demo_prediction(request_data)
            self.send_json_response(result)
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)})
    
    def handle_validate_request(self):
        """处理数据验证请求"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length)
        
        try:
            request_data = json.loads(post_data.decode('utf-8'))
            result = validate_demo_data(request_data)
            self.send_json_response(result)
        except Exception as e:
            self.send_json_response({'success': False, 'error': str(e)})

def get_web_root():
    """获取Web根目录"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        return Path(sys._MEIPASS) / 'web'
    else:
        # 开发环境
        return Path(__file__).parent / 'web'

def get_demo_models():
    """获取演示模型列表"""
    return {
        'success': True,
        'models': {
            'stage_1': {
                '硬度预测模型': {
                    'description': '硬度相关预测模型 - 预测材料硬度值',
                    'target': '硬度',
                    'available': True
                }
            },
            'stage_2': {
                '拉伸强度预测模型': {
                    'description': '拉伸屈服预测模型 - 预测材料拉伸强度',
                    'target': '拉伸强度',
                    'available': True
                }
            },
            'stage_3_A': {
                'A法抗硫预测模型': {
                    'description': 'A法抗硫预测模型 - 预测抗硫化氢腐蚀性能',
                    'target': 'A法抗硫',
                    'available': True
                }
            },
            'stage_3_D': {
                'D法抗硫预测模型': {
                    'description': 'D法抗硫预测模型 - 预测抗硫化氢腐蚀性能（D法标准）',
                    'target': 'D法抗硫',
                    'available': True
                }
            },
            'stage_4': {
                '综合质量预测模型': {
                    'description': '硬度和拉伸预测抗硫模型 - 综合预测模型',
                    'target': '综合质量',
                    'available': True
                }
            }
        },
        'message': '演示模式：所有模型均可用'
    }

def get_guide_info():
    """获取引导信息"""
    return {
        'success': True,
        'guide_steps': [
            {
                'step': 1,
                'title': '选择预测模型',
                'description': '根据您的预测需求选择合适的机器学习模型',
                'details': [
                    '硬度相关预测模型：用于预测材料硬度值',
                    '拉伸屈服预测模型：用于预测材料拉伸强度',
                    'A法抗硫预测模型：用于预测抗硫化氢腐蚀性能',
                    'D法抗硫预测模型：用于预测抗硫化氢腐蚀性能（D法标准）',
                    '硬度和拉伸预测抗硫模型：综合预测模型'
                ],
                'tips': '选择与您的测试目标最匹配的模型类型'
            },
            {
                'step': 2,
                'title': '准备数据文件',
                'description': '上传包含工艺参数的CSV或Excel文件',
                'details': [
                    '支持的文件格式：CSV (.csv)、Excel (.xlsx, .xls)',
                    '文件大小限制：最大50MB',
                    '数据要求：包含模型所需的特征列',
                    '数据质量：确保数值数据格式正确，避免空值过多',
                    '测试数据：系统提供了各阶段的测试数据文件供下载使用'
                ],
                'tips': '建议先下载测试数据文件进行试用，熟悉系统功能后再使用自己的数据',
                'test_data_available': True
            },
            {
                'step': 3,
                'title': '数据验证与预览',
                'description': '系统会自动验证数据与模型的兼容性',
                'details': [
                    '特征匹配检查：验证数据列是否包含模型所需特征',
                    '数据类型检查：确保数值特征格式正确',
                    '缺失值处理：系统会自动处理部分缺失值',
                    '数据预览：查看前10行数据确认格式正确'
                ],
                'tips': '如果验证失败，请检查数据格式和特征名称'
            },
            {
                'step': 4,
                'title': '执行预测分析',
                'description': '使用训练好的机器学习模型进行预测',
                'details': [
                    '自动特征预处理：系统会对高偏度特征进行变换',
                    '批量预测：支持同时预测多个样本',
                    '结果统计：提供预测结果的统计分析',
                    '可视化展示：生成预测结果分布图表'
                ],
                'tips': '预测过程可能需要几分钟，请耐心等待'
            },
            {
                'step': 5,
                'title': '结果导出与报告',
                'description': '下载预测结果和生成专业分析报告',
                'details': [
                    'Excel导出：包含原始数据和预测结果的完整表格',
                    'CSV导出：便于进一步数据处理的格式',
                    'PDF报告：包含统计分析、图表和专业建议的完整报告',
                    '质量评估：基于预测结果的质量等级判断'
                ],
                'tips': '建议保存完整的分析报告用于质量控制记录'
            }
        ],
        'system_info': {
            'name': '基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统',
            'version': '2.0 独立版',
            'developer': '宝武钢铁集团',
            'description': '专业的油套管质量预测平台，基于先进的机器学习算法'
        }
    }

def get_test_data_list():
    """获取测试数据列表"""
    test_files = []
    test_data_dir = get_web_root() / 'test_data'
    
    if test_data_dir.exists():
        for csv_file in test_data_dir.glob('test_*.csv'):
            stage = csv_file.stem.replace('test_', '')
            stage_descriptions = {
                'stage_1': '硬度相关预测模型',
                'stage_2': '拉伸屈服预测模型',
                'stage_3_A': 'A法抗硫预测模型',
                'stage_3_D': 'D法抗硫预测模型',
                'stage_4': '硬度和拉伸预测抗硫模型'
            }
            
            test_files.append({
                'stage': stage,
                'filename': csv_file.name,
                'description': stage_descriptions.get(stage, f'阶段 {stage}'),
                'download_url': f'/api/test_data/{stage}'
            })
    
    return {
        'success': True,
        'test_files': test_files,
        'message': f'找到 {len(test_files)} 个测试数据文件'
    }

def generate_demo_prediction(request_data):
    """生成演示预测结果"""
    import random
    
    try:
        data = request_data.get('data', [])
        model_name = request_data.get('model_name', '')
        
        if not data:
            return {'success': False, 'error': '没有提供数据'}
        
        # 生成模拟预测结果
        predictions = []
        for _ in range(len(data)):
            if "硬度" in model_name:
                pred = random.normalvariate(250, 30)
            elif "拉伸" in model_name:
                pred = random.normalvariate(450, 50)
            elif "抗硫" in model_name:
                pred = random.normalvariate(500, 100)
            else:
                pred = random.normalvariate(300, 50)
            predictions.append(max(0, pred))
        
        # 计算统计信息
        avg_pred = sum(predictions) / len(predictions)
        min_pred = min(predictions)
        max_pred = max(predictions)
        
        return {
            'success': True,
            'predictions': predictions,
            'statistics': {
                'count': len(predictions),
                'mean': round(avg_pred, 2),
                'min': round(min_pred, 2),
                'max': round(max_pred, 2),
                'std': round((sum((p - avg_pred) ** 2 for p in predictions) / len(predictions)) ** 0.5, 2)
            },
            'message': f'成功预测 {len(predictions)} 个样本（演示模式）'
        }
    
    except Exception as e:
        return {'success': False, 'error': f'预测失败: {str(e)}'}

def validate_demo_data(request_data):
    """验证演示数据"""
    try:
        data = request_data.get('data', [])
        
        if not data:
            return {'success': False, 'error': '没有提供数据'}
        
        # 模拟数据验证
        return {
            'success': True,
            'validation_result': {
                'is_valid': True,
                'row_count': len(data),
                'column_count': len(data[0]) if data else 0,
                'missing_features': [],
                'data_types_valid': True
            },
            'message': '数据验证通过（演示模式）'
        }
    
    except Exception as e:
        return {'success': False, 'error': f'数据验证失败: {str(e)}'}

def setup_web_files():
    """设置Web文件"""
    web_root = get_web_root()
    web_root.mkdir(parents=True, exist_ok=True)
    
    # 复制必要的文件
    source_dir = Path(__file__).parent.parent
    
    # 复制模板文件
    templates_dir = web_root
    templates_dir.mkdir(exist_ok=True)
    
    # 复制静态文件
    static_dir = web_root / 'static'
    if (source_dir / 'backend' / 'static').exists():
        if static_dir.exists():
            shutil.rmtree(static_dir)
        shutil.copytree(source_dir / 'backend' / 'static', static_dir)
    
    # 复制HTML文件
    html_source = source_dir / 'backend' / 'templates' / 'index.html'
    html_target = web_root / 'index.html'
    if html_source.exists():
        shutil.copy2(html_source, html_target)
    
    # 复制测试数据
    test_data_dir = web_root / 'test_data'
    if (source_dir / 'test_data').exists():
        if test_data_dir.exists():
            shutil.rmtree(test_data_dir)
        shutil.copytree(source_dir / 'test_data', test_data_dir)
    
    print(f"✅ Web文件已设置到: {web_root}")

def start_server():
    """启动HTTP服务器"""
    try:
        # 设置Web文件
        setup_web_files()
        
        # 创建服务器
        with socketserver.TCPServer((CONFIG['HOST'], CONFIG['PORT']), StandaloneHandler) as httpd:
            print(f"🚀 {CONFIG['APP_NAME']} v{CONFIG['VERSION']}")
            print(f"🌐 服务器启动成功: http://{CONFIG['HOST']}:{CONFIG['PORT']}")
            print(f"📁 Web根目录: {get_web_root()}")
            print("🔧 按 Ctrl+C 停止服务")
            print("=" * 60)
            
            # 延迟打开浏览器
            def open_browser():
                time.sleep(2)
                try:
                    webbrowser.open(f'http://{CONFIG["HOST"]}:{CONFIG["PORT"]}')
                    print("🌍 已自动打开浏览器")
                except:
                    print("⚠️ 无法自动打开浏览器，请手动访问上述地址")
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    start_server()
