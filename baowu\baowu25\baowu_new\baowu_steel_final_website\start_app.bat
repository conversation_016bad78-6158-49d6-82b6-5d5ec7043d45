@echo off
chcp 65001 >nul
title 耐应力腐蚀油套管智能预测系统

echo.
echo ========================================
echo 🔬 耐应力腐蚀油套管智能预测系统
echo 📊 基于机器学习的质量预测平台
echo 🏭 宝武钢铁集团
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python，请先安装Python 3.8+
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

:: 检查是否存在虚拟环境
if not exist "venv" (
    echo 📦 创建虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

:: 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ 激活虚拟环境失败
    pause
    exit /b 1
)

:: 检查并安装依赖
echo 📋 检查依赖包...
pip list | findstr Flask >nul
if %errorlevel% neq 0 (
    echo 📦 安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 安装依赖包失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已安装
)

:: 检查模型文件和演示模式
echo 🔍 检查系统配置...
if exist "models\stage_1" (
    echo ✅ 找到模型文件，将使用真实AutoGluon模型
) else (
    echo ⚠️ 未找到模型文件，将使用演示模式
    echo 💡 演示模式提供模拟预测结果，无需真实模型
)

echo.
echo 🔍 检查AutoGluon...
python -c "from autogluon.tabular import TabularPredictor" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ AutoGluon未安装，将使用演示模式
    echo 💡 演示模式无需AutoGluon，提供完整功能体验
) else (
    echo ✅ AutoGluon已安装，支持真实模型预测
)

:: 启动后端服务
echo.
echo 🚀 启动智能预测系统...
echo 🌐 服务地址：http://localhost:5000
echo 📱 系统将自动打开浏览器
echo 🎯 支持演示模式和真实预测模式
echo.

cd backend
python app.py
