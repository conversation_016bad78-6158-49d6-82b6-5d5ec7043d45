@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统启动器
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误：未找到Python，请先安装Python 3.8+
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装
python --version

:: 检查是否存在虚拟环境
if not exist "venv" (
    echo 📦 创建虚拟环境...
    python -m venv venv
    if %errorlevel% neq 0 (
        echo ❌ 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
)

:: 激活虚拟环境
echo 🔧 激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo ❌ 激活虚拟环境失败
    pause
    exit /b 1
)

:: 检查并安装依赖
echo 📋 检查依赖包...
pip list | findstr Flask >nul
if %errorlevel% neq 0 (
    echo 📦 安装依赖包...
    pip install -r requirements.txt
    if %errorlevel% neq 0 (
        echo ❌ 安装依赖包失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已安装
)

:: 检查模型文件
echo 🔍 检查模型文件...
if not exist "models\stage_1" (
    echo ❌ 警告：未找到模型文件，请确保models目录下有stage_1到stage_4的模型文件
)

:: 启动后端服务
echo 🌐 启动后端服务...
echo 服务地址：http://localhost:5000
echo 前端页面：请在浏览器中打开 frontend/index.html

cd backend
python app.py
