/*
基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统 - 主样式表
*/

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #4caf50;
    --error-color: #f44336;
    --warning-color: #ff9800;
    --info-color: #2196f3;
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
    background-attachment: fixed;
    min-height: 100vh;
    padding: 20px;
    line-height: 1.6;
    position: relative;
    overflow-x: hidden;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.98);
    border-radius: 28px;
    padding: 50px;
    box-shadow:
        0 32px 64px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(255, 255, 255, 0.05),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.header {
    text-align: center;
    margin-bottom: 60px;
    position: relative;
    padding-bottom: 40px;
    border-bottom: 2px solid rgba(224, 224, 224, 0.5);
}

.system-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
}

.logo-icon {
    font-size: 80px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.system-info {
    text-align: left;
}

.system-info h1 {
    font-size: 36px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 10px;
    line-height: 1.2;
}

.system-subtitle {
    font-size: 16px;
    color: #666;
    font-style: italic;
    margin-bottom: 15px;
    font-weight: 400;
}

.system-badges {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.badge {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.badge-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.badge-secondary {
    background: linear-gradient(135deg, var(--info-color), #21cbf3);
    color: white;
}

.badge-success {
    background: linear-gradient(135deg, var(--success-color), #66bb6a);
    color: white;
}

.header p {
    color: #666;
    font-size: 1.3rem;
    font-weight: 400;
    margin-top: 15px;
    opacity: 0.8;
}

.step {
    margin-bottom: 40px;
    padding: 40px;
    border: 2px solid rgba(224, 224, 224, 0.5);
    border-radius: 20px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

/* 新的步骤头部样式 */
.step-header {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    margin-bottom: 30px;
}

.step-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.step-number {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.step-line {
    width: 2px;
    height: 60px;
    background: linear-gradient(180deg, var(--primary-color), transparent);
    margin-top: 10px;
}

.step:last-child .step-line {
    display: none;
}

.step-content h3 {
    font-size: 24px;
    color: var(--primary-color);
    margin-bottom: 8px;
    font-weight: 600;
}

.step-description {
    color: #666;
    font-size: 16px;
    line-height: 1.6;
    margin: 0;
}

/* 保留旧的样式作为备用 */
.step h3 {
    color: #333;
    margin-bottom: 25px;
    font-size: 1.6rem;
    display: flex;
    align-items: center;
    font-weight: 700;
    position: relative;
    z-index: 2;
}

/* 预测信息卡片 */
.prediction-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.info-card {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    transition: var(--transition);
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.info-icon {
    font-size: 32px;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 12px;
    color: white;
}

.info-text strong {
    display: block;
    color: var(--primary-color);
    font-size: 16px;
    margin-bottom: 4px;
}

.info-text p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

/* 导出选项 */
.export-options {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-export, .btn-report {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 25px;
    border-radius: 12px;
    border: none;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    text-decoration: none;
}

.btn-export {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #495057;
    border: 2px solid #dee2e6;
}

.btn-export:hover {
    background: linear-gradient(135deg, #e9ecef, #dee2e6);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 移除重复的 .btn-report 样式，使用后面的定义 */

.btn-icon {
    font-size: 18px;
}

.btn-text {
    font-size: 14px;
}

/* 改进文件上传样式 */
.file-upload {
    position: relative;
    display: inline-block;
    cursor: pointer;
    width: 100%;
}

.file-upload-label {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    padding: 40px;
    border: 3px dashed #ccc;
    border-radius: 12px;
    background: rgba(248, 249, 250, 0.8);
    transition: var(--transition);
    cursor: pointer;
}

.file-upload-label:hover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.05);
}

.upload-icon {
    font-size: 48px;
    color: var(--primary-color);
}

.upload-text {
    text-align: center;
}

.upload-text strong {
    display: block;
    color: var(--primary-color);
    font-size: 18px;
    margin-bottom: 8px;
}

.upload-text small {
    color: #666;
    font-size: 14px;
}

/* 测试数据样式 */
.test-data-section {
    margin-bottom: 20px;
    text-align: center;
}

.test-data-list {
    margin-top: 20px;
    padding: 20px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.test-data-header {
    text-align: center;
    margin-bottom: 20px;
}

.test-data-header h4 {
    color: var(--primary-color);
    margin-bottom: 8px;
}

.test-data-header p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

.test-data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.test-data-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
    transition: var(--transition);
}

.test-data-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.test-data-info h5 {
    color: var(--primary-color);
    margin-bottom: 8px;
    font-size: 16px;
}

.test-data-info p {
    margin: 4px 0;
    font-size: 14px;
    color: #666;
}

.test-data-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.btn-download, .btn-demo {
    flex: 1;
    padding: 8px 12px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    text-align: center;
    transition: var(--transition);
}

.btn-download {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.btn-download:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.btn-demo {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-demo:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.file-upload input[type="file"] {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.file-upload-label {
    display: block;
    padding: 80px 30px;
    border: 3px dashed rgba(102, 126, 234, 0.4);
    border-radius: 20px;
    text-align: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9));
    transition: var(--transition);
    color: var(--primary-color);
    font-weight: 600;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.file-upload-label::before {
    content: '📁';
    font-size: 64px;
    display: block;
    margin-bottom: 20px;
}

.btn {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 16px 36px;
    border-radius: 12px;
    cursor: pointer;
    font-size: 16px;
    font-weight: 600;
    transition: var(--transition);
    margin: 8px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-report {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
}

.btn-report:hover {
    box-shadow: 0 12px 30px rgba(238, 90, 36, 0.4);
}

.data-preview {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
    border-radius: 20px;
    padding: 30px;
    margin-top: 25px;
    overflow-x: auto;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    max-height: 600px;
    overflow-y: auto;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(224, 224, 224, 0.3);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.data-table th,
.data-table td {
    padding: 16px 20px;
    text-align: left;
    border-bottom: 1px solid rgba(224, 224, 224, 0.5);
    white-space: nowrap;
    transition: var(--transition);
}

.data-table th {
    background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
    color: white;
    font-weight: 700;
    position: sticky;
    top: 0;
    z-index: 10;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 13px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-box {
    background: linear-gradient(135deg, rgba(227, 242, 253, 0.9), rgba(187, 222, 251, 0.9));
    border-left: 6px solid var(--info-color);
    padding: 25px 30px;
    margin: 25px 0;
    border-radius: 0 16px 16px 0;
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(33, 150, 243, 0.2);
    position: relative;
    overflow: hidden;
}

.success-box {
    background: linear-gradient(135deg, rgba(232, 245, 233, 0.9), rgba(200, 230, 201, 0.9));
    border-left: 6px solid var(--success-color);
    padding: 25px 30px;
    margin: 25px 0;
    border-radius: 0 16px 16px 0;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(76, 175, 80, 0.2);
    position: relative;
    overflow: hidden;
}

.error-box {
    background: linear-gradient(135deg, rgba(255, 235, 238, 0.9), rgba(255, 205, 210, 0.9));
    border-left: 6px solid var(--error-color);
    padding: 25px 30px;
    margin: 25px 0;
    border-radius: 0 16px 16px 0;
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(244, 67, 54, 0.2);
    position: relative;
    overflow: hidden;
}

.warning-box {
    background: linear-gradient(135deg, rgba(255, 243, 224, 0.9), rgba(255, 224, 178, 0.9));
    border-left: 6px solid var(--warning-color);
    padding: 25px 30px;
    margin: 25px 0;
    border-radius: 0 16px 16px 0;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 152, 0, 0.2);
    position: relative;
    overflow: hidden;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
    vertical-align: middle;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hidden {
    display: none !important;
}

.model-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stage-section {
    grid-column: 1 / -1;
    margin-bottom: 30px;
    padding: 25px;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, #fafafa, #f5f5f5);
}

.stage-section h4 {
    color: var(--primary-color);
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, #f8f9ff, #e8f4fd);
    border-radius: 8px;
    border-left: 5px solid var(--primary-color);
    font-size: 1.2rem;
    font-weight: 600;
}

.stage-models {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.model-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95));
    border: 2px solid rgba(224, 224, 224, 0.5);
    border-radius: 20px;
    padding: 30px;
    transition: var(--transition);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.model-card.available:hover {
    border-color: rgba(102, 126, 234, 0.4);
    transform: translateY(-6px);
    box-shadow: 0 16px 40px rgba(102, 126, 234, 0.2);
}

.model-card.unavailable {
    opacity: 0.6;
    cursor: not-allowed;
    background: linear-gradient(135deg, rgba(245, 245, 245, 0.9), rgba(238, 238, 238, 0.9));
    filter: grayscale(0.3);
}

.model-card.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.25);
    transform: translateY(-4px);
}

.model-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 12px;
    font-size: 1.2rem;
}

.model-info {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
}

.prediction-results {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    margin-top: 25px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: linear-gradient(135deg, #f8f9ff, #e8f4fd);
    padding: 20px;
    border-radius: var(--border-radius);
    text-align: center;
    border: 1px solid #e0e0e0;
    transition: var(--transition);
}

.stat-label {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 8px;
}

.stat-value {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
}

.notification {
    position: fixed;
    top: 30px;
    right: 30px;
    padding: 25px 35px;
    border-radius: 16px;
    box-shadow: 0 16px 40px rgba(0, 0, 0, 0.25);
    z-index: 1000;
    max-width: 450px;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 600;
    letter-spacing: 0.3px;
}

.notification.success {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.95), rgba(69, 160, 73, 0.95));
    color: white;
    border-left: 6px solid #4caf50;
}

.notification.error {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.95), rgba(229, 57, 53, 0.95));
    color: white;
    border-left: 6px solid #f44336;
}

.notification.warning {
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.95), rgba(251, 140, 0, 0.95));
    color: white;
    border-left: 6px solid #ff9800;
}

.notification.info {
    background: linear-gradient(135deg, rgba(33, 150, 243, 0.95), rgba(30, 136, 229, 0.95));
    color: white;
    border-left: 6px solid #2196f3;
}

.chart-container {
    position: relative;
    height: 400px;
    margin-top: 30px;
    padding: 20px;
    background: white;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.backend-status {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    z-index: 1000;
    cursor: pointer;
    transition: all 0.3s ease;
}

.backend-status.online {
    background: #4caf50;
    color: white;
}

.backend-status.offline {
    background: #f44336;
    color: white;
}

.backend-status.demo {
    background: #ff9800;
    color: white;
}

/* 报告预览样式 */
.report-preview {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    z-index: 2000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.report-preview-content {
    background: white;
    max-width: 900px;
    max-height: 90vh;
    width: 100%;
    border-radius: 20px;
    overflow: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    position: relative;
}

.report-preview-header {
    position: sticky;
    top: 0;
    background: white;
    padding: 20px 30px;
    border-bottom: 1px solid #e0e0e0;
    z-index: 10;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.report-preview-body {
    padding: 30px;
}

.report-section {
    margin-bottom: 40px;
}

.report-section h2 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.report-section h3 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.4rem;
}

.report-charts {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin: 30px 0;
}

.report-chart {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.report-chart canvas {
    max-width: 100%;
    height: 300px !important;
}

.close-preview {
    position: absolute;
    top: 20px;
    right: 30px;
    background: none;
    border: none;
    font-size: 30px;
    cursor: pointer;
    color: #666;
    transition: color 0.3s;
}

.close-preview:hover {
    color: #333;
}

.transform-info {
    background: #f0f7ff;
    border: 1px solid #b3d9ff;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    font-size: 0.9rem;
}

.transform-info h5 {
    color: #0066cc;
    margin-bottom: 10px;
}

.transform-list {
    list-style: none;
    padding-left: 20px;
}

.transform-list li {
    margin: 5px 0;
    position: relative;
    padding-left: 20px;
}

.transform-list li:before {
    content: '→';
    position: absolute;
    left: 0;
    color: #0066cc;
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 25px 20px;
        border-radius: 20px;
    }

    .header h1 {
        font-size: 2.2rem;
    }

    .step {
        padding: 25px 20px;
    }

    .btn {
        width: 100%;
        margin: 5px 0;
    }

    .report-charts {
        grid-template-columns: 1fr;
    }
}

/* 引导功能样式 */
.header-actions {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    gap: 10px;
}

.btn-guide, .btn-help {
    padding: 8px 16px;
    font-size: 14px;
    border-radius: 20px;
    border: 2px solid var(--primary-color);
    background: rgba(255, 255, 255, 0.9);
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition);
}

.btn-guide:hover, .btn-help:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
}

/* 引导模态框 */
.guide-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
}

.guide-modal.hidden {
    display: none;
}

.guide-modal-content {
    background: white;
    border-radius: var(--border-radius);
    max-width: 800px;
    width: 90%;
    max-height: 90%;
    overflow-y: auto;
    position: relative;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.guide-modal-header {
    padding: 30px 30px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.guide-modal-header h2 {
    margin: 0;
    font-size: 24px;
}

.close-guide {
    background: none;
    border: none;
    font-size: 28px;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.close-guide:hover {
    background: rgba(255, 255, 255, 0.2);
}

.guide-modal-body {
    padding: 30px;
}

.guide-welcome {
    text-align: center;
}

.guide-welcome h3 {
    color: var(--primary-color);
    margin-bottom: 20px;
    font-size: 28px;
}

.guide-welcome p {
    font-size: 16px;
    color: #666;
    margin-bottom: 30px;
    line-height: 1.8;
}

.guide-options {
    display: flex;
    gap: 20px;
    justify-content: center;
}

.guide-steps {
    animation: fadeIn 0.5s ease-in-out;
}

.guide-step {
    margin-bottom: 30px;
}

.guide-step-header {
    margin-bottom: 20px;
}

.guide-step-header h3 {
    color: var(--primary-color);
    font-size: 22px;
    margin-bottom: 15px;
}

.guide-progress {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
}

.guide-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.5s ease;
}

.guide-step-content {
    margin-bottom: 30px;
}

.guide-description {
    font-size: 16px;
    color: #333;
    margin-bottom: 20px;
    font-weight: 500;
}

.guide-details {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.guide-details h4 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 16px;
}

.guide-details ul {
    list-style: none;
    padding: 0;
}

.guide-details li {
    padding: 8px 0;
    padding-left: 20px;
    position: relative;
    color: #555;
}

.guide-details li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.guide-tips {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid var(--warning-color);
    font-size: 14px;
    color: #856404;
}

.guide-demo-section {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid var(--info-color);
    margin-top: 20px;
    text-align: center;
}

.guide-demo-section h4 {
    color: var(--info-color);
    margin-bottom: 10px;
    font-size: 16px;
}

.guide-demo-section p {
    color: #0277bd;
    margin-bottom: 15px;
    font-size: 14px;
}

.guide-demo-section .btn-demo {
    background: linear-gradient(135deg, var(--info-color), #21cbf3);
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    border: none;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
}

.guide-demo-section .btn-demo:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

.guide-step-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.guide-step-actions .btn {
    padding: 10px 20px !important;
    font-size: 14px !important;
    border-radius: 6px !important;
    border: none !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
    min-width: 80px !important;
}

.guide-step-actions .btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    color: white !important;
}

.guide-step-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #5a6268) !important;
    color: white !important;
}

.guide-step-actions .btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

/* 引导高亮遮罩 */
.guide-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    pointer-events: none;
}

.guide-overlay.hidden {
    display: none;
}

/* 帮助内容样式 */
.help-content {
    line-height: 1.8;
}

.help-content h3 {
    color: var(--primary-color);
    margin: 25px 0 15px 0;
    font-size: 18px;
}

.help-content ul {
    margin-bottom: 20px;
}

.help-content li {
    margin-bottom: 8px;
    color: #555;
}

.help-content strong {
    color: var(--primary-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .container {
        padding: 30px 20px;
    }

    .system-logo {
        flex-direction: column;
        gap: 20px;
        text-align: center;
    }

    .system-info {
        text-align: center;
    }

    .system-info h1 {
        font-size: 28px;
    }

    .header-actions {
        position: static;
        justify-content: center;
        margin-top: 20px;
    }

    .step {
        padding: 25px 20px;
    }

    .step-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 15px;
    }

    .step-line {
        display: none;
    }

    .prediction-info {
        grid-template-columns: 1fr;
    }

    .info-card {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .export-options {
        flex-direction: column;
    }

    .btn-export, .btn-report {
        width: 100%;
        justify-content: center;
    }

    .file-upload-label {
        flex-direction: column;
        gap: 15px;
        padding: 30px 20px;
    }

    .upload-icon {
        font-size: 36px;
    }

    .guide-modal-content {
        width: 95%;
        margin: 20px;
    }

    .guide-modal-body {
        padding: 20px;
    }

    .guide-options {
        flex-direction: column;
        align-items: center;
    }

    .guide-step-actions {
        flex-direction: column;
    }

    .guide-step-actions .btn {
        width: 100%;
    }

    .btn {
        width: 100%;
        margin: 5px 0;
    }

    .report-charts {
        grid-template-columns: 1fr;
    }
}
