@echo off
chcp 65001 >nul
title 构建两套完整版发布包

echo.
echo ========================================
echo 📦 耐应力腐蚀油套管智能预测系统
echo 🏗️ 两套完整版构建工具
echo ========================================
echo.

echo 📋 构建方案：
echo.
echo 🔹 方案一：客户端-服务器分离版
echo   ├── 服务器端(Linux)：包含AutoGluon模型，提供API服务
echo   └── 客户端(Windows)：轻量级前端，连接服务器
echo.
echo 🔹 方案二：本地完整版(Windows)
echo   └── 前后端完全集成，包含所有模型，本地运行
echo.

echo 🔍 检查Python环境...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 🔍 检查基础依赖...
python -c "import pandas, numpy, flask, requests" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 缺少基础依赖包
    echo 正在安装基础依赖...
    pip install pandas numpy flask flask-cors requests
)

echo.
echo 🔍 检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
)

echo.
echo 🔍 检查AutoGluon（可选）...
python -c "from autogluon.tabular import TabularPredictor" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ 未找到AutoGluon
    echo.
    set /p install_ag="是否安装AutoGluon？(推荐，但需要较长时间) (Y/N): "
    if /i "%install_ag%"=="Y" (
        echo 正在安装AutoGluon...
        pip install autogluon
    ) else (
        echo 将构建演示版本（不包含真实模型预测）
    )
)

echo.
echo 🚀 开始构建两套完整版...
python build_two_versions.py

echo.
echo 📁 检查构建结果...
if exist "two_versions_distribution" (
    echo ✅ 两套完整版构建成功！
    echo 📂 发布目录: two_versions_distribution\
    echo.
    
    if exist "two_versions_distribution\客户端-服务器分离版" (
        echo ✅ 客户端-服务器分离版: 已创建
        echo   📊 服务器端(Linux): 包含AutoGluon模型
        echo   💻 客户端(Windows): 轻量级前端
    )
    
    if exist "two_versions_distribution\本地完整版(Windows)" (
        echo ✅ 本地完整版(Windows): 已创建
        echo   🎯 特点: 前后端完全集成，本地运行
    )
    
    if exist "耐应力腐蚀油套管智能预测系统_两套完整版.zip" (
        echo ✅ 完整ZIP压缩包: 已创建
    )
    
    echo.
    echo 🎉 构建完成！
    echo.
    echo 📋 部署建议：
    echo.
    echo 🔹 客户端-服务器分离版：
    echo   1. 将服务器端部署到Linux服务器
    echo   2. 在Windows上运行客户端
    echo   3. 输入服务器IP和端口连接
    echo.
    echo 🔹 本地完整版：
    echo   1. 直接在Windows上双击运行
    echo   2. 无需任何配置和网络连接
    echo   3. 适合单机使用
    echo.
    
    set /p choice="是否打开发布目录？(Y/N): "
    if /i "%choice%"=="Y" (
        explorer two_versions_distribution
    )
    
) else (
    echo ❌ 构建失败，请检查错误信息
    echo.
    echo 💡 可能的解决方案：
    echo 1. 确保安装了所有依赖包
    echo 2. 检查模型文件是否存在
    echo 3. 确保有足够的磁盘空间（至少5GB）
    echo 4. 以管理员身份运行
    echo 5. 检查complete_web目录是否存在
)

echo.
echo 📞 如需技术支持，请联系开发团队
echo © 2024 宝武钢铁集团

pause
