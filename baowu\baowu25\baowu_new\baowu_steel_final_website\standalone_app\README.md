# 🔬 耐应力腐蚀油套管智能预测系统

## 📁 核心文件

### 🔹 方案一：客户端-服务器分离版
- `server_backend.py` - 服务器端（Linux/Windows）
- `client_frontend.py` - 客户端（Windows）
- `启动服务器.bat` - Windows服务器启动
- `启动客户端.bat` - Windows客户端启动
- `start_server.sh` - Linux服务器启动

### 🔹 方案二：本地完整版
- `complete_standalone_app.py` - 本地完整版
- `complete_web/` - Web资源文件夹
- `构建发布包.bat` - 单版本构建

### 🔹 构建工具
- `build_two_versions.py` - 两套版本构建脚本
- `构建两套完整版.bat` - 一键构建工具

## 🚀 快速开始

### 方案一：分离版
```bash
# 服务器端
python server_backend.py
# 或双击：启动服务器.bat

# 客户端
python client_frontend.py
# 或双击：启动客户端.bat
```

### 方案二：本地版
```bash
python complete_standalone_app.py
```

### 构建可执行文件
```bash
双击运行：构建两套完整版.bat
```

## ✨ 主要特色

- ✅ 18个真实AutoGluon模型
- ✅ 智能端口自动分配（避免冲突）
- ✅ 支持多实例并发运行
- ✅ 完整前端资源（已修复渲染）
- ✅ 两套灵活部署方案

## 🔧 环境要求

- Python 3.8+
- Flask, requests, pandas, numpy
- AutoGluon（可选，无则使用演示模式）
- PyInstaller（构建时需要）

---
© 2024 宝武钢铁集团
