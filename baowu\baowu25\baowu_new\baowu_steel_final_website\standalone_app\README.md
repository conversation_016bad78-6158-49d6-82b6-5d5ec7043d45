# 🔬 耐应力腐蚀油套管智能预测系统 - 两套完整版

## 🎯 两套方案

### 方案一：客户端-服务器分离版
- `server_backend.py` - 服务器端（运行在Linux）
- `client_frontend.py` - 客户端（运行在Windows）
- `start_server.sh` - Linux服务器启动脚本

### 方案二：本地完整版
- `complete_standalone_app.py` - 本地完整版（Windows）
- `complete_web/` - Web资源文件夹

## 🚀 使用方法

### 客户端-服务器分离版
```bash
# Linux服务器端
python3 server_backend.py
# 或使用启动脚本
./start_server.sh

# Windows客户端
python client_frontend.py
```

### 本地完整版
```bash
python complete_standalone_app.py
```

## 📦 一键构建两套版本
双击运行：`构建两套完整版.bat`

构建结果：
- **客户端-服务器分离版**：服务器端(Linux) + 客户端(Windows)
- **本地完整版**：Windows完整版可执行文件

## 🔧 环境要求

- Python 3.8+
- Flask, requests (`pip install flask flask-cors requests`)
- AutoGluon (`pip install autogluon`) - 可选
- PyInstaller (`pip install pyinstaller`) - 构建时需要

## ✨ 特色功能

- ✅ 18个真实AutoGluon模型
- ✅ 完整前端资源（已修复渲染问题）
- ✅ 专业数据验证和预测
- ✅ 智能操作引导
- ✅ 支持客户端-服务器分离部署
- ✅ 支持本地完整版部署
- ✅ **自动端口分配**：客户端自动寻找可用端口，避免冲突
- ✅ **多实例支持**：可同时运行多个客户端实例

## 🔌 端口说明

### 自动端口分配
- **客户端和本地完整版**：自动从8080开始寻找可用端口
- **服务器端**：固定使用5000端口
- **优势**：避免端口冲突，支持多实例运行，无需手动配置

### 端口分配规则
1. 从8080开始依次尝试（8080, 8081, 8082, ...）
2. 找到可用端口立即使用
3. 最多尝试50个端口
4. 如都被占用，使用系统随机分配

---
© 2024 宝武钢铁集团
