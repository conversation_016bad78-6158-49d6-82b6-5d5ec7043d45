"""
基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统后端服务
"""
from flask import Flask, request, jsonify, render_template, send_from_directory
from flask_cors import CORS
import pandas as pd
import numpy as np
import os
try:
    from autogluon.tabular import TabularPredictor
    AUTOGLUON_AVAILABLE = True
except ImportError:
    AUTOGLUON_AVAILABLE = False
    print("⚠️ AutoGluon未安装，将运行在演示模式下")

import json
from pathlib import Path
import logging
import traceback

try:
    from utils.feature_transformer import feature_transformer
    FEATURE_TRANSFORMER_AVAILABLE = True
except ImportError:
    FEATURE_TRANSFORMER_AVAILABLE = False
    print("⚠️ 特征变换器未找到，将使用基本数据处理")

try:
    from feature_statistics import FeatureStatistics
    FEATURE_STATISTICS_AVAILABLE = True
except ImportError:
    FEATURE_STATISTICS_AVAILABLE = False
    print("⚠️ 特征统计模块未找到，将使用基本模式")

try:
    from model_evaluator import ModelEvaluator
    MODEL_EVALUATOR_AVAILABLE = True
except ImportError:
    MODEL_EVALUATOR_AVAILABLE = False
    print("⚠️ 模型评估模块未找到，评估功能将不可用")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 初始化特征统计分析器
feature_analyzer = None
if FEATURE_STATISTICS_AVAILABLE:
    try:
        feature_analyzer = FeatureStatistics("../final.csv")
        feature_analyzer.load_statistics("feature_statistics.json")
        print("✅ 特征统计分析器已加载")
    except Exception as e:
        print(f"⚠️ 特征统计分析器加载失败: {e}")
        feature_analyzer = None

# 初始化模型评估器
model_evaluator = None
if MODEL_EVALUATOR_AVAILABLE:
    try:
        model_evaluator = ModelEvaluator()
        print("✅ 模型评估器已加载")
    except Exception as e:
        print(f"⚠️ 模型评估器加载失败: {e}")
        model_evaluator = None

# 配置Flask应用，设置模板和静态文件路径
app = Flask(__name__,
            template_folder='templates',
            static_folder='static')

CORS(app, resources={
    r"/*": {
        "origins": ["http://localhost:3000", "http://127.0.0.1:3000",
                   "http://localhost:8080", "http://127.0.0.1:8080",
                   "file://", "*"],
        "methods": ["GET", "POST", "OPTIONS", "PUT", "DELETE"],
        "allow_headers": ["Content-Type", "Authorization", "Accept"],
        "supports_credentials": False
    }
})

MODEL_BASE_PATH = ["../models/stage_1", "../models/stage_2","../models/stage_3_A","../models/stage_3_D","../models/stage_4"]
Des_of_models = {
    "../models/stage_1": "硬度相关预测模型",
    "../models/stage_2": "拉伸屈服预测模型",
    "../models/stage_3_A": "A法——抗硫预测模型",
    "../models/stage_3_D": "D法——抗硫预测模型",
    "../models/stage_4": "硬度和拉伸预测抗硫模型"
}

AVAILABLE_MODELS = {}

def scan_model_directory(model_stage_path):
    """自动扫描每个 stage 下的模型目录，并提取模型名、路径和目标"""
    result = {}

    if not AUTOGLUON_AVAILABLE:
        # 演示模式：创建虚拟模型
        demo_models = {
            "../models/stage_1": ["硬度预测模型"],
            "../models/stage_2": ["拉伸强度预测模型"],
            "../models/stage_3_A": ["A法抗硫预测模型"],
            "../models/stage_3_D": ["D法抗硫预测模型"],
            "../models/stage_4": ["综合质量预测模型"]
        }

        if model_stage_path in demo_models:
            for model_name in demo_models[model_stage_path]:
                result[model_name] = {
                    "path": f"{model_stage_path}/{model_name}_demo",
                    "target": model_name.replace("预测模型", ""),
                    "desc": f"{Des_of_models[model_stage_path]} - 预测 [{model_name.replace('预测模型', '')}] (演示模式)"
                }
        return result

    path = Path(model_stage_path)
    if not path.exists():
        logger.warning(f"模型路径不存在: {model_stage_path}")
        return result

    for model_dir in path.iterdir():
        if model_dir.is_dir() and model_dir.name.endswith("_model"):
            model_name = model_dir.name.replace("_model", "")
            result[model_name] = {
                "path": str(model_dir),
                "target": model_name,
                "desc": f"{Des_of_models[model_stage_path]} - 预测 [{model_name}]"
            }
    return result

# 填充每个阶段的模型信息
for stage_path in MODEL_BASE_PATH:
    AVAILABLE_MODELS[stage_path] = scan_model_directory(stage_path)

print("发现的模型:")
print(AVAILABLE_MODELS)

# 缓存加载的模型
loaded_models = {}

class SpecialModelHandler:
    """处理特殊模型的类，如抗硫_最小承载时间模型"""

    @staticmethod
    def is_special_model(model_name):
        """判断是否为特殊模型"""
        return model_name == "抗硫_最小承载时间"

    @staticmethod
    def load_special_model(stage, model_name, model_info):
        """加载特殊模型"""
        if model_name == "抗硫_最小承载时间":
            model_path = model_info['path']
            class_model_path = model_path + "/抗硫_最小承载时间_model_class"
            reg_model_path = model_path + "/抗硫_最小承载时间_model_reg"

            return {
                'class': TabularPredictor.load(class_model_path),
                'reg': TabularPredictor.load(reg_model_path)
            }
        return None

    @staticmethod
    def predict_special_model(models, prediction_data):
        """使用特殊模型进行预测"""
        classifier = models['class']
        regressor = models['reg']

        # 阶段1：分类预测
        class_predictions = classifier.predict(prediction_data)

        # 筛选需要回归预测的样本（分类结果为0）
        need_regression_mask = (class_predictions == 0)
        regression_data = prediction_data[need_regression_mask]

        # 阶段2：回归预测
        final_predictions = class_predictions.copy().astype(float)
        if len(regression_data) > 0:
            reg_predictions = regressor.predict(regression_data)
            reg_predictions = np.power(10, reg_predictions)
            final_predictions[need_regression_mask] = reg_predictions

        # 将分类预测为1的样本赋值为720
        final_predictions[class_predictions == 1] = 720.0

        return final_predictions

def clean_dataframe(df):
    """清理数据框中的空字符串和其他无效值"""
    na_values = ['', 'null', 'NULL', 'None', 'NONE', 'none', 'NaN', 'nan', 'N/A', 'n/a', '#N/A']
    df_clean = df.copy()

    for col in df_clean.columns:
        df_clean[col] = df_clean[col].replace(na_values, np.nan)
        try:
            df_clean[col] = pd.to_numeric(df_clean[col], errors='ignore')
        except:
            pass

    return df_clean

def preprocess_prediction_data(df, model_stage=None):
    """预处理预测数据，包括高偏度特征变换"""
    try:
        df_cleaned = clean_dataframe(df)
        if FEATURE_TRANSFORMER_AVAILABLE:
            return feature_transformer.transform_dataset(df_cleaned, auto_detect=True)
        else:
            return df_cleaned, {}
    except Exception as e:
        logger.error(f"特征变换失败: {str(e)}")
        return clean_dataframe(df), {}

def handle_errors(f):
    """API错误处理装饰器"""
    def wrapper(*args, **kwargs):
        try:
            return f(*args, **kwargs)
        except Exception as e:
            logger.error(f"API错误 in {f.__name__}: {str(e)}")
            logger.error(f"完整错误信息: {traceback.format_exc()}")
            return jsonify({
                'success': False,
                'error': str(e),
                'type': type(e).__name__
            }), 500
    wrapper.__name__ = f.__name__
    return wrapper

@app.route('/api/models', methods=['GET', 'OPTIONS'])
@handle_errors
def get_available_models():
    """获取所有阶段下的模型列表"""
    if request.method == 'OPTIONS':
        return '', 200
        
    response = []
    
    for stage, models in AVAILABLE_MODELS.items():
        stage_desc = Des_of_models.get(stage, "未知阶段")
        stage_models = []
        for model_name, config in models.items():
            exists = os.path.exists(config['path'])
            stage_models.append({
                'name': model_name,
                'path': config['path'],
                'target': config['target'],
                'stage': stage_desc,
                'available': exists
            })
        response.append({
            'stage': stage,
            'stage_desc': stage_desc,
            'models': stage_models
        })
    
    logger.info(f"返回模型列表: {len(response)} 个阶段")
    return jsonify(response)

def get_model_required_features(stage, model_name):
    """从缓存或磁盘加载模型后获取该模型需要的特征列表"""
    model_info = AVAILABLE_MODELS[f"../models/{stage}"].get(model_name)
    if not model_info:
        raise FileNotFoundError(f"模型 [{model_name}] 不存在于阶段 [{stage}]")

    if not AUTOGLUON_AVAILABLE:
        # 演示模式：返回示例特征列表
        demo_features = [
            "C", "Si", "Mn", "P", "S", "Cr", "Ni", "Mo", "Cu", "Al",
            "温度", "时间", "压力", "冷却速度", "厚度", "直径"
        ]
        return demo_features

    if stage in loaded_models and model_name in loaded_models[stage]:
        predictor = loaded_models[stage][model_name]
        if SpecialModelHandler.is_special_model(model_name):
            return predictor['class'].feature_metadata.get_features()
        return predictor.feature_metadata.get_features()

    # 临时加载模型获取特征元数据
    if SpecialModelHandler.is_special_model(model_name):
        predictor = SpecialModelHandler.load_special_model(stage, model_name, model_info)
        features = predictor['class'].feature_metadata.get_features()
    else:
        predictor = TabularPredictor.load(model_info['path'])
        features = predictor.feature_metadata.get_features()

    # 缓存模型
    if stage not in loaded_models:
        loaded_models[stage] = {}
    loaded_models[stage][model_name] = predictor
    return features

@app.route('/api/validate_data', methods=['POST', 'OPTIONS'])
@handle_errors
def validate_data():
    """验证上传的数据是否匹配模型输入要求"""
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        data = request.get_json()
        logger.info(f"收到验证请求: {type(data)}")

        if not data or 'stage' not in data or 'model_name' not in data or 'data' not in data:
            return jsonify({
                'valid': False,
                'error': "缺少以下必要参数：stage、model_name、data"
            }), 400

        stage = data['stage']
        model_name = data['model_name']
        raw_data = data['data']
        
        logger.info(f"验证参数: stage={stage}, model={model_name}, data_rows={len(raw_data)}")

        # 将数据转换为DataFrame并清理
        try:
            raw_df = pd.DataFrame(raw_data)
            raw_df = clean_dataframe(raw_df)  # 清理空字符串
            logger.info(f"数据转换成功: {raw_df.shape}")
        except Exception as e:
            return jsonify({
                'valid': False,
                'error': f"数据格式转换失败: {str(e)}"
            }), 400

        # 验证模型是否存在 - 修复路径格式不匹配问题
        model_stage_key = f"../models/{stage}"
        if model_stage_key not in AVAILABLE_MODELS:
            return jsonify({
                'valid': False,
                'error': f"无效的阶段名: {stage}"
            }), 400

        if model_name not in AVAILABLE_MODELS[model_stage_key]:
            return jsonify({
                'valid': False,
                'error': f"阶段 [{stage}] 中不存在模型 [{model_name}]"
            }), 400

        model_info = AVAILABLE_MODELS[model_stage_key][model_name]

        # 获取模型所需特征
        try:
            logger.info(f"调试信息 - stage参数: '{stage}', model_name: '{model_name}'")
            logger.info(f"调试信息 - AVAILABLE_MODELS keys: {list(AVAILABLE_MODELS.keys())}")
            required_features = get_model_required_features(stage, model_name)
            logger.info(f"模型需要特征: {required_features}")
        except Exception as e:
            logger.error(f"获取模型特征失败: {str(e)}")
            return jsonify({
                'valid': False,
                'error': f"无法提取模型所需的特征列: {str(e)}"
            }), 400

        # 检查缺失特征
        missing = [f for f in required_features if f not in raw_df.columns]
        if missing:
            return jsonify({
                'valid': False,
                'error': f"输入数据缺少以下必需特征: {', '.join(missing)}"
            }), 400

        # 检查数值类型（在清理后的数据上检查）
        invalid_types = []
        for feature in required_features:
            try:
                # 尝试转换为数值，忽略NaN
                pd.to_numeric(raw_df[feature], errors='coerce')
                # 检查是否有非数值且非NaN的值
                non_numeric = raw_df[feature].apply(lambda x: 
                    not pd.isna(x) and not isinstance(x, (int, float)) and 
                    (isinstance(x, str) and not x.replace('.', '', 1).replace('-', '', 1).isdigit())
                )
                if non_numeric.any():
                    invalid_types.append(feature)
            except Exception as e:
                logger.warning(f"特征 {feature} 类型检查失败: {str(e)}")
                invalid_types.append(feature)

        if invalid_types:
            return jsonify({
                'valid': False,
                'error': f"以下特征包含无法转换为数值的内容: {', '.join(invalid_types)}"
            }), 400

        return jsonify({
            'valid': True,
            'message': '输入数据格式验证通过',
            'features_count': int(len(required_features)),
            'rows': int(len(raw_df)),
            'model_target': model_info['target'],
            'used_features': required_features
        })

    except Exception as e:
        logger.error(f"验证过程异常: {str(e)}")
        return jsonify({
            'valid': False, 
            'error': f"数据验证失败 - 系统错误: {str(e)}"
        }), 500

def safe_float_convert(value):
    """安全地将值转换为可JSON序列化的格式"""
    # 处理 pandas 的 NA 值
    if pd.isna(value):
        return None
    
    # 处理 numpy 数值类型
    if isinstance(value, np.floating):
        if np.isnan(value) or np.isinf(value):
            return None
        return float(value)
    elif isinstance(value, np.integer):
        return int(value)
    elif isinstance(value, (np.int64, np.int32, np.int16, np.int8)):
        return int(value)
    elif isinstance(value, (np.float64, np.float32, np.float16)):
        if np.isnan(value) or np.isinf(value):
            return None
        return float(value)
    elif isinstance(value, np.bool_):
        return bool(value)
    elif isinstance(value, np.ndarray):
        return value.tolist()
    
    # 处理原生 Python 类型
    elif isinstance(value, float):
        if np.isnan(value) or np.isinf(value):
            return None
        return value
    elif isinstance(value, (int, bool, str)):
        return value
    
    # 处理其他可能的类型
    elif hasattr(value, 'item'):  # numpy scalar
        return value.item()
    
    return value

def clean_prediction_results(results):
    """清理预测结果中的特殊值"""
    cleaned_results = []
    for result in results:
        cleaned_result = {}
        for key, value in result.items():
            if key == 'pred':
                cleaned_result[key] = safe_float_convert(value)
            elif key == '原始':
                cleaned_original = {}
                for orig_key, orig_value in value.items():
                    cleaned_original[orig_key] = safe_float_convert(orig_value)
                cleaned_result[key] = cleaned_original
            elif isinstance(value, dict):
                # 递归处理嵌套字典
                cleaned_dict = {}
                for sub_key, sub_value in value.items():
                    cleaned_dict[sub_key] = safe_float_convert(sub_value)
                cleaned_result[key] = cleaned_dict
            else:
                cleaned_result[key] = safe_float_convert(value)
        cleaned_results.append(cleaned_result)
    return cleaned_results

@app.route('/api/predict', methods=['POST', 'OPTIONS'])
@handle_errors
def predict():
    """执行预测"""
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        data = request.get_json()
        if not data or 'stage' not in data or 'model_name' not in data or 'data' not in data:
            return jsonify({'success': False, 'error': "参数缺失: stage、model_name 或 data"}), 400

        stage = data['stage']
        model_name = data['model_name']
        inputs = data['data']

        # 获取预测模式（默认为宽松模式）
        prediction_mode = data.get('mode', 'lenient')  # 'strict', 'lenient', 或 'evaluation'

        # 评估模式需要目标值列
        target_column = None
        if prediction_mode == 'evaluation':
            target_column = data.get('target_column')
            if not target_column:
                return jsonify({
                    'success': False,
                    'error': '评估模式需要指定目标值列名 (target_column)',
                    'mode': 'evaluation'
                }), 400

        logger.info(f"预测请求: stage={stage}, model={model_name}, samples={len(inputs)}, mode={prediction_mode}")

        # 转换为DataFrame并立即清理数据
        df = pd.DataFrame(inputs)
        df = clean_dataframe(df)  # 清理空字符串和无效值

        logger.info(f"数据清理完成，形状: {df.shape}")

        # 验证模型路径 - 修复路径格式不匹配问题
        model_dir = f"../models/{stage}"
        if model_dir not in AVAILABLE_MODELS:
            return jsonify({'success': False, 'error': f"未定义的阶段: {stage}"}), 400

        if model_name not in AVAILABLE_MODELS[model_dir]:
            return jsonify({'success': False, 'error': f"未定义的模型: {model_name}"}), 400

        model_info = AVAILABLE_MODELS[model_dir][model_name]
        model_path = model_info['path']

        if not os.path.exists(model_path):
            return jsonify({'success': False, 'error': f"模型路径不存在: {model_path}"}), 404

        # 应用高偏度特征预处理（内部已包含数据清理）
        try:
            df_processed, transform_record = preprocess_prediction_data(df, stage)
        except Exception as e:
            logger.warning(f"特征预处理失败，使用清理后的原始数据: {str(e)}")
            df_processed, transform_record = df, {}

        # 获取所需特征
        logger.info(f"预测调试信息 - stage参数: '{stage}', model_name: '{model_name}'")
        required_features = get_model_required_features(stage, model_name)

        # 评估模式：提取目标值
        true_values = None
        if prediction_mode == 'evaluation':
            if target_column not in df_processed.columns:
                return jsonify({
                    'success': False,
                    'error': f"评估模式下未找到目标值列: {target_column}",
                    'mode': 'evaluation'
                }), 400

            true_values = df_processed[target_column].values
            logger.info(f"评估模式：提取目标值 {len(true_values)} 个")

        # 检查特征完整性
        missing_features = [f for f in required_features if f not in df_processed.columns]
        fill_report = []

        if prediction_mode == 'strict':
            # 严格模式：缺失特征直接报错
            if missing_features:
                return jsonify({
                    'success': False,
                    'error': f"严格模式下缺少必需特征: {', '.join(missing_features)}",
                    'mode': 'strict',
                    'missing_features': missing_features
                }), 400
        elif prediction_mode == 'evaluation':
            # 评估模式：可以使用宽松填充，但会在报告中标注
            if missing_features and feature_analyzer:
                logger.info(f"评估模式：尝试填充 {len(missing_features)} 个缺失特征")
                try:
                    df_processed, fill_report = feature_analyzer.fill_missing_features(
                        df_processed, stage, model_name
                    )
                    logger.info(f"特征填充完成，填充了 {len(fill_report)} 个特征")
                except Exception as e:
                    logger.error(f"特征填充失败: {e}")
                    return jsonify({
                        'success': False,
                        'error': f"评估模式特征填充失败: {str(e)}",
                        'mode': 'evaluation'
                    }), 400
            elif missing_features:
                return jsonify({
                    'success': False,
                    'error': f"评估模式下缺少必需特征: {', '.join(missing_features)}",
                    'mode': 'evaluation',
                    'missing_features': missing_features
                }), 400
        else:
            # 宽松模式：尝试填充缺失特征
            if missing_features and feature_analyzer:
                logger.info(f"宽松模式：尝试填充 {len(missing_features)} 个缺失特征")
                try:
                    df_processed, fill_report = feature_analyzer.fill_missing_features(
                        df_processed, stage, model_name
                    )
                    logger.info(f"特征填充完成，填充了 {len(fill_report)} 个特征")
                except Exception as e:
                    logger.error(f"特征填充失败: {e}")
                    return jsonify({
                        'success': False,
                        'error': f"特征填充失败: {str(e)}",
                        'mode': 'lenient'
                    }), 400
            elif missing_features:
                # 没有特征分析器时的处理
                return jsonify({
                    'success': False,
                    'error': f"缺少必需特征且无法自动填充: {', '.join(missing_features)}",
                    'mode': 'lenient',
                    'missing_features': missing_features
                }), 400
            
        # 选择所需特征并处理缺失值
        prediction_data = df_processed[required_features].copy()
        
        # 记录缺失值情况
        missing_info = {}
        for col in prediction_data.columns:
            missing_count = prediction_data[col].isna().sum()
            if missing_count > 0:
                missing_info[col] = int(missing_count)  # 转换为 Python int
                logger.warning(f"特征 {col} 包含 {missing_count} 个缺失值")
        
        # 删除包含缺失值的行
        prediction_data = prediction_data.dropna()

        if len(prediction_data) == 0:
            return jsonify({
                'success': False, 
                'error': "处理后的数据为空，所有行都包含缺失值",
                'missing_info': missing_info
            }), 400
        
        logger.info(f"预测数据准备完成，有效行数: {len(prediction_data)}/{len(df)}")

        # 加载和使用模型
        if not AUTOGLUON_AVAILABLE:
            # 演示模式：生成模拟预测结果
            predictions = []
            for _ in range(len(prediction_data)):
                if "硬度" in model_name:
                    pred = np.random.normal(250, 30)  # 硬度预测值
                elif "拉伸" in model_name:
                    pred = np.random.normal(450, 50)  # 拉伸强度预测值
                elif "抗硫" in model_name:
                    pred = np.random.normal(500, 100)  # 抗硫时间预测值
                else:
                    pred = np.random.normal(300, 50)  # 默认预测值
                predictions.append(max(0, pred))  # 确保预测值为正
            predictions = np.array(predictions)
        else:
            if stage not in loaded_models:
                loaded_models[stage] = {}

            if model_name not in loaded_models[stage]:
                if SpecialModelHandler.is_special_model(model_name):
                    loaded_models[stage][model_name] = SpecialModelHandler.load_special_model(stage, model_name, model_info)
                else:
                    loaded_models[stage][model_name] = TabularPredictor.load(model_path)

            # 执行预测
            if SpecialModelHandler.is_special_model(model_name):
                predictions = SpecialModelHandler.predict_special_model(loaded_models[stage][model_name], prediction_data)
            else:
                predictor = loaded_models[stage][model_name]
                predictions = predictor.predict(prediction_data)

        # 确保predictions是numpy数组或列表
        if hasattr(predictions, 'values'):
            predictions = predictions.values
        predictions = np.array(predictions)

        # 构造输出（只包含成功预测的行）
        output = []
        valid_indices = df.index[~df[required_features].isna().any(axis=1)]
        for i, (idx, pred) in enumerate(zip(valid_indices, predictions)):
            result = {
                'pred': safe_float_convert(pred),
                '原始': df.iloc[idx].to_dict(),
                '特征变换记录': transform_record
            }
            output.append(result)

        # 计算统计量
        valid_predictions = predictions[~np.isnan(predictions)] if len(predictions) > 0 else []
        
        statistics = {
            '数量': len(valid_predictions),
            '均值': safe_float_convert(np.mean(valid_predictions)) if len(valid_predictions) > 0 else None,
            '最小值': safe_float_convert(np.min(valid_predictions)) if len(valid_predictions) > 0 else None,
            '最大值': safe_float_convert(np.max(valid_predictions)) if len(valid_predictions) > 0 else None,
            '标准差': safe_float_convert(np.std(valid_predictions)) if len(valid_predictions) > 0 else None
        }

        # 清理输出数据
        cleaned_output = clean_prediction_results(output)

        response_data = {
            'success': True,
            'results': cleaned_output,
            'statistics': statistics,
            'stage': stage,
            'model': model_name,
            'target': model_info['target'],
            'processed_rows': int(len(prediction_data)),
            'total_rows': int(len(df)),
            'missing_info': missing_info if missing_info else None,
            'prediction_mode': prediction_mode,
            'fill_report': fill_report if fill_report else None
        }

        # 评估模式：添加评估报告
        if prediction_mode == 'evaluation' and true_values is not None and model_evaluator:
            try:
                # 获取有效的预测值和真实值
                valid_mask = ~np.isnan(predictions)
                if len(true_values) == len(predictions):
                    valid_true = true_values[valid_mask]
                    valid_pred = predictions[valid_mask]

                    # 计算评估指标
                    evaluation_metrics = model_evaluator.evaluate_predictions(
                        valid_pred, valid_true, model_info['target']
                    )

                    # 创建评估摘要
                    evaluation_summary = model_evaluator.create_evaluation_summary(evaluation_metrics)

                    response_data['evaluation'] = {
                        'metrics': evaluation_metrics,
                        'summary': evaluation_summary,
                        'target_column': target_column,
                        'evaluation_samples': len(valid_pred)
                    }

                    logger.info(f"评估完成: R²={evaluation_metrics.get('r2', 'N/A'):.4f}")
                else:
                    response_data['evaluation'] = {
                        'error': '预测值和真实值数量不匹配',
                        'pred_count': len(predictions),
                        'true_count': len(true_values)
                    }
            except Exception as e:
                logger.error(f"评估失败: {e}")
                response_data['evaluation'] = {
                    'error': f'评估失败: {str(e)}'
                }
        elif prediction_mode == 'evaluation':
            response_data['evaluation'] = {
                'error': '评估器不可用或缺少真实值'
            }

        logger.info(f"预测完成: {len(cleaned_output)} 个结果")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"预测失败: {str(e)}")
        logger.error(f"完整错误: {traceback.format_exc()}")
        return jsonify({'success': False, 'error': f"预测失败: {str(e)}"}), 500

@app.route('/api/model_info/<stage>/<model_name>', methods=['GET', 'OPTIONS'])
@handle_errors
def get_model_info(stage, model_name):
    """获取模型详细信息"""
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        model_dir = f"../models/{stage}"
        if model_dir not in AVAILABLE_MODELS:
            return jsonify({'success': False, 'error': "无效的阶段名"}), 404

        model_list = AVAILABLE_MODELS[model_dir]
        if model_name not in model_list:
            return jsonify({'success': False, 'error': "模型不存在"}), 404

        model_info = model_list[model_name]
        model_path = model_info['path']

        # 加载模型获取特征信息
        if stage not in loaded_models:
            loaded_models[stage] = {}

        if model_name not in loaded_models[stage]:
            if SpecialModelHandler.is_special_model(model_name):
                loaded_models[stage][model_name] = SpecialModelHandler.load_special_model(stage, model_name, model_info)
                # 对于特殊模型，使用分类器获取特征信息
                predictor = loaded_models[stage][model_name]['class']
            else:
                predictor = TabularPredictor.load(model_path)
                loaded_models[stage][model_name] = predictor
        else:
            if SpecialModelHandler.is_special_model(model_name):
                predictor = loaded_models[stage][model_name]['class']
            else:
                predictor = loaded_models[stage][model_name]

        feature_list = predictor.feature_metadata.get_features()

        return jsonify({
            'success': True,
            'model_name': model_name,
            'stage': stage,
            'target': model_info['target'],
            'desc': model_info.get('desc'),
            'num_features': int(len(feature_list)),
            'features': feature_list,
            'path': model_path,
            'is_special_model': SpecialModelHandler.is_special_model(model_name)
        })

    except Exception as e:
        logger.error(f"获取模型信息失败: {str(e)}")
        return jsonify({'success': False, 'error': f"获取模型信息失败: {str(e)}"}), 500

@app.route('/api/skew_features', methods=['GET', 'OPTIONS'])
@handle_errors
def get_skew_features():
    """获取高偏度特征配置信息"""
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        return jsonify({
            'success': True,
            'high_skew_features': feature_transformer.high_skew_features,
            'description': '系统预定义的高偏度特征及其变换配置',
            'transform_summary': feature_transformer.get_transform_summary()
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'high_skew_features': {},
            'description': '特征变换模块不可用',
            'error': str(e)
        })

@app.route('/api/analyze_skew', methods=['POST', 'OPTIONS'])
@handle_errors
def analyze_data_skew():
    """分析上传数据的偏度情况"""
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        data = request.get_json()
        if not data or 'data' not in data:
            return jsonify({'success': False, 'error': "缺少数据参数"}), 400

        df = pd.DataFrame(data['data'])
        df = clean_dataframe(df)  # 清理数据

        # 检测高偏度特征
        try:
            skew_features = feature_transformer.detect_skew_features(df, skew_threshold=2.0)
            # 应用变换并比较
            _, transform_record = preprocess_prediction_data(df)
        except:
            skew_features = {}
            transform_record = {}

        return jsonify({
            'success': True,
            'original_skew_features': skew_features,
            'applied_transforms': transform_record,
            'recommendations': {
                feature: config.get('recommended_transform', 'log')
                for feature, config in skew_features.items()
            }
        })

    except Exception as e:
        logger.error(f"分析数据偏度失败: {str(e)}")
        return jsonify({'success': False, 'error': f"分析数据偏度失败: {str(e)}"}), 500

@app.route('/health', methods=['GET', 'OPTIONS'])
def health_check():
    """健康检查"""
    if request.method == 'OPTIONS':
        return '', 200
        
    return jsonify({
        'success': True,
        'status': 'healthy',
        'message': '后端服务运行正常',
        'models_loaded': int(len(loaded_models)),
        'available_models': int(sum(len(models) for models in AVAILABLE_MODELS.values()))
    })

# 主页路由 - 返回前端页面
@app.route('/')
def index():
    """返回主页面"""
    return render_template('index.html')

# 静态文件路由
@app.route('/static/<path:filename>')
def static_files(filename):
    """提供静态文件服务"""
    return send_from_directory(app.static_folder, filename)

# 测试数据下载路由
@app.route('/api/test_data/<stage>')
@handle_errors
def get_test_data(stage):
    """提供测试数据下载"""
    test_data_path = f"../test_data/test_{stage}.csv"
    if os.path.exists(test_data_path):
        return send_from_directory('../test_data', f'test_{stage}.csv', as_attachment=True)
    else:
        return jsonify({'success': False, 'error': '测试数据文件不存在'}), 404

# 获取可用测试数据列表
@app.route('/api/test_data_list', methods=['GET', 'OPTIONS'])
@handle_errors
def get_test_data_list():
    """获取可用的测试数据列表"""
    if request.method == 'OPTIONS':
        return '', 200

    test_data_dir = Path('../test_data')
    test_files = []

    if test_data_dir.exists():
        for file_path in test_data_dir.glob('test_*.csv'):
            stage = file_path.stem.replace('test_', '')
            stage_desc = Des_of_models.get(f"../models/{stage}", f"阶段 {stage}")
            test_files.append({
                'stage': stage,
                'filename': file_path.name,
                'description': stage_desc,
                'download_url': f'/api/test_data/{stage}'
            })

    return jsonify({
        'success': True,
        'test_files': test_files,
        'message': f'找到 {len(test_files)} 个测试数据文件'
    })

# 预测模式API
@app.route('/api/modes', methods=['GET', 'OPTIONS'])
@handle_errors
def get_prediction_modes():
    """获取预测模式信息"""
    if request.method == 'OPTIONS':
        return '', 200

    return jsonify({
        'success': True,
        'modes': {
            'strict': {
                'name': '严格模式',
                'description': '要求输入数据包含模型所需的所有特征，缺失特征将报错',
                'features': [
                    '数据必须包含所有必需特征',
                    '缺失特征时立即报错',
                    '适用于标准化数据输入',
                    '确保预测结果的准确性'
                ]
            },
            'lenient': {
                'name': '宽松模式',
                'description': '允许输入数据缺失部分特征，系统将基于final.csv统计数据自动填充',
                'features': [
                    '自动填充缺失特征',
                    '基于final.csv统计数据填充',
                    '支持不完整的数据输入',
                    '提供填充报告和统计信息'
                ]
            },
            'evaluation': {
                'name': '评估模式',
                'description': '对含有目标值的数据进行模型效果评估，计算各种评估指标',
                'features': [
                    '要求数据包含目标值列',
                    '计算R²、RMSE、MAE、MAPE等指标',
                    '提供详细的评估报告',
                    '支持模型性能等级评估'
                ]
            }
        },
        'default_mode': 'lenient',
        'feature_statistics_available': feature_analyzer is not None,
        'model_evaluator_available': model_evaluator is not None,
        'final_csv_loaded': feature_analyzer.data is not None if feature_analyzer else False
    })

# 引导功能API
@app.route('/api/guide', methods=['GET', 'OPTIONS'])
@handle_errors
def get_guide_info():
    """获取系统引导信息"""
    if request.method == 'OPTIONS':
        return '', 200

    guide_steps = [
        {
            'step': 1,
            'title': '选择预测模型',
            'description': '根据您的预测需求选择合适的机器学习模型',
            'details': [
                '硬度相关预测模型：用于预测材料硬度值',
                '拉伸屈服预测模型：用于预测材料拉伸强度',
                'A法抗硫预测模型：用于预测抗硫化氢腐蚀性能',
                'D法抗硫预测模型：用于预测抗硫化氢腐蚀性能（D法标准）',
                '硬度和拉伸预测抗硫模型：综合预测模型'
            ],
            'tips': '选择与您的测试目标最匹配的模型类型'
        },
        {
            'step': 2,
            'title': '准备数据文件',
            'description': '上传包含工艺参数的CSV或Excel文件',
            'details': [
                '支持的文件格式：CSV (.csv)、Excel (.xlsx, .xls)',
                '文件大小限制：最大50MB',
                '数据要求：包含模型所需的特征列',
                '数据质量：确保数值数据格式正确，避免空值过多',
                '测试数据：系统提供了各阶段的测试数据文件供下载使用'
            ],
            'tips': '建议先下载测试数据文件进行试用，熟悉系统功能后再使用自己的数据',
            'test_data_available': True
        },
        {
            'step': 3,
            'title': '数据验证与预览',
            'description': '系统会自动验证数据与模型的兼容性',
            'details': [
                '特征匹配检查：验证数据列是否包含模型所需特征',
                '数据类型检查：确保数值特征格式正确',
                '缺失值处理：系统会自动处理部分缺失值',
                '数据预览：查看前10行数据确认格式正确'
            ],
            'tips': '如果验证失败，请检查数据格式和特征名称'
        },
        {
            'step': 4,
            'title': '执行预测分析',
            'description': '使用训练好的机器学习模型进行预测',
            'details': [
                '自动特征预处理：系统会对高偏度特征进行变换',
                '批量预测：支持同时预测多个样本',
                '结果统计：提供预测结果的统计分析',
                '可视化展示：生成预测结果分布图表'
            ],
            'tips': '预测过程可能需要几分钟，请耐心等待'
        },
        {
            'step': 5,
            'title': '结果导出与报告',
            'description': '下载预测结果和生成专业分析报告',
            'details': [
                'Excel导出：包含原始数据和预测结果的完整表格',
                'CSV导出：便于进一步数据处理的格式',
                'PDF报告：包含统计分析、图表和专业建议的完整报告',
                '质量评估：基于预测结果的质量等级判断'
            ],
            'tips': '建议保存完整的分析报告用于质量控制记录'
        }
    ]

    return jsonify({
        'success': True,
        'guide_steps': guide_steps,
        'system_info': {
            'name': '基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统',
            'version': '1.0',
            'developer': '宝武钢铁集团',
            'description': '专业的油套管质量预测平台，基于先进的机器学习算法'
        }
    })

# 添加全局错误处理
@app.errorhandler(404)
def not_found(error):
    # 如果是API请求，返回JSON错误
    if request.path.startswith('/api/'):
        return jsonify({'success': False, 'error': '接口不存在'}), 404
    # 否则返回主页
    return render_template('index.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'success': False, 'error': '服务器内部错误'}), 500

if __name__ == '__main__':
    print("🚀 启动AutoGluon预测服务...")
    print(f"📁 模型搜索路径: {MODEL_BASE_PATH}")
    
    # 检查模型可用性
    total_available = 0
    total_models = 0
    
    for stage_path, models in AVAILABLE_MODELS.items():
        stage_available = 0
        stage_total = len(models)
        total_models += stage_total
        
        print(f"\n📋 {Des_of_models.get(stage_path, stage_path)}:")
        for model_name, config in models.items():
            if os.path.exists(config['path']):
                print(f"  ✅ {model_name} - {config['target']}")
                stage_available += 1
                total_available += 1
            else:
                print(f"  ❌ {model_name} - 路径不存在: {config['path']}")
        
        print(f"  📊 本阶段: {stage_available}/{stage_total} 个模型可用")
    
    print(f"\n🎯 总计: {total_available}/{total_models} 个模型可用")
    
    if total_available == 0:
        print("⚠️  警告: 没有找到可用的模型文件!")
    else:
        print("✅ 服务准备就绪!")
    
    print(f"\n🌐 启动地址: http://localhost:5000")
    print("🔧 CORS已配置，支持跨域访问")
    
    # 使用更详细的配置启动服务
    try:
        app.run(
            debug=False,  # 关闭调试模式避免问题
            host='127.0.0.1',  # 使用localhost
            port=5000,
            threaded=True,  # 支持多线程处理
            use_reloader=False  # 关闭自动重载
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()