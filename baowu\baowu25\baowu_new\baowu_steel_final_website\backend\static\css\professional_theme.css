/*
基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统 - 专业科研主题
Professional Research & Engineering Interface Theme
*/

:root {
    /* 专业配色方案 - 深蓝科技风 */
    --primary-color: #1e3a8a;          /* 深蓝色 */
    --secondary-color: #3b82f6;        /* 蓝色 */
    --accent-color: #06b6d4;           /* 青色 */
    --success-color: #059669;          /* 绿色 */
    --warning-color: #d97706;          /* 橙色 */
    --error-color: #dc2626;            /* 红色 */
    --info-color: #0284c7;             /* 信息蓝 */
    
    /* 中性色调 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: var(--gray-50);
    --bg-tertiary: var(--gray-100);
    --bg-dark: var(--gray-900);
    
    /* 文字颜色 */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-500);
    --text-inverse: #ffffff;
    
    /* 边框和阴影 */
    --border-color: var(--gray-200);
    --border-radius: 8px;
    --border-radius-lg: 12px;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 字体 */
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', monospace;
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    
    /* 过渡动画 */
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 主容器 */
.container {
    max-width: 1600px;
    margin: 20px auto;
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

/* 专业头部设计 */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--text-inverse);
    padding: 24px 32px;
    border-bottom: 1px solid var(--border-color);
}

.system-logo {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 12px;
}

.logo-icon {
    font-size: 32px;
    color: var(--text-inverse);
    opacity: 0.9;
}

.system-info h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 4px;
    letter-spacing: -0.025em;
}

.system-subtitle {
    font-size: 14px;
    opacity: 0.8;
    font-weight: 400;
    margin-bottom: 12px;
}

.system-badges {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    backdrop-filter: blur(10px);
}

/* 状态指示器 */
.backend-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid transparent;
    transition: var(--transition);
    cursor: pointer;
}

.backend-status.online {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
    border-color: rgba(5, 150, 105, 0.2);
}

.backend-status.offline {
    background: rgba(220, 38, 38, 0.1);
    color: var(--error-color);
    border-color: rgba(220, 38, 38, 0.2);
}

.backend-status.demo {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
    border-color: rgba(217, 119, 6, 0.2);
}

/* 主要内容区域 */
.main-content {
    padding: 32px;
    background: var(--bg-primary);
}

/* 步骤容器 */
.step {
    margin-bottom: 32px;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    background: var(--bg-primary);
    overflow: hidden;
    transition: var(--transition);
}

.step:hover {
    box-shadow: var(--shadow-md);
}

.step-header {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px 24px;
    background: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    color: var(--text-inverse);
    border-radius: 50%;
    font-weight: 600;
    font-size: 14px;
    flex-shrink: 0;
}

.step-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.step-description {
    font-size: 14px;
    color: var(--text-secondary);
    margin: 4px 0 0 0;
}

.step-content {
    padding: 24px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 10px 16px;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    background: transparent;
    white-space: nowrap;
    user-select: none;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background: var(--secondary-color);
    border-color: var(--secondary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-secondary);
    border-color: var(--gray-300);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-inverse);
    border-color: var(--success-color);
}

.btn-success:hover:not(:disabled) {
    background: #047857;
    border-color: #047857;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
    border-radius: var(--border-radius-lg);
}

/* 信息框样式 */
.info-box, .success-box, .error-box, .warning-box {
    padding: 16px;
    border-radius: var(--border-radius);
    border: 1px solid;
    margin: 16px 0;
}

.info-box {
    background: rgba(2, 132, 199, 0.05);
    border-color: rgba(2, 132, 199, 0.2);
    color: var(--info-color);
}

.success-box {
    background: rgba(5, 150, 105, 0.05);
    border-color: rgba(5, 150, 105, 0.2);
    color: var(--success-color);
}

.error-box {
    background: rgba(220, 38, 38, 0.05);
    border-color: rgba(220, 38, 38, 0.2);
    color: var(--error-color);
}

.warning-box {
    background: rgba(217, 119, 6, 0.05);
    border-color: rgba(217, 119, 6, 0.2);
    color: var(--warning-color);
}

/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-primary);
    font-size: 13px;
}

.data-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.data-table tr:hover {
    background: var(--bg-secondary);
}

/* 代码样式 */
code {
    font-family: var(--font-mono);
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

/* 模型选择网格 */
.model-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin: 16px 0;
}

.model-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    background: var(--bg-primary);
    cursor: pointer;
    transition: var(--transition);
    position: relative;
}

.model-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.model-card.selected {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.05);
    box-shadow: var(--shadow-md);
}

.model-card.selected::before {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 8px;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.model-name {
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 14px;
}

.model-stage {
    font-size: 12px;
    color: var(--text-muted);
    background: var(--bg-tertiary);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
    margin-bottom: 8px;
}

.model-target {
    font-size: 13px;
    color: var(--text-secondary);
}

/* 预测模式选择 */
.mode-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
    margin: 16px 0;
}

.mode-option {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    background: var(--bg-primary);
    cursor: pointer;
    transition: var(--transition);
    text-align: center;
}

.mode-option:hover {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.02);
}

.mode-option.selected {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.05);
    color: var(--primary-color);
}

.mode-icon {
    font-size: 24px;
    margin-bottom: 8px;
    display: block;
}

.mode-name {
    font-weight: 600;
    margin-bottom: 4px;
    font-size: 14px;
}

.mode-description {
    font-size: 12px;
    color: var(--text-muted);
    line-height: 1.4;
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 32px;
    text-align: center;
    background: var(--bg-secondary);
    transition: var(--transition);
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.02);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.05);
}

.upload-icon {
    font-size: 48px;
    color: var(--text-muted);
    margin-bottom: 16px;
}

.upload-text {
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-weight: 500;
}

.upload-hint {
    font-size: 14px;
    color: var(--text-muted);
}

/* 数据预览表格 */
.data-preview {
    margin: 16px 0;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-primary);
}

.data-preview-header {
    background: var(--bg-secondary);
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
}

.data-preview-table {
    width: 100%;
    font-size: 12px;
    border-collapse: collapse;
}

.data-preview-table th {
    background: var(--bg-tertiary);
    padding: 8px 12px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-preview-table td {
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-family: var(--font-mono);
}

/* 统计信息卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin: 16px 0;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
    font-family: var(--font-mono);
}

.stat-label {
    font-size: 12px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* 加载状态 */
.loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--text-muted);
    font-size: 14px;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--bg-tertiary);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: var(--border-radius);
    }

    .header {
        padding: 16px 20px;
    }

    .main-content {
        padding: 20px;
    }

    .step-content {
        padding: 16px;
    }

    .system-info h1 {
        font-size: 20px;
    }

    .model-grid {
        grid-template-columns: 1fr;
    }

    .mode-selection {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 图表容器 */
.chart-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin: 16px 0;
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    text-align: center;
}

.chart-wrapper {
    position: relative;
    height: 400px;
    width: 100%;
}

/* 结果展示 */
.results-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    margin: 16px 0;
}

.results-header {
    background: var(--bg-secondary);
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.results-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.results-actions {
    display: flex;
    gap: 8px;
}

.results-content {
    padding: 20px;
}

/* 评估指标网格 */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin: 16px 0;
}

.metric-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    text-align: center;
}

.metric-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
    font-family: var(--font-mono);
}

.metric-label {
    font-size: 12px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 4px;
}

.metric-description {
    font-size: 11px;
    color: var(--text-secondary);
    line-height: 1.3;
}

/* 性能等级指示器 */
.performance-grade {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 14px;
}

.performance-grade.excellent {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(5, 150, 105, 0.2);
}

.performance-grade.good {
    background: rgba(2, 132, 199, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(2, 132, 199, 0.2);
}

.performance-grade.fair {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(217, 119, 6, 0.2);
}

.performance-grade.poor {
    background: rgba(220, 38, 38, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

/* 数据表格增强 */
.enhanced-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    font-size: 13px;
}

.enhanced-table thead {
    background: var(--bg-secondary);
}

.enhanced-table th {
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

.enhanced-table td {
    padding: 10px 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-family: var(--font-mono);
    font-size: 12px;
}

.enhanced-table tbody tr:hover {
    background: var(--bg-secondary);
}

.enhanced-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

/* 通知和提示 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 500;
    font-size: 14px;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--error-color);
}

.notification.warning {
    background: var(--warning-color);
}

.notification.info {
    background: var(--info-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    border: 1px solid var(--border-color);
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    background: var(--bg-secondary);
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-800);
    color: white;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 实用类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.font-mono { font-family: var(--font-mono); }
.font-bold { font-weight: 600; }
.text-sm { font-size: 12px; }
.text-lg { font-size: 16px; }
.mt-4 { margin-top: 16px; }
.mb-4 { margin-bottom: 16px; }
.p-4 { padding: 16px; }
