/*
基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统 - 现代科研主题
Modern Research & Engineering Interface Theme
*/

:root {
    /* 现代科研配色方案 - 优雅深色主题 */
    --primary-color: #2563eb;          /* 现代蓝 */
    --primary-dark: #1d4ed8;           /* 深蓝 */
    --primary-light: #3b82f6;          /* 浅蓝 */
    --secondary-color: #7c3aed;        /* 紫色 */
    --accent-color: #06b6d4;           /* 青色 */
    --success-color: #10b981;          /* 现代绿 */
    --warning-color: #f59e0b;          /* 琥珀色 */
    --error-color: #ef4444;            /* 现代红 */
    --info-color: #06b6d4;             /* 信息青 */

    /* 精致灰度系统 */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* 背景渐变 */
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --bg-gradient-light: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-card-hover: rgba(255, 255, 255, 0.98);

    /* 文字颜色 */
    --text-primary: var(--gray-900);
    --text-secondary: var(--gray-600);
    --text-muted: var(--gray-500);
    --text-inverse: #ffffff;

    /* 现代阴影系统 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* 现代边框系统 */
    --border-color: var(--gray-200);
    --border-color-light: var(--gray-100);
    --border-radius: 12px;
    --border-radius-lg: 16px;
    --border-radius-xl: 20px;

    /* 字体系统 */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Consolas', monospace;
    --font-display: 'Inter', system-ui, sans-serif;

    /* 动画系统 */
    --transition-fast: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    --transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 全局重置和基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-sans);
    background: var(--bg-gradient);
    background-attachment: fixed;
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 15px;
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    min-height: 100vh;
    padding: 24px;
}

/* 主容器 - 现代卡片设计 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    background: var(--bg-card);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-2xl);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(20px);
    overflow: hidden;
    position: relative;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
}

/* 现代头部设计 */
.header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--secondary-color) 100%);
    color: var(--text-inverse);
    padding: 32px 40px;
    position: relative;
    overflow: hidden;
}

.header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.system-logo {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 16px;
    position: relative;
    z-index: 1;
}

.logo-icon {
    font-size: 48px;
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-8px); }
}

.system-info {
    position: relative;
    z-index: 1;
}

.system-info h1 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 8px;
    letter-spacing: -0.025em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(135deg, #ffffff 0%, #e0e7ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.system-subtitle {
    font-size: 15px;
    opacity: 0.9;
    font-weight: 400;
    margin-bottom: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.system-badges {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    position: relative;
    z-index: 1;
}

.badge {
    display: inline-flex;
    align-items: center;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.25);
    border-radius: var(--border-radius);
    font-size: 13px;
    font-weight: 500;
    backdrop-filter: blur(10px);
    transition: var(--transition);
    cursor: default;
}

.badge:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* 现代状态指示器 */
.backend-status {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    border-radius: var(--border-radius);
    font-size: 13px;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.25);
    transition: var(--transition);
    cursor: pointer;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.backend-status::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.backend-status:hover::before {
    left: 100%;
}

.backend-status.online {
    background: rgba(16, 185, 129, 0.15);
    color: #ffffff;
    border-color: rgba(16, 185, 129, 0.3);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.backend-status.offline {
    background: rgba(239, 68, 68, 0.15);
    color: #ffffff;
    border-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.backend-status.demo {
    background: rgba(245, 158, 11, 0.15);
    color: #ffffff;
    border-color: rgba(245, 158, 11, 0.3);
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
}

/* 主要内容区域 */
.main-content {
    padding: 40px;
    background: transparent;
    position: relative;
}

/* 现代步骤容器 */
.step {
    margin-bottom: 32px;
    background: var(--bg-card);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    overflow: hidden;
    transition: var(--transition-slow);
    position: relative;
}

.step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: var(--transition);
}

.step:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.step:hover::before {
    opacity: 1;
}

.step-header {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 24px 32px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.4) 100%);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

.step-indicator {
    display: flex;
    align-items: center;
    gap: 16px;
}

.step-number {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-inverse);
    border-radius: 50%;
    font-weight: 700;
    font-size: 16px;
    flex-shrink: 0;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.step-number::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.step:hover .step-number::before {
    opacity: 1;
    animation: shine 0.6s ease-in-out;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.step-content h3 {
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 8px 0;
    letter-spacing: -0.025em;
}

.step-description {
    font-size: 15px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.5;
}

.step-content {
    padding: 32px;
}

/* 现代按钮系统 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
    font-family: var(--font-sans);
    letter-spacing: 0.025em;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn:disabled::before {
    display: none;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

.btn-primary:active:not(:disabled) {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.8);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover:not(:disabled) {
    background: rgba(255, 255, 255, 0.9);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.btn-success:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, #10b981 0%, var(--success-color) 100%);
}

.btn-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
    color: var(--text-inverse);
    box-shadow: var(--shadow-md);
}

.btn-warning:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-lg {
    padding: 16px 32px;
    font-size: 16px;
    border-radius: var(--border-radius-lg);
    font-weight: 700;
}

.btn-sm {
    padding: 8px 16px;
    font-size: 13px;
    border-radius: 8px;
}

/* 现代信息框样式 */
.info-box, .success-box, .error-box, .warning-box {
    padding: 20px;
    border-radius: var(--border-radius-lg);
    border: 1px solid;
    margin: 20px 0;
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
}

.info-box {
    background: linear-gradient(135deg, rgba(6, 182, 212, 0.1) 0%, rgba(6, 182, 212, 0.05) 100%);
    border-color: rgba(6, 182, 212, 0.3);
    color: var(--info-color);
}

.success-box {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(16, 185, 129, 0.05) 100%);
    border-color: rgba(16, 185, 129, 0.3);
    color: var(--success-color);
}

.error-box {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
    border-color: rgba(239, 68, 68, 0.3);
    color: var(--error-color);
}

.warning-box {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(245, 158, 11, 0.05) 100%);
    border-color: rgba(245, 158, 11, 0.3);
    color: var(--warning-color);
}

/* 表格样式 */
.data-table {
    width: 100%;
    border-collapse: collapse;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-primary);
    font-size: 13px;
}

.data-table th {
    background: var(--bg-secondary);
    color: var(--text-primary);
    font-weight: 600;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-table td {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
}

.data-table tr:hover {
    background: var(--bg-secondary);
}

/* 代码样式 */
code {
    font-family: var(--font-mono);
    background: var(--bg-tertiary);
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

/* 现代模型选择网格 */
.model-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
    margin: 24px 0;
}

.model-card {
    background: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    cursor: pointer;
    transition: var(--transition-slow);
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.model-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.model-card:hover {
    transform: translateY(-6px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(37, 99, 235, 0.3);
}

.model-card:hover::before {
    transform: scaleX(1);
}

.model-card.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
}

.model-card.selected::before {
    transform: scaleX(1);
}

.model-card.selected::after {
    content: '✓';
    position: absolute;
    top: 16px;
    right: 16px;
    width: 28px;
    height: 28px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    box-shadow: var(--shadow-md);
    animation: checkmark 0.3s var(--bounce);
}

@keyframes checkmark {
    0% { transform: scale(0) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
    100% { transform: scale(1) rotate(360deg); }
}

.model-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 16px;
}

.model-info {
    flex: 1;
}

.model-name {
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-size: 16px;
    line-height: 1.4;
}

.model-stage {
    font-size: 12px;
    color: var(--primary-color);
    background: rgba(37, 99, 235, 0.1);
    padding: 6px 12px;
    border-radius: var(--border-radius);
    display: inline-block;
    margin-bottom: 12px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.model-target {
    font-size: 14px;
    color: var(--text-secondary);
    line-height: 1.5;
    display: flex;
    align-items: center;
    gap: 8px;
}

.model-target::before {
    content: '🎯';
    font-size: 16px;
}

/* 现代预测模式选择 */
.mode-selection {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
    margin: 24px 0;
}

.mode-option {
    background: var(--bg-card);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-lg);
    padding: 24px;
    cursor: pointer;
    transition: var(--transition-slow);
    text-align: center;
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.mode-option::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.mode-option:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: rgba(37, 99, 235, 0.3);
}

.mode-option:hover::before {
    transform: scaleX(1);
}

.mode-option.selected {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1) 0%, rgba(37, 99, 235, 0.05) 100%);
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.mode-option.selected::before {
    transform: scaleX(1);
}

.mode-option.selected .mode-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.mode-icon {
    font-size: 32px;
    margin-bottom: 12px;
    display: block;
    color: var(--text-muted);
    transition: var(--transition);
}

.mode-name {
    font-weight: 700;
    margin-bottom: 8px;
    font-size: 16px;
    color: var(--text-primary);
}

.mode-description {
    font-size: 13px;
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 文件上传区域 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 32px;
    text-align: center;
    background: var(--bg-secondary);
    transition: var(--transition);
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.02);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(30, 58, 138, 0.05);
}

.upload-icon {
    font-size: 48px;
    color: var(--text-muted);
    margin-bottom: 16px;
}

.upload-text {
    font-size: 16px;
    color: var(--text-primary);
    margin-bottom: 8px;
    font-weight: 500;
}

.upload-hint {
    font-size: 14px;
    color: var(--text-muted);
}

/* 数据预览表格 */
.data-preview {
    margin: 16px 0;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    background: var(--bg-primary);
}

.data-preview-header {
    background: var(--bg-secondary);
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    font-weight: 600;
    font-size: 14px;
    color: var(--text-primary);
}

.data-preview-table {
    width: 100%;
    font-size: 12px;
    border-collapse: collapse;
}

.data-preview-table th {
    background: var(--bg-tertiary);
    padding: 8px 12px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    font-size: 11px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.data-preview-table td {
    padding: 8px 12px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-family: var(--font-mono);
}

/* 统计信息卡片 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 16px;
    margin: 16px 0;
}

.stat-card {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
    font-family: var(--font-mono);
}

.stat-label {
    font-size: 12px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 进度条 */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin: 8px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* 加载状态 */
.loading {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: var(--text-muted);
    font-size: 14px;
}

.loading-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid var(--bg-tertiary);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        margin: 10px;
        border-radius: var(--border-radius);
    }

    .header {
        padding: 16px 20px;
    }

    .main-content {
        padding: 20px;
    }

    .step-content {
        padding: 16px;
    }

    .system-info h1 {
        font-size: 20px;
    }

    .model-grid {
        grid-template-columns: 1fr;
    }

    .mode-selection {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* 图表容器 */
.chart-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: 20px;
    margin: 16px 0;
}

.chart-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
    text-align: center;
}

.chart-wrapper {
    position: relative;
    height: 400px;
    width: 100%;
}

/* 结果展示 */
.results-container {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    margin: 16px 0;
}

.results-header {
    background: var(--bg-secondary);
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.results-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.results-actions {
    display: flex;
    gap: 8px;
}

.results-content {
    padding: 20px;
}

/* 评估指标网格 */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin: 16px 0;
}

.metric-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 16px;
    text-align: center;
}

.metric-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 4px;
    font-family: var(--font-mono);
}

.metric-label {
    font-size: 12px;
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 4px;
}

.metric-description {
    font-size: 11px;
    color: var(--text-secondary);
    line-height: 1.3;
}

/* 性能等级指示器 */
.performance-grade {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 14px;
}

.performance-grade.excellent {
    background: rgba(5, 150, 105, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(5, 150, 105, 0.2);
}

.performance-grade.good {
    background: rgba(2, 132, 199, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(2, 132, 199, 0.2);
}

.performance-grade.fair {
    background: rgba(217, 119, 6, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(217, 119, 6, 0.2);
}

.performance-grade.poor {
    background: rgba(220, 38, 38, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(220, 38, 38, 0.2);
}

/* 数据表格增强 */
.enhanced-table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    overflow: hidden;
    font-size: 13px;
}

.enhanced-table thead {
    background: var(--bg-secondary);
}

.enhanced-table th {
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

.enhanced-table td {
    padding: 10px 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-secondary);
    font-family: var(--font-mono);
    font-size: 12px;
}

.enhanced-table tbody tr:hover {
    background: var(--bg-secondary);
}

.enhanced-table tbody tr:nth-child(even) {
    background: rgba(248, 250, 252, 0.5);
}

/* 通知和提示 */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 16px;
    border-radius: var(--border-radius);
    color: white;
    font-weight: 500;
    font-size: 14px;
    box-shadow: var(--shadow-lg);
    z-index: 1000;
    max-width: 400px;
    animation: slideIn 0.3s ease;
}

.notification.success {
    background: var(--success-color);
}

.notification.error {
    background: var(--error-color);
}

.notification.warning {
    background: var(--warning-color);
}

.notification.info {
    background: var(--info-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 模态框 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    border: 1px solid var(--border-color);
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--bg-secondary);
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: var(--transition);
}

.modal-close:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 8px;
    justify-content: flex-end;
    background: var(--bg-secondary);
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--gray-800);
    color: white;
    padding: 6px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    z-index: 1000;
}

.tooltip:hover::after {
    opacity: 1;
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

/* 实用类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.font-mono { font-family: var(--font-mono); }
.font-bold { font-weight: 600; }
.text-sm { font-size: 12px; }
.text-lg { font-size: 16px; }
.mt-4 { margin-top: 16px; }
.mb-4 { margin-bottom: 16px; }
.p-4 { padding: 16px; }
