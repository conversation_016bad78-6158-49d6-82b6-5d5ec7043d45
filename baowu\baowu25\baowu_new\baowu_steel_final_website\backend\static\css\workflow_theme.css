/*
油套管智能预测系统 - 流程化标签页主题
Workflow Tab-based Theme
*/

:root {
    /* 简洁配色 */
    --primary-color: #2563eb;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    
    /* 文字色 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-muted: #94a3b8;
    
    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    
    /* 字体 */
    --font-sans: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
    
    /* 动画 */
    --transition: all 0.2s ease;
}

/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-sans);
    background: var(--bg-secondary);
    color: var(--text-primary);
    line-height: 1.6;
    font-size: 14px;
}

/* 主容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    background: var(--bg-primary);
    min-height: 100vh;
    box-shadow: var(--shadow-lg);
}

/* 简洁头部 */
.header {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    padding: 20px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.system-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.system-subtitle {
    font-size: 12px;
    opacity: 0.8;
    margin-top: 2px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 标签页导航 */
.tab-navigation {
    background: var(--bg-primary);
    border-bottom: 1px solid var(--border-color);
    padding: 0 30px;
    display: flex;
    overflow-x: auto;
}

.tab-nav-item {
    padding: 15px 20px;
    border: none;
    background: none;
    color: var(--text-secondary);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 3px solid transparent;
    transition: var(--transition);
    white-space: nowrap;
    display: flex;
    align-items: center;
    gap: 8px;
}

.tab-nav-item:hover {
    color: var(--primary-color);
    background: var(--bg-secondary);
}

.tab-nav-item.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
    background: var(--bg-secondary);
}

.tab-nav-item.completed {
    color: var(--success-color);
}

.tab-nav-item.completed::after {
    content: '✓';
    font-size: 12px;
    background: var(--success-color);
    color: white;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 标签页内容 */
.tab-content {
    padding: 30px;
    min-height: 500px;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 内容区域 */
.content-section {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 25px;
    margin-bottom: 20px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.section-description {
    color: var(--text-secondary);
    font-size: 13px;
    margin-bottom: 20px;
}

/* 网格布局 */
.grid {
    display: grid;
    gap: 15px;
}

.grid-2 { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); }
.grid-3 { grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); }
.grid-4 { grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); }

/* 选择卡片 */
.selection-card {
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    padding: 20px;
    cursor: pointer;
    transition: var(--transition);
    background: var(--bg-primary);
}

.selection-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.selection-card.selected {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
    position: relative;
}

.selection-card.selected::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 10px;
    background: var(--primary-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
}

.card-title {
    font-weight: 600;
    margin-bottom: 8px;
    color: var(--text-primary);
}

.card-subtitle {
    font-size: 12px;
    color: var(--text-muted);
    margin-bottom: 8px;
}

.card-description {
    font-size: 13px;
    color: var(--text-secondary);
}

/* 按钮 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 10px 20px;
    border: 1px solid transparent;
    border-radius: var(--border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    background: none;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-light);
    border-color: var(--primary-light);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border-color: var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--bg-tertiary);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 信息框 */
.info-box, .success-box, .error-box, .warning-box {
    padding: 15px;
    border-radius: var(--border-radius);
    border: 1px solid;
    margin: 15px 0;
    font-size: 13px;
}

.info-box {
    background: rgba(37, 99, 235, 0.05);
    border-color: rgba(37, 99, 235, 0.2);
    color: var(--primary-color);
}

.success-box {
    background: rgba(16, 185, 129, 0.05);
    border-color: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.error-box {
    background: rgba(239, 68, 68, 0.05);
    border-color: rgba(239, 68, 68, 0.2);
    color: var(--error-color);
}

.warning-box {
    background: rgba(245, 158, 11, 0.05);
    border-color: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

/* 文件上传 */
.upload-area {
    border: 2px dashed var(--border-color);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    background: var(--bg-secondary);
    cursor: pointer;
    transition: var(--transition);
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.02);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(37, 99, 235, 0.05);
}

/* 底部操作栏 */
.tab-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
    border-top: 1px solid var(--border-color);
    margin-top: 30px;
}

.tab-actions-left {
    display: flex;
    gap: 10px;
}

.tab-actions-right {
    display: flex;
    gap: 10px;
}

/* 后端状态指示器 */
.backend-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
}

.backend-status.online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.backend-status.offline {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.backend-status.demo {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 12px;
    height: 12px;
    border: 2px solid var(--border-color);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 隐藏类 */
.hidden { display: none !important; }

/* 响应式 */
@media (max-width: 768px) {
    .container {
        margin: 0;
    }
    
    .header {
        padding: 15px 20px;
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }
    
    .tab-content {
        padding: 20px;
    }
    
    .grid-2, .grid-3, .grid-4 {
        grid-template-columns: 1fr;
    }
}
