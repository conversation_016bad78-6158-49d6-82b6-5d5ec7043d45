#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特征统计分析模块
基于final.csv文件分析模型特征的统计信息，用于数据填充
"""

import os
import pandas as pd
import numpy as np
from pathlib import Path
import json
import logging
from scipy import stats

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入AutoGluon
try:
    from autogluon.tabular import TabularPredictor
    AUTOGLUON_AVAILABLE = True
except ImportError:
    AUTOGLUON_AVAILABLE = False

class FeatureStatistics:
    """特征统计分析类"""
    
    def __init__(self, final_csv_path="final.csv"):
        self.final_csv_path = final_csv_path
        self.statistics = {}
        self.model_features = {}
        self.load_data()
        
    def load_data(self):
        """加载final.csv数据"""
        try:
            if os.path.exists(self.final_csv_path):
                self.data = pd.read_csv(self.final_csv_path)
                logger.info(f"✅ 成功加载final.csv，数据形状: {self.data.shape}")
            else:
                logger.warning(f"⚠️ 未找到{self.final_csv_path}文件")
                self.data = None
        except Exception as e:
            logger.error(f"❌ 加载final.csv失败: {e}")
            self.data = None
    
    def get_model_features(self, stage, model_name):
        """获取模型所需特征"""
        if not AUTOGLUON_AVAILABLE:
            return self._get_demo_features(stage, model_name)
        
        try:
            model_path = f"../models/{stage}/{model_name}_model"
            if os.path.exists(model_path):
                predictor = TabularPredictor.load(model_path)
                features = predictor.feature_metadata.get_features()
                return features
            else:
                logger.warning(f"⚠️ 模型路径不存在: {model_path}")
                return self._get_demo_features(stage, model_name)
        except Exception as e:
            logger.error(f"❌ 获取模型特征失败: {e}")
            return self._get_demo_features(stage, model_name)
    
    def _get_demo_features(self, stage, model_name):
        """获取演示模式的特征列表"""
        # 基于final.csv的前50个特征作为演示特征
        if self.data is not None:
            return list(self.data.columns[:50])
        else:
            # 默认特征列表
            return [
                "外径", "壁厚", "软抱紧力2", "软抱紧力3", "上辊冲击电流", "上辊平均电流",
                "下辊冲击电流", "下辊平均电流", "倾角", "准备时间", "前伸量", "咬入时间",
                "回退电流(%)", "快速2(%)", "慢速(%)", "盘距", "盘速", "盘高L", "盘高R",
                "空减长度_穿孔", "管坯重量", "纯轧时间", "软抱紧力 1", "辅助时间", "辊距",
                "辊速", "速降", "顶杆位", "顶杆位置", "1机架转速调整", "1机架辊缝调整",
                "2机架转速调整", "2机架辊缝调整", "3机架辊缝调整", "4机架辊缝调整",
                "5机架辊缝调整", "6机架转速调整", "6机架辊缝调整", "7机架转速调整",
                "7机架辊缝调整", "8机架转速调整", "8机架辊缝调整", "AF", "AR", "PV",
                "SF", "空减长度_连轧", "芯补修改", "连轧长度", "空减长度"
            ]
    
    def calculate_feature_statistics(self, features):
        """计算特征统计信息"""
        if self.data is None:
            return {}
        
        statistics = {}
        
        for feature in features:
            if feature in self.data.columns:
                series = self.data[feature]
                
                # 只处理数值型数据
                if pd.api.types.is_numeric_dtype(series):
                    # 去除缺失值
                    clean_series = series.dropna()
                    
                    if len(clean_series) > 0:
                        stats_dict = {
                            'count': len(clean_series),
                            'mean': float(clean_series.mean()),
                            'median': float(clean_series.median()),
                            'std': float(clean_series.std()),
                            'min': float(clean_series.min()),
                            'max': float(clean_series.max()),
                            'q25': float(clean_series.quantile(0.25)),
                            'q75': float(clean_series.quantile(0.75))
                        }
                        
                        # 计算众数
                        try:
                            mode_result = stats.mode(clean_series, keepdims=True)
                            if len(mode_result.mode) > 0:
                                stats_dict['mode'] = float(mode_result.mode[0])
                                stats_dict['mode_count'] = int(mode_result.count[0])
                            else:
                                stats_dict['mode'] = stats_dict['median']
                                stats_dict['mode_count'] = 1
                        except:
                            stats_dict['mode'] = stats_dict['median']
                            stats_dict['mode_count'] = 1
                        
                        # 选择填充值（优先级：众数 > 中位数 > 均值）
                        if stats_dict['mode_count'] >= 3:  # 众数出现次数>=3时使用众数
                            stats_dict['fill_value'] = stats_dict['mode']
                            stats_dict['fill_method'] = 'mode'
                        elif not np.isnan(stats_dict['median']):
                            stats_dict['fill_value'] = stats_dict['median']
                            stats_dict['fill_method'] = 'median'
                        else:
                            stats_dict['fill_value'] = stats_dict['mean']
                            stats_dict['fill_method'] = 'mean'
                        
                        statistics[feature] = stats_dict
                    else:
                        # 没有有效数据时使用默认值
                        statistics[feature] = {
                            'count': 0,
                            'fill_value': 0.0,
                            'fill_method': 'default'
                        }
                else:
                    # 非数值型数据
                    value_counts = series.value_counts()
                    if len(value_counts) > 0:
                        most_common = value_counts.index[0]
                        statistics[feature] = {
                            'count': len(series.dropna()),
                            'fill_value': most_common,
                            'fill_method': 'most_frequent',
                            'unique_values': list(value_counts.index[:10])  # 前10个最常见值
                        }
                    else:
                        statistics[feature] = {
                            'count': 0,
                            'fill_value': '',
                            'fill_method': 'default'
                        }
            else:
                # 特征不存在时的默认值
                statistics[feature] = {
                    'count': 0,
                    'fill_value': 0.0,
                    'fill_method': 'not_found'
                }
        
        return statistics
    
    def get_all_model_statistics(self):
        """获取所有模型的特征统计信息"""
        all_statistics = {}
        
        # 模型配置
        model_config = {
            "stage_1": ["硬度_油井管硬度极差", "硬度_油管硬度平均值"],
            "stage_2": ["平均抗拉强度", "拉伸_平均屈服强度", "拉伸_最大屈服强度", 
                       "拉伸_最小屈服强度", "最大抗拉强度", "最小抗拉强度"],
            "stage_3_A": ["抗硫_合格率", "抗硫_最小承载时间"],
            "stage_3_D": ["抗硫_平均抗硫因子", "抗硫_最大抗硫因子", "抗硫_最小抗硫因子"],
            "stage_4": ["抗硫_合格率", "抗硫_平均抗硫因子", "抗硫_最大抗硫因子", 
                       "抗硫_最小承载时间", "抗硫_最小抗硫因子"]
        }
        
        for stage, models in model_config.items():
            all_statistics[stage] = {}
            for model_name in models:
                logger.info(f"🔍 分析模型: {stage}/{model_name}")
                
                # 获取模型特征
                features = self.get_model_features(stage, model_name)
                
                # 计算统计信息
                statistics = self.calculate_feature_statistics(features)
                
                all_statistics[stage][model_name] = {
                    'features': features,
                    'statistics': statistics,
                    'feature_count': len(features)
                }
                
                logger.info(f"✅ 完成分析: {len(features)} 个特征")
        
        return all_statistics
    
    def save_statistics(self, output_path="feature_statistics.json"):
        """保存统计信息到JSON文件"""
        try:
            all_stats = self.get_all_model_statistics()
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(all_stats, f, ensure_ascii=False, indent=2)
            
            logger.info(f"✅ 统计信息已保存到: {output_path}")
            return True
        except Exception as e:
            logger.error(f"❌ 保存统计信息失败: {e}")
            return False
    
    def load_statistics(self, input_path="feature_statistics.json"):
        """从JSON文件加载统计信息"""
        try:
            if os.path.exists(input_path):
                with open(input_path, 'r', encoding='utf-8') as f:
                    self.statistics = json.load(f)
                logger.info(f"✅ 统计信息已加载: {input_path}")
                return True
            else:
                logger.warning(f"⚠️ 统计信息文件不存在: {input_path}")
                return False
        except Exception as e:
            logger.error(f"❌ 加载统计信息失败: {e}")
            return False
    
    def fill_missing_features(self, data, stage, model_name):
        """填充缺失特征"""
        if stage not in self.statistics or model_name not in self.statistics[stage]:
            logger.warning(f"⚠️ 未找到模型统计信息: {stage}/{model_name}")
            return data
        
        model_stats = self.statistics[stage][model_name]
        features = model_stats['features']
        statistics = model_stats['statistics']
        
        filled_data = data.copy()
        fill_report = []
        
        for feature in features:
            if feature not in filled_data.columns:
                # 特征完全缺失，添加新列
                if feature in statistics:
                    fill_value = statistics[feature]['fill_value']
                    filled_data[feature] = fill_value
                    fill_report.append({
                        'feature': feature,
                        'action': 'added_column',
                        'fill_value': fill_value,
                        'method': statistics[feature]['fill_method']
                    })
                else:
                    # 没有统计信息时使用默认值
                    filled_data[feature] = 0.0
                    fill_report.append({
                        'feature': feature,
                        'action': 'added_column',
                        'fill_value': 0.0,
                        'method': 'default'
                    })
            else:
                # 特征存在但有缺失值
                missing_count = filled_data[feature].isna().sum()
                if missing_count > 0 and feature in statistics:
                    fill_value = statistics[feature]['fill_value']
                    filled_data[feature].fillna(fill_value, inplace=True)
                    fill_report.append({
                        'feature': feature,
                        'action': 'filled_missing',
                        'missing_count': missing_count,
                        'fill_value': fill_value,
                        'method': statistics[feature]['fill_method']
                    })
        
        return filled_data, fill_report

def main():
    """主函数，用于生成统计信息"""
    print("🔍 开始分析final.csv特征统计信息...")
    
    # 创建统计分析器
    analyzer = FeatureStatistics("../final.csv")
    
    # 生成并保存统计信息
    if analyzer.save_statistics("feature_statistics.json"):
        print("✅ 特征统计信息生成完成！")
    else:
        print("❌ 特征统计信息生成失败！")

if __name__ == '__main__':
    main()
