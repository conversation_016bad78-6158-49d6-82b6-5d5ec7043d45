#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
耐应力腐蚀油套管智能预测系统 - 整合版启动脚本
"""

import os
import sys
import subprocess
import webbrowser
import time
from pathlib import Path

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = ['flask', 'flask_cors', 'pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def start_application():
    """启动整合后的应用"""
    print("🚀 启动耐应力腐蚀油套管智能预测系统...")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 切换到backend目录
    backend_dir = Path(__file__).parent / 'backend'
    if not backend_dir.exists():
        print("❌ backend目录不存在")
        return False
    
    # 启动Flask应用
    try:
        print("🌐 启动Web服务器...")
        print("📍 访问地址: http://localhost:5000")
        print("🔧 按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 延迟3秒后自动打开浏览器
        def open_browser():
            time.sleep(3)
            try:
                webbrowser.open('http://localhost:5000')
                print("🌍 已自动打开浏览器")
            except:
                print("⚠️ 无法自动打开浏览器，请手动访问 http://localhost:5000")
        
        import threading
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # 启动Flask应用
        os.chdir(backend_dir)
        subprocess.run([sys.executable, 'app.py'], check=True)
        
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
        return True
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def main():
    """主函数"""
    print("🔬 耐应力腐蚀油套管智能预测系统")
    print("📊 基于机器学习的质量预测平台")
    print("🏭 宝武钢铁集团")
    print()
    
    if start_application():
        print("✅ 应用启动成功")
    else:
        print("❌ 应用启动失败")
        input("按回车键退出...")

if __name__ == '__main__':
    main()
