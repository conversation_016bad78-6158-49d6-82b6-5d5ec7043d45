#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示自动端口功能
同时启动多个实例，展示自动端口分配
"""

import os
import sys
import time
import socket
import threading
import subprocess
from pathlib import Path

def find_available_port(start_port=8080, max_attempts=50):
    """寻找可用端口"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    # 如果没找到可用端口，使用系统分配
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('localhost', 0))
        port = s.getsockname()[1]
        return port

def test_port_allocation():
    """测试端口分配"""
    print("🔬 耐应力腐蚀油套管智能预测系统")
    print("🧪 自动端口分配演示")
    print("=" * 50)
    
    print("🔍 测试端口分配功能...")
    
    # 测试多个端口分配
    ports = []
    for i in range(5):
        port = find_available_port(8080 + i * 10)
        ports.append(port)
        print(f"✅ 实例 {i+1}: 分配端口 {port}")
    
    print(f"\n📊 成功分配了 {len(ports)} 个不同端口:")
    for i, port in enumerate(ports):
        print(f"   实例 {i+1}: localhost:{port}")
    
    print("\n💡 这意味着可以同时运行多个客户端实例，")
    print("   每个实例都会自动获得不同的端口，避免冲突。")

def demonstrate_multiple_instances():
    """演示多实例运行"""
    print("\n" + "=" * 50)
    print("🚀 多实例运行演示")
    print("=" * 50)
    
    print("💡 现在将演示如何同时运行多个实例：")
    print("1. 本地完整版 - 自动分配端口")
    print("2. 客户端版 - 自动分配端口")
    print("3. 服务器端 - 固定5000端口")
    
    choice = input("\n是否继续演示？(y/N): ").strip().lower()
    if choice != 'y':
        return
    
    print("\n🔧 准备启动多个实例...")
    
    # 启动服务器端（如果还没启动）
    print("1. 检查服务器端...")
    try:
        import requests
        response = requests.get("http://127.0.0.1:5000/api/health", timeout=2)
        if response.status_code == 200:
            print("✅ 服务器端已运行在端口 5000")
        else:
            print("⚠️ 服务器端响应异常")
    except:
        print("❌ 服务器端未运行，请先启动:")
        print("   python server_backend.py")
        return
    
    print("\n2. 启动本地完整版...")
    print("💡 本地完整版将自动寻找可用端口（通常是8081）")
    
    print("\n3. 启动客户端版...")
    print("💡 客户端版将自动寻找可用端口（通常是8080）")
    
    print("\n🎉 演示完成！")
    print("📋 总结：")
    print("- 服务器端：固定端口 5000")
    print("- 本地完整版：自动端口（如 8081）")
    print("- 客户端版：自动端口（如 8080）")
    print("- 所有实例可以同时运行，互不冲突")

def show_port_usage():
    """显示端口使用情况"""
    print("\n" + "=" * 50)
    print("📊 端口使用说明")
    print("=" * 50)
    
    print("🔹 服务器端 (server_backend.py):")
    print("   - 固定端口: 5000")
    print("   - 监听所有IP: 0.0.0.0:5000")
    print("   - 用途: 提供API服务")
    
    print("\n🔹 客户端 (client_frontend.py):")
    print("   - 自动端口: 从8080开始寻找")
    print("   - 监听本地: localhost:自动端口")
    print("   - 用途: 前端界面，转发请求到服务器")
    
    print("\n🔹 本地完整版 (complete_standalone_app.py):")
    print("   - 自动端口: 从8080开始寻找")
    print("   - 监听本地: localhost:自动端口")
    print("   - 用途: 完整的前后端服务")
    
    print("\n💡 自动端口分配规则:")
    print("1. 从指定起始端口开始（默认8080）")
    print("2. 依次尝试后续端口（8081, 8082, ...）")
    print("3. 找到可用端口后立即使用")
    print("4. 如果50个端口都被占用，使用系统随机分配")
    
    print("\n🎯 优势:")
    print("✅ 避免端口冲突")
    print("✅ 支持多实例运行")
    print("✅ 无需手动配置")
    print("✅ 自动适应环境")

def main():
    """主函数"""
    try:
        # 测试端口分配
        test_port_allocation()
        
        # 显示端口使用说明
        show_port_usage()
        
        # 演示多实例运行
        demonstrate_multiple_instances()
        
    except KeyboardInterrupt:
        print("\n👋 演示已停止")
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")

if __name__ == '__main__':
    main()
