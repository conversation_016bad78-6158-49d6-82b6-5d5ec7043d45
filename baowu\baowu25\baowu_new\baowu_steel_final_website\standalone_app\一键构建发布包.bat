@echo off
chcp 65001 >nul
title 一键构建发布包

echo.
echo ========================================
echo 📦 耐应力腐蚀油套管智能预测系统
echo 🏗️ 一键构建发布包工具
echo ========================================
echo.

echo 📋 构建内容：
echo 1. 便携版（需要Python环境）
echo 2. 可执行文件版（无需Python环境）
echo 3. 完整说明文档
echo 4. ZIP压缩包
echo.

echo 🔍 检查Python环境...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 🚀 开始构建发布包...
python create_distribution.py

echo.
echo 📁 检查构建结果...
if exist "distribution" (
    echo ✅ 发布包构建成功！
    echo 📂 发布目录: distribution\
    echo.
    
    if exist "distribution\便携版" (
        echo ✅ 便携版: 已创建
    )
    
    if exist "distribution\可执行文件版" (
        echo ✅ 可执行文件版: 已创建
    ) else (
        echo ⚠️ 可执行文件版: 构建失败（可能缺少PyInstaller）
    )
    
    if exist "耐应力腐蚀油套管智能预测系统_发布包.zip" (
        echo ✅ ZIP压缩包: 已创建
    )
    
    echo.
    echo 🎉 构建完成！
    echo.
    echo 📋 使用说明：
    echo 1. 将发布包复制到目标计算机
    echo 2. 解压ZIP文件
    echo 3. 根据环境选择合适的版本运行
    echo.
    
    set /p choice="是否打开发布目录？(Y/N): "
    if /i "%choice%"=="Y" (
        explorer distribution
    )
    
) else (
    echo ❌ 构建失败，请检查错误信息
)

pause
