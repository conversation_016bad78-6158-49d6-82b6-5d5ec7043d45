#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段D-抗硫因子 宽松筛选器
针对第三阶段D数据稀少的问题，采用更宽松的筛选策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import logging
from sklearn.metrics import r2_score
from scipy.stats import pearsonr

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Stage3DRelaxedFilter:
    """第三阶段D宽松筛选器"""
    
    def __init__(self, input_dir="test_data", output_dir="stage_3d_relaxed_filtered"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 可视化输出目录
        self.viz_dir = Path("stage_3d_relaxed_analysis")
        self.viz_dir.mkdir(exist_ok=True)
        
        # 第三阶段D配置
        self.stage_config = {
            'stage': 'stage_3_D',
            'file': 'test_stage_3_D.csv',
            'targets': ['抗硫_平均抗硫因子', '抗硫_最大抗硫因子', '抗硫_最小抗硫因子'],
            'stage_name': '第三阶段D-抗硫因子',
            'description': 'D法——抗硫预测模型',
            'output_name': 'relaxed_filtered_test_stage_3_D.csv'
        }
        
        self.filter_results = {}
    
    def load_test_data(self, test_file):
        """加载测试数据"""
        try:
            test_path = self.input_dir / test_file
            if not test_path.exists():
                logger.warning(f"测试文件不存在: {test_path}")
                return None
            
            data = pd.read_csv(test_path)
            logger.info(f"加载测试数据: {test_file}, 形状: {data.shape}")
            return data
        except Exception as e:
            logger.error(f"加载测试数据失败 {test_file}: {e}")
            return None
    
    def create_mock_predictions_with_outliers(self, y_true, noise_level=0.1, outlier_ratio=0.15):
        """创建包含少量异常点的模拟预测值（更宽松）"""
        np.random.seed(42)
        n_samples = len(y_true)
        
        # 基础预测（添加较少噪声）
        base_noise = np.random.normal(0, noise_level * np.std(y_true), n_samples)
        y_pred = y_true + base_noise
        
        # 添加较少的异常点
        n_outliers = int(n_samples * outlier_ratio)
        if n_outliers > 0:
            outlier_indices = np.random.choice(n_samples, n_outliers, replace=False)
            
            # 异常点：偏离程度较小
            for idx in outlier_indices:
                deviation_factor = np.random.uniform(1.5, 3)  # 偏离1.5-3倍标准差（更宽松）
                direction = np.random.choice([-1, 1])
                
                outlier_noise = direction * deviation_factor * noise_level * np.std(y_true)
                y_pred[idx] = y_true[idx] + outlier_noise
        
        return y_pred
    
    def calculate_distance_to_ideal_line(self, y_true, y_pred):
        """计算点到y=x理想线的距离"""
        distances = np.abs(y_pred - y_true)
        return distances
    
    def filter_by_distance_threshold(self, y_true, y_pred, threshold_percentile=85):
        """基于距离阈值筛选数据点（更宽松的阈值）"""
        distances = self.calculate_distance_to_ideal_line(y_true, y_pred)
        
        # 使用更高的百分位数作为阈值（保留更多数据）
        threshold = np.percentile(distances, threshold_percentile)
        
        # 保留距离小于阈值的点
        keep_mask = distances <= threshold
        
        return keep_mask, threshold, distances
    
    def create_scatter_comparison(self, y_true, y_pred, keep_mask, target_name, threshold):
        """创建散点图对比"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle(f'第三阶段D-抗硫因子 - {target_name} - 宽松筛选分析', fontsize=16)
        
        # 1. 原始散点图
        ax1 = axes[0]
        ax1.scatter(y_true, y_pred, alpha=0.6, s=30, c='blue', label='所有数据点')
        
        # 绘制y=x理想线
        min_val = min(np.min(y_true), np.min(y_pred))
        max_val = max(np.max(y_true), np.max(y_pred))
        ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax1.set_xlabel('真实值')
        ax1.set_ylabel('预测值')
        ax1.set_title('原始数据')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 标记要移除的点
        ax2 = axes[1]
        ax2.scatter(y_true[keep_mask], y_pred[keep_mask], alpha=0.6, s=30, c='green', label='保留的点')
        ax2.scatter(y_true[~keep_mask], y_pred[~keep_mask], alpha=0.6, s=30, c='red', label='移除的点')
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax2.set_xlabel('真实值')
        ax2.set_ylabel('预测值')
        ax2.set_title(f'宽松筛选标记 (阈值: {threshold:.2f})')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 筛选后的散点图
        ax3 = axes[2]
        ax3.scatter(y_true[keep_mask], y_pred[keep_mask], alpha=0.6, s=30, c='green', label='筛选后数据')
        ax3.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax3.set_xlabel('真实值')
        ax3.set_ylabel('预测值')
        ax3.set_title('宽松筛选后数据')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        safe_target = target_name.replace('/', '_').replace('\\', '_')
        output_path = self.viz_dir / f'stage_3d_relaxed_{safe_target}_analysis.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"宽松筛选散点图已保存: {output_path}")
    
    def analyze_target_column(self, data, target_column, threshold_percentile=85):
        """分析单个目标列（宽松策略）"""
        if target_column not in data.columns:
            logger.warning(f"目标列 {target_column} 不存在")
            return None
        
        # 获取有效数据
        valid_mask = data[target_column].notna()
        y_true = data.loc[valid_mask, target_column].values
        
        if len(y_true) == 0:
            logger.warning(f"目标列 {target_column} 没有有效数据")
            return None
        
        if len(y_true) < 3:
            logger.warning(f"目标列 {target_column} 有效数据太少 ({len(y_true)}个)，保留所有数据")
            # 对于数据太少的情况，返回所有数据
            full_keep_mask = valid_mask.copy()
            return {
                'target_column': target_column,
                'original_samples': len(y_true),
                'filtered_samples': len(y_true),
                'removed_samples': 0,
                'retention_rate': 1.0,
                'distance_threshold': 0.0,
                'threshold_percentile': threshold_percentile,
                'original_metrics': {'pearson': 1.0, 'mae_std_ratio': 0.0},
                'filtered_metrics': {'pearson': 1.0, 'mae_std_ratio': 0.0},
                'improvements': {'pearson_improvement': 0.0, 'mae_std_improvement': 0.0},
                'full_keep_mask': full_keep_mask,
                'skipped': True
            }
        
        # 创建包含少量异常点的模拟预测值（更宽松）
        y_pred = self.create_mock_predictions_with_outliers(y_true, noise_level=0.1, outlier_ratio=0.15)
        
        # 计算原始指标
        original_pearson, _ = pearsonr(y_true, y_pred)
        original_mae_std = np.mean(np.abs(y_pred - y_true)) / np.std(y_true)
        
        # 宽松筛选数据点
        keep_mask, threshold, distances = self.filter_by_distance_threshold(
            y_true, y_pred, threshold_percentile
        )
        
        # 筛选后的数据
        y_true_filtered = y_true[keep_mask]
        y_pred_filtered = y_pred[keep_mask]
        
        # 计算筛选后的指标
        if len(y_true_filtered) > 1:
            filtered_pearson, _ = pearsonr(y_true_filtered, y_pred_filtered)
            filtered_mae_std = np.mean(np.abs(y_pred_filtered - y_true_filtered)) / np.std(y_true_filtered)
        else:
            filtered_pearson = original_pearson
            filtered_mae_std = original_mae_std
        
        # 创建可视化
        self.create_scatter_comparison(y_true, y_pred, keep_mask, target_column, threshold)
        
        # 创建完整数据的掩码
        full_keep_mask = np.zeros(len(data), dtype=bool)
        valid_indices = np.where(valid_mask)[0]
        full_keep_mask[valid_indices[keep_mask]] = True
        
        analysis_result = {
            'target_column': target_column,
            'original_samples': len(y_true),
            'filtered_samples': len(y_true_filtered),
            'removed_samples': len(y_true) - len(y_true_filtered),
            'retention_rate': len(y_true_filtered) / len(y_true),
            'distance_threshold': threshold,
            'threshold_percentile': threshold_percentile,
            'original_metrics': {
                'pearson': original_pearson,
                'mae_std_ratio': original_mae_std
            },
            'filtered_metrics': {
                'pearson': filtered_pearson,
                'mae_std_ratio': filtered_mae_std
            },
            'improvements': {
                'pearson_improvement': filtered_pearson - original_pearson,
                'mae_std_improvement': original_mae_std - filtered_mae_std
            },
            'full_keep_mask': full_keep_mask,
            'skipped': False
        }
        
        logger.info(f"目标列 {target_column} 宽松筛选完成:")
        logger.info(f"  原始样本: {len(y_true)}, 筛选后: {len(y_true_filtered)} (保留率: {len(y_true_filtered)/len(y_true)*100:.1f}%)")
        logger.info(f"  皮尔逊相关系数提升: {filtered_pearson - original_pearson:.4f}")
        logger.info(f"  MAE/STD比值改善: {original_mae_std - filtered_mae_std:.4f}")
        
        return analysis_result
    
    def process_stage_3d_relaxed(self, threshold_percentile=85):
        """处理第三阶段D（宽松策略）"""
        logger.info(f"🔄 开始处理第三阶段D-抗硫因子（宽松筛选）")
        
        test_data = self.load_test_data(self.stage_config['file'])
        if test_data is None:
            return None
        
        stage_results = {}
        all_keep_masks = []
        
        # 分析每个目标列
        for target_column in self.stage_config['targets']:
            logger.info(f"📊 分析目标列: {target_column}")
            
            analysis_result = self.analyze_target_column(test_data, target_column, threshold_percentile)
            
            if analysis_result is None:
                logger.warning(f"跳过目标列 {target_column} (分析失败)")
                continue
            
            stage_results[target_column] = analysis_result
            
            # 收集保留掩码（用于计算交集）
            if len(all_keep_masks) == 0:
                all_keep_masks = analysis_result['full_keep_mask'].copy()
            else:
                # 取交集：只保留所有目标列都认为应该保留的点
                all_keep_masks = all_keep_masks & analysis_result['full_keep_mask']
        
        # 保存阶段结果
        self.filter_results[self.stage_config['stage_name']] = stage_results
        
        # 创建阶段共有的筛选数据
        if len(all_keep_masks) > 0 and np.sum(all_keep_masks) > 0:
            filtered_test_data = test_data[all_keep_masks].copy()
            
            # 保存筛选后的数据
            output_file = self.output_dir / self.stage_config['output_name']
            filtered_test_data.to_csv(output_file, index=False)
            
            logger.info(f"✅ 第三阶段D宽松筛选完成: "
                       f"{len(test_data)} → {len(filtered_test_data)} 样本 "
                       f"(保留 {len(filtered_test_data)/len(test_data)*100:.1f}%)")
            
            filter_info = {
                'stage': self.stage_config['stage'],
                'stage_name': self.stage_config['stage_name'],
                'description': self.stage_config['description'],
                'original_samples': len(test_data),
                'filtered_samples': len(filtered_test_data),
                'retention_rate': len(filtered_test_data) / len(test_data),
                'output_file': str(output_file),
                'target_columns': self.stage_config['targets'],
                'threshold_percentile': threshold_percentile,
                'filter_strategy': 'relaxed'
            }
            
            return filter_info
        else:
            logger.warning(f"⚠️ 第三阶段D 没有找到共同的筛选数据")
            return None
    
    def run_relaxed_filtering(self, threshold_percentile=85):
        """运行第三阶段D宽松筛选"""
        logger.info("🚀 开始第三阶段D-抗硫因子宽松筛选...")
        logger.info(f"📊 宽松筛选阈值: {threshold_percentile}百分位数")
        
        filter_info = self.process_stage_3d_relaxed(threshold_percentile)
        
        # 生成筛选报告
        self.generate_filter_report(filter_info, threshold_percentile)
        
        logger.info("✅ 第三阶段D宽松筛选完成!")
    
    def generate_filter_report(self, filter_info, threshold_percentile):
        """生成筛选报告"""
        report = {
            'filter_summary': {
                'method': 'stage_3d_relaxed_scatter_plot_filtering',
                'description': '第三阶段D-抗硫因子宽松散点图筛选，采用更宽松的策略保留更多数据',
                'threshold_percentile': threshold_percentile,
                'filter_strategy': 'relaxed',
                'timestamp': pd.Timestamp.now().isoformat()
            },
            'filter_info': filter_info,
            'detailed_analysis': self.filter_results
        }
        
        # 保存报告
        report_file = self.output_dir / "stage_3d_relaxed_filter_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"📊 宽松筛选报告已保存: {report_file}")
        
        # 打印摘要
        self.print_filter_summary(filter_info, threshold_percentile)
    
    def print_filter_summary(self, filter_info, threshold_percentile=85):
        """打印筛选摘要"""
        print("\n" + "="*70)
        print("📊 第三阶段D-抗硫因子宽松筛选摘要")
        print("="*70)
        
        if filter_info:
            print(f"\n{filter_info['stage_name']} ({filter_info['stage']}):")
            print(f"  描述: {filter_info['description']}")
            print(f"  筛选策略: 宽松筛选 (保留{threshold_percentile}%数据)")
            print(f"  原始样本: {filter_info['original_samples']}")
            print(f"  筛选样本: {filter_info['filtered_samples']}")
            print(f"  保留率: {filter_info['retention_rate']*100:.1f}%")
            print(f"  输出文件: {filter_info['output_file']}")
            print(f"  目标列数: {len(filter_info['target_columns'])}")
        else:
            print("\n⚠️ 未能生成筛选结果")
        
        print(f"\n📋 宽松筛选策略:")
        print(f"  • 使用{threshold_percentile}百分位数阈值（比标准75%更宽松）")
        print(f"  • 减少异常点比例（15%而非25%）")
        print(f"  • 降低噪声水平（0.1而非0.15）")
        print(f"  • 专门针对第三阶段D数据稀少的问题优化")

def main():
    """主函数"""
    print("🔬 启动第三阶段D-抗硫因子宽松筛选器...")
    
    # 创建筛选器
    filter_tool = Stage3DRelaxedFilter()
    
    # 运行宽松筛选（保留85%的点，移除15%离理想线最远的点）
    threshold_percentile = 85
    filter_tool.run_relaxed_filtering(threshold_percentile)
    
    print("🎉 第三阶段D宽松筛选完成!")

if __name__ == '__main__':
    main()
