#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单功能测试
"""

import sys
import os
sys.path.append('backend')

def test_feature_statistics():
    """测试特征统计功能"""
    print("🧪 测试特征统计功能")
    print("=" * 50)
    
    try:
        from feature_statistics import FeatureStatistics
        
        # 创建分析器
        analyzer = FeatureStatistics("final.csv")
        
        if analyzer.data is not None:
            print(f"✅ final.csv加载成功，数据形状: {analyzer.data.shape}")
            
            # 测试获取模型特征
            features = analyzer.get_model_features("stage_1", "硬度_油管硬度平均值")
            print(f"✅ 获取模型特征成功，特征数: {len(features)}")
            print(f"   前5个特征: {features[:5]}")
            
            # 测试计算统计信息
            stats = analyzer.calculate_feature_statistics(features[:10])
            print(f"✅ 计算统计信息成功，统计了 {len(stats)} 个特征")
            
            # 显示一个特征的统计信息
            if "外径" in stats:
                feature_stat = stats["外径"]
                print(f"   外径统计:")
                print(f"     均值: {feature_stat.get('mean', 'N/A')}")
                print(f"     中位数: {feature_stat.get('median', 'N/A')}")
                print(f"     众数: {feature_stat.get('mode', 'N/A')}")
                print(f"     填充值: {feature_stat.get('fill_value', 'N/A')}")
                print(f"     填充方法: {feature_stat.get('fill_method', 'N/A')}")
            
            # 测试加载统计信息
            if analyzer.load_statistics("backend/feature_statistics.json"):
                print("✅ 统计信息加载成功")
                
                # 测试填充功能
                import pandas as pd
                test_data = pd.DataFrame({
                    "外径": [92.0, None],
                    "壁厚": [18.6, 20.0]
                })
                
                filled_data, fill_report = analyzer.fill_missing_features(
                    test_data, "stage_1", "硬度_油管硬度平均值"
                )
                
                print(f"✅ 特征填充测试成功")
                print(f"   原始数据形状: {test_data.shape}")
                print(f"   填充后数据形状: {filled_data.shape}")
                print(f"   填充报告项数: {len(fill_report)}")
                
                if fill_report:
                    print("   填充报告示例:")
                    for item in fill_report[:3]:
                        print(f"     - {item['feature']}: {item['action']} ({item.get('method', 'N/A')})")
            else:
                print("⚠️ 统计信息加载失败")
        else:
            print("❌ final.csv加载失败")
            
    except Exception as e:
        print(f"❌ 特征统计功能测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_app_import():
    """测试app模块导入"""
    print("\n🧪 测试app模块导入")
    print("=" * 50)
    
    try:
        import app
        print("✅ app模块导入成功")
        
        # 检查特征分析器
        if hasattr(app, 'feature_analyzer') and app.feature_analyzer:
            print("✅ 特征分析器已初始化")
            print(f"   数据已加载: {app.feature_analyzer.data is not None}")
            print(f"   统计信息已加载: {len(app.feature_analyzer.statistics) > 0}")
        else:
            print("⚠️ 特征分析器未初始化")
            
        # 检查模型配置
        if hasattr(app, 'AVAILABLE_MODELS'):
            model_count = sum(len(models) for models in app.AVAILABLE_MODELS.values())
            print(f"✅ 模型配置已加载，共 {model_count} 个模型")
        else:
            print("⚠️ 模型配置未加载")
            
    except Exception as e:
        print(f"❌ app模块导入失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主函数"""
    print("🚀 开始简单功能测试...")
    
    # 测试特征统计功能
    test_feature_statistics()
    
    # 测试app模块导入
    test_app_import()
    
    print(f"\n{'='*50}")
    print("🎉 简单功能测试完成！")

if __name__ == '__main__':
    main()
