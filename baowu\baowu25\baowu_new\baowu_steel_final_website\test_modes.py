#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试两种预测模式的脚本
"""

import requests
import json
import pandas as pd
import time

def test_prediction_modes():
    """测试预测模式"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 测试两种预测模式")
    print("=" * 60)
    
    # 1. 测试模式信息接口
    print("1️⃣ 测试模式信息接口")
    try:
        response = requests.get(f"{base_url}/api/modes", timeout=5)
        if response.status_code == 200:
            modes_data = response.json()
            print("✅ 模式信息获取成功")
            print(f"   严格模式: {modes_data['modes']['strict']['name']}")
            print(f"   宽松模式: {modes_data['modes']['lenient']['name']}")
            print(f"   默认模式: {modes_data['default_mode']}")
            print(f"   特征统计可用: {modes_data['feature_statistics_available']}")
            print(f"   final.csv已加载: {modes_data['final_csv_loaded']}")
        else:
            print(f"❌ 模式信息获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模式信息接口测试失败: {e}")
        return False
    
    # 2. 准备测试数据
    print(f"\n2️⃣ 准备测试数据")
    
    # 完整数据（包含所有特征）
    complete_data = {
        "外径": 92.0,
        "壁厚": 18.6,
        "软抱紧力2": 10.0,
        "软抱紧力3": 10.0,
        "上辊冲击电流": 823.0,
        "上辊平均电流": 736.0,
        "下辊冲击电流": 807.0,
        "下辊平均电流": 737.0,
        "倾角": 10.0,
        "准备时间": 6.3
    }
    
    # 不完整数据（缺少部分特征）
    incomplete_data = {
        "外径": 92.0,
        "壁厚": 18.6,
        "上辊冲击电流": 823.0,
        "上辊平均电流": 736.0
    }
    
    print(f"   完整数据特征数: {len(complete_data)}")
    print(f"   不完整数据特征数: {len(incomplete_data)}")
    
    # 3. 测试严格模式
    print(f"\n3️⃣ 测试严格模式")
    
    # 3.1 严格模式 + 完整数据
    print("   3.1 严格模式 + 完整数据")
    try:
        payload = {
            "stage": "stage_1",
            "model_name": "硬度_油管硬度平均值",
            "mode": "strict",
            "data": [complete_data]
        }
        
        response = requests.post(f"{base_url}/api/predict", 
                               json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 严格模式 + 完整数据：预测成功")
            print(f"      预测模式: {result.get('prediction_mode')}")
            print(f"      处理行数: {result.get('processed_rows')}")
        else:
            result = response.json()
            print(f"   ❌ 严格模式 + 完整数据：预测失败")
            print(f"      错误: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ 严格模式 + 完整数据测试失败: {e}")
    
    # 3.2 严格模式 + 不完整数据
    print("   3.2 严格模式 + 不完整数据")
    try:
        payload = {
            "stage": "stage_1",
            "model_name": "硬度_油管硬度平均值",
            "mode": "strict",
            "data": [incomplete_data]
        }
        
        response = requests.post(f"{base_url}/api/predict", 
                               json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("   ⚠️ 严格模式 + 不完整数据：意外成功")
        else:
            result = response.json()
            print("   ✅ 严格模式 + 不完整数据：正确报错")
            print(f"      错误: {result.get('error')}")
            print(f"      缺失特征: {result.get('missing_features', [])}")
    except Exception as e:
        print(f"   ❌ 严格模式 + 不完整数据测试失败: {e}")
    
    # 4. 测试宽松模式
    print(f"\n4️⃣ 测试宽松模式")
    
    # 4.1 宽松模式 + 完整数据
    print("   4.1 宽松模式 + 完整数据")
    try:
        payload = {
            "stage": "stage_1",
            "model_name": "硬度_油管硬度平均值",
            "mode": "lenient",
            "data": [complete_data]
        }
        
        response = requests.post(f"{base_url}/api/predict", 
                               json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 宽松模式 + 完整数据：预测成功")
            print(f"      预测模式: {result.get('prediction_mode')}")
            print(f"      处理行数: {result.get('processed_rows')}")
            fill_report = result.get('fill_report')
            if fill_report:
                print(f"      填充报告: {len(fill_report)} 项操作")
        else:
            result = response.json()
            print(f"   ❌ 宽松模式 + 完整数据：预测失败")
            print(f"      错误: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ 宽松模式 + 完整数据测试失败: {e}")
    
    # 4.2 宽松模式 + 不完整数据
    print("   4.2 宽松模式 + 不完整数据")
    try:
        payload = {
            "stage": "stage_1",
            "model_name": "硬度_油管硬度平均值",
            "mode": "lenient",
            "data": [incomplete_data]
        }
        
        response = requests.post(f"{base_url}/api/predict", 
                               json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 宽松模式 + 不完整数据：预测成功")
            print(f"      预测模式: {result.get('prediction_mode')}")
            print(f"      处理行数: {result.get('processed_rows')}")
            fill_report = result.get('fill_report')
            if fill_report:
                print(f"      填充报告: {len(fill_report)} 项操作")
                for item in fill_report[:3]:  # 显示前3项
                    print(f"        - {item['feature']}: {item['action']} ({item['method']})")
        else:
            result = response.json()
            print(f"   ❌ 宽松模式 + 不完整数据：预测失败")
            print(f"      错误: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ 宽松模式 + 不完整数据测试失败: {e}")
    
    # 5. 测试默认模式（不指定mode参数）
    print(f"\n5️⃣ 测试默认模式")
    try:
        payload = {
            "stage": "stage_1",
            "model_name": "硬度_油管硬度平均值",
            "data": [incomplete_data]
        }
        
        response = requests.post(f"{base_url}/api/predict", 
                               json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 默认模式：预测成功")
            print(f"      预测模式: {result.get('prediction_mode')}")
            print(f"      处理行数: {result.get('processed_rows')}")
        else:
            result = response.json()
            print(f"   ❌ 默认模式：预测失败")
            print(f"      错误: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ 默认模式测试失败: {e}")
    
    print(f"\n{'='*60}")
    print("🎉 两种预测模式测试完成！")
    return True

def main():
    """主函数"""
    print("🚀 启动两种预测模式测试...")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://127.0.0.1:5000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器响应异常")
            return
    except:
        print("❌ 无法连接到服务器，请先启动app.py")
        return
    
    # 运行测试
    test_prediction_modes()

if __name__ == '__main__':
    main()
