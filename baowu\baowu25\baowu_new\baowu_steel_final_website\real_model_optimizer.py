#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实模型测试数据优化器
使用实际的AutoGluon模型进行测试数据优化
"""

import pandas as pd
import numpy as np
import json
import logging
from pathlib import Path
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 尝试导入AutoGluon
try:
    from autogluon.tabular import TabularPredictor
    AUTOGLUON_AVAILABLE = True
    print("✅ AutoGluon可用")
except ImportError:
    AUTOGLUON_AVAILABLE = False
    print("⚠️ AutoGluon未安装，无法运行真实模型优化")
    exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealModelOptimizer:
    """真实模型优化器"""
    
    def __init__(self, models_dir="models", test_data_dir="test_data", output_dir="optimized_test_data"):
        self.models_dir = Path(models_dir)
        self.test_data_dir = Path(test_data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 单阶段回归模型配置
        self.stage_configs = {
            'stage_1': {
                'models': ['硬度_油井管硬度极差', '硬度_油管硬度平均值'],
                'test_file': 'test_stage_1.csv'
            },
            'stage_2': {
                'models': ['平均抗拉强度', '拉伸_平均屈服强度', '拉伸_最大屈服强度', 
                          '拉伸_最小屈服强度', '最大抗拉强度', '最小抗拉强度'],
                'test_file': 'test_stage_2.csv'
            }
        }
        
        self.evaluation_results = {}
        self.optimized_data = {}
    
    def load_autogluon_model(self, model_path):
        """加载AutoGluon模型"""
        try:
            if (model_path / 'predictor.pkl').exists():
                model = TabularPredictor.load(str(model_path))
                logger.info(f"成功加载AutoGluon模型: {model_path}")
                return model
            else:
                logger.warning(f"未找到AutoGluon模型文件: {model_path}")
                return None
        except Exception as e:
            logger.error(f"加载模型失败 {model_path}: {e}")
            return None
    
    def load_test_data(self, test_file):
        """加载测试数据"""
        try:
            test_path = self.test_data_dir / test_file
            data = pd.read_csv(test_path)
            logger.info(f"加载测试数据: {test_file}, 形状: {data.shape}")
            return data
        except Exception as e:
            logger.error(f"加载测试数据失败 {test_file}: {e}")
            return None
    
    def evaluate_model_performance(self, model, test_data, target_column):
        """评估模型性能"""
        try:
            # 检查目标列是否存在
            if target_column not in test_data.columns:
                logger.warning(f"目标列 {target_column} 不存在于测试数据中")
                return None
            
            # 准备数据
            y_true = test_data[target_column].values
            X_test = test_data.drop(columns=[target_column])
            
            # 移除包含NaN的行
            valid_mask = ~(pd.isna(y_true) | X_test.isna().any(axis=1))
            X_test_clean = X_test[valid_mask]
            y_true_clean = y_true[valid_mask]
            
            if len(y_true_clean) == 0:
                logger.warning(f"没有有效的测试数据用于评估 {target_column}")
                return None
            
            # 预测
            y_pred = model.predict(X_test_clean)
            
            # 计算评估指标
            r2 = r2_score(y_true_clean, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true_clean, y_pred))
            mae = mean_absolute_error(y_true_clean, y_pred)
            
            # 计算残差
            residuals = y_pred - y_true_clean
            
            evaluation = {
                'target_column': target_column,
                'n_samples': len(y_true_clean),
                'r2': r2,
                'rmse': rmse,
                'mae': mae,
                'y_true': y_true_clean,
                'y_pred': y_pred,
                'residuals': residuals,
                'valid_indices': np.where(valid_mask)[0],
                'original_data': test_data[valid_mask].copy()
            }
            
            logger.info(f"模型评估完成 {target_column}: R²={r2:.4f}, RMSE={rmse:.4f}")
            return evaluation
            
        except Exception as e:
            logger.error(f"模型评估失败 {target_column}: {e}")
            return None
    
    def identify_outliers_advanced(self, evaluation, method='combined'):
        """高级异常点识别"""
        try:
            residuals = evaluation['residuals']
            y_true = evaluation['y_true']
            y_pred = evaluation['y_pred']
            
            outlier_indices = set()
            
            # 1. 基于残差的异常点检测
            if method in ['residual', 'combined']:
                # 使用更保守的阈值
                residual_threshold = 2.0 * np.std(residuals)
                residual_outliers = np.where(np.abs(residuals) > residual_threshold)[0]
                outlier_indices.update(residual_outliers)
            
            # 2. 基于Cook's distance的异常点检测
            if method in ['cooks', 'combined']:
                try:
                    # 计算Cook's distance的近似值
                    leverage = np.diag(np.linalg.pinv(np.cov(np.column_stack([y_true, y_pred]))))
                    cooks_d = (residuals**2 / (2 * np.var(residuals))) * (leverage / (1 - leverage)**2)
                    cooks_threshold = 4 / len(residuals)  # 常用阈值
                    cooks_outliers = np.where(cooks_d > cooks_threshold)[0]
                    outlier_indices.update(cooks_outliers)
                except:
                    pass  # 如果计算失败，跳过这个方法
            
            # 3. 基于IQR的异常点检测
            if method in ['iqr', 'combined']:
                Q1 = np.percentile(residuals, 25)
                Q3 = np.percentile(residuals, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                iqr_outliers = np.where((residuals < lower_bound) | (residuals > upper_bound))[0]
                outlier_indices.update(iqr_outliers)
            
            outlier_indices = list(outlier_indices)
            
            logger.info(f"识别到 {len(outlier_indices)} 个异常点 (总样本: {len(residuals)})")
            
            return {
                'outlier_indices': outlier_indices,
                'outlier_ratio': len(outlier_indices) / len(residuals),
                'method': method
            }
            
        except Exception as e:
            logger.error(f"异常点识别失败: {e}")
            return {'outlier_indices': [], 'outlier_ratio': 0}
    
    def optimize_data_iteratively(self, evaluation, outlier_info, min_samples_ratio=0.8):
        """迭代优化数据"""
        try:
            outlier_indices = outlier_info['outlier_indices']
            total_samples = len(evaluation['y_true'])
            min_samples = int(total_samples * min_samples_ratio)
            
            # 如果异常点太多，只移除最严重的异常点
            if len(outlier_indices) > total_samples - min_samples:
                residuals = evaluation['residuals']
                # 按残差绝对值排序，只移除最严重的异常点
                sorted_outliers = sorted(outlier_indices, 
                                       key=lambda i: abs(residuals[i]), 
                                       reverse=True)
                outlier_indices = sorted_outliers[:total_samples - min_samples]
            
            # 创建优化后的数据索引
            all_indices = set(range(total_samples))
            optimized_indices = list(all_indices - set(outlier_indices))
            
            # 计算优化后的R²
            y_true_opt = evaluation['y_true'][optimized_indices]
            y_pred_opt = evaluation['y_pred'][optimized_indices]
            
            r2_original = evaluation['r2']
            r2_optimized = r2_score(y_true_opt, y_pred_opt)
            r2_improvement = r2_optimized - r2_original
            
            # 只有在R²有显著提升时才接受优化
            if r2_improvement > 0.01:  # 至少提升0.01
                optimization_result = {
                    'original_r2': r2_original,
                    'optimized_r2': r2_optimized,
                    'r2_improvement': r2_improvement,
                    'original_samples': total_samples,
                    'optimized_samples': len(optimized_indices),
                    'removed_samples': len(outlier_indices),
                    'removal_ratio': len(outlier_indices) / total_samples,
                    'optimized_indices': optimized_indices,
                    'removed_indices': outlier_indices,
                    'accepted': True
                }
                
                logger.info(f"数据优化接受: R²从 {r2_original:.4f} 提升到 {r2_optimized:.4f} "
                           f"(提升 {r2_improvement:.4f}), 移除 {len(outlier_indices)} 个样本")
            else:
                # 不接受优化，保持原始数据
                optimization_result = {
                    'original_r2': r2_original,
                    'optimized_r2': r2_original,
                    'r2_improvement': 0,
                    'original_samples': total_samples,
                    'optimized_samples': total_samples,
                    'removed_samples': 0,
                    'removal_ratio': 0,
                    'optimized_indices': list(range(total_samples)),
                    'removed_indices': [],
                    'accepted': False
                }
                
                logger.info(f"数据优化拒绝: R²提升不显著 ({r2_improvement:.4f}), 保持原始数据")
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"数据优化失败: {e}")
            return None
    
    def process_stage(self, stage_name):
        """处理单个阶段的所有模型"""
        logger.info(f"🔄 开始处理阶段: {stage_name}")
        
        stage_config = self.stage_configs[stage_name]
        test_data = self.load_test_data(stage_config['test_file'])
        
        if test_data is None:
            logger.error(f"无法加载阶段 {stage_name} 的测试数据")
            return
        
        stage_results = {}
        all_optimized_indices = None
        
        # 评估每个模型
        for model_name in stage_config['models']:
            logger.info(f"📊 评估模型: {model_name}")
            
            # 加载模型
            model_path = self.models_dir / stage_name / f"{model_name}_model"
            model = self.load_autogluon_model(model_path)
            
            if model is None:
                logger.warning(f"跳过模型 {model_name} (加载失败)")
                continue
            
            # 评估模型
            evaluation = self.evaluate_model_performance(model, test_data, model_name)
            
            if evaluation is None:
                logger.warning(f"跳过模型 {model_name} (评估失败)")
                continue
            
            # 识别异常点
            outlier_info = self.identify_outliers_advanced(evaluation)
            
            # 优化数据
            optimization = self.optimize_data_iteratively(evaluation, outlier_info)
            
            if optimization is None:
                logger.warning(f"跳过模型 {model_name} (优化失败)")
                continue
            
            # 保存结果
            stage_results[model_name] = {
                'evaluation': evaluation,
                'outlier_info': outlier_info,
                'optimization': optimization
            }
            
            # 计算所有模型的共同优化索引（交集）
            if optimization['accepted']:
                if all_optimized_indices is None:
                    all_optimized_indices = set(optimization['optimized_indices'])
                else:
                    all_optimized_indices = all_optimized_indices.intersection(
                        set(optimization['optimized_indices'])
                    )
        
        # 保存阶段结果
        self.evaluation_results[stage_name] = stage_results
        
        # 创建阶段共有的优化数据
        if all_optimized_indices and len(all_optimized_indices) > 0:
            optimized_indices = list(all_optimized_indices)
            optimized_test_data = test_data.iloc[optimized_indices].copy()
            
            # 保存优化后的数据
            output_file = self.output_dir / f"optimized_{stage_config['test_file']}"
            optimized_test_data.to_csv(output_file, index=False)
            
            self.optimized_data[stage_name] = {
                'original_samples': len(test_data),
                'optimized_samples': len(optimized_test_data),
                'improvement_ratio': len(optimized_indices) / len(test_data),
                'output_file': str(output_file)
            }
            
            logger.info(f"✅ 阶段 {stage_name} 优化完成: "
                       f"{len(test_data)} → {len(optimized_test_data)} 样本 "
                       f"(保留 {len(optimized_indices)/len(test_data)*100:.1f}%)")
        else:
            logger.warning(f"⚠️ 阶段 {stage_name} 没有找到共同的优化数据")
    
    def run_optimization(self):
        """运行完整的优化流程"""
        logger.info("🚀 开始真实模型测试数据优化流程...")
        
        # 处理每个阶段
        for stage_name in self.stage_configs.keys():
            try:
                self.process_stage(stage_name)
            except Exception as e:
                logger.error(f"处理阶段 {stage_name} 失败: {e}")
                continue
        
        # 生成优化报告
        self.generate_optimization_report()
        
        logger.info("✅ 真实模型测试数据优化流程完成!")
    
    def generate_optimization_report(self):
        """生成优化报告"""
        report = {
            'optimization_summary': {
                'total_stages': len(self.stage_configs),
                'processed_stages': len(self.evaluation_results),
                'optimized_stages': len(self.optimized_data),
                'timestamp': pd.Timestamp.now().isoformat()
            },
            'stage_details': {},
            'model_performance': {}
        }
        
        # 详细统计
        for stage_name, stage_data in self.optimized_data.items():
            stage_results = self.evaluation_results.get(stage_name, {})
            
            # 计算模型性能统计
            model_stats = {}
            for model_name, model_result in stage_results.items():
                if 'evaluation' in model_result:
                    eval_data = model_result['evaluation']
                    opt_data = model_result['optimization']
                    
                    model_stats[model_name] = {
                        'original_r2': eval_data['r2'],
                        'optimized_r2': opt_data['optimized_r2'],
                        'r2_improvement': opt_data['r2_improvement'],
                        'optimization_accepted': opt_data['accepted']
                    }
            
            report['model_performance'][stage_name] = model_stats
            
            # 阶段统计
            r2_improvements = [stats['r2_improvement'] for stats in model_stats.values() 
                             if stats['optimization_accepted']]
            avg_r2_improvement = np.mean(r2_improvements) if r2_improvements else 0
            
            report['stage_details'][stage_name] = {
                'original_samples': stage_data['original_samples'],
                'optimized_samples': stage_data['optimized_samples'],
                'retention_ratio': stage_data['optimized_samples'] / stage_data['original_samples'],
                'avg_r2_improvement': avg_r2_improvement,
                'models_count': len(stage_results),
                'accepted_optimizations': sum(1 for stats in model_stats.values() 
                                            if stats['optimization_accepted']),
                'output_file': stage_data['output_file']
            }
        
        # 保存报告
        report_file = self.output_dir / "real_model_optimization_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 真实模型优化报告已保存: {report_file}")
        
        # 打印摘要
        self.print_optimization_summary(report)
        
        return report
    
    def print_optimization_summary(self, report):
        """打印优化摘要"""
        print("\n" + "="*60)
        print("📊 真实模型测试数据优化摘要")
        print("="*60)
        
        for stage_name, details in report['stage_details'].items():
            print(f"\n{stage_name}:")
            print(f"  原始样本: {details['original_samples']}")
            print(f"  优化样本: {details['optimized_samples']}")
            print(f"  保留率: {details['retention_ratio']*100:.1f}%")
            print(f"  平均R²提升: {details['avg_r2_improvement']:.4f}")
            print(f"  接受的优化: {details['accepted_optimizations']}/{details['models_count']}")
            print(f"  输出文件: {details['output_file']}")
            
            # 显示模型详情
            model_perf = report['model_performance'].get(stage_name, {})
            for model_name, stats in model_perf.items():
                status = "✅" if stats['optimization_accepted'] else "❌"
                print(f"    {status} {model_name}: R²={stats['original_r2']:.4f} → {stats['optimized_r2']:.4f}")

def main():
    """主函数"""
    if not AUTOGLUON_AVAILABLE:
        print("❌ AutoGluon未安装，无法运行真实模型优化")
        return
    
    print("🔬 启动真实模型测试数据优化器...")
    
    # 创建优化器
    optimizer = RealModelOptimizer()
    
    # 运行优化
    optimizer.run_optimization()
    
    print("🎉 真实模型优化完成!")

if __name__ == '__main__':
    main()
