#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动测试两套完整版方案
"""

import os
import sys
import time
import requests
import subprocess
import threading
from pathlib import Path

def test_server_health():
    """测试服务器健康状态"""
    try:
        response = requests.get('http://127.0.0.1:5000/api/health', timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器端测试成功")
            print(f"   状态: {data.get('status', '未知')}")
            print(f"   版本: {data.get('version', '未知')}")
            print(f"   AutoGluon: {'可用' if data.get('autogluon_available') else '不可用'}")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 服务器连接失败 - 服务器可能未启动")
        return False
    except Exception as e:
        print(f"❌ 服务器测试失败: {e}")
        return False

def test_server_models():
    """测试服务器模型接口"""
    try:
        response = requests.get('http://127.0.0.1:5000/api/models', timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                models = data.get('models', {})
                model_count = sum(len(stage_models) for stage_models in models.values())
                print(f"✅ 模型接口测试成功")
                print(f"   找到 {len(models)} 个阶段")
                print(f"   总计 {model_count} 个模型")
                return True
            else:
                print(f"❌ 模型接口返回错误: {data.get('error', '未知错误')}")
                return False
        else:
            print(f"❌ 模型接口响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模型接口测试失败: {e}")
        return False

def test_client_connection():
    """测试客户端连接"""
    try:
        # 测试客户端是否能连接到服务器
        print("🔍 测试客户端-服务器连接...")
        
        # 设置环境变量
        os.environ['SERVER_HOST'] = '127.0.0.1'
        os.environ['SERVER_PORT'] = '5000'
        
        # 导入客户端模块
        import client_frontend
        
        # 设置配置
        client_frontend.CONFIG['SERVER_HOST'] = '127.0.0.1'
        client_frontend.CONFIG['SERVER_PORT'] = 5000
        client_frontend.CONFIG['SERVER_URL'] = 'http://127.0.0.1:5000'
        
        # 测试服务器连接
        if client_frontend.test_server_connection():
            print("✅ 客户端连接测试成功")
            return True
        else:
            print("❌ 客户端连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        return False

def test_complete_standalone():
    """测试本地完整版"""
    try:
        print("🔍 测试本地完整版...")
        
        # 导入完整版模块
        import complete_standalone_app
        
        # 测试端口分配
        port = complete_standalone_app.find_available_port()
        print(f"✅ 端口分配测试成功: {port}")
        
        # 测试模型扫描
        models_root = complete_standalone_app.get_models_root()
        if models_root.exists():
            model_count = sum(1 for _ in models_root.rglob("*_model"))
            print(f"✅ 模型文件扫描成功: 找到 {model_count} 个模型")
        else:
            print("⚠️ 模型目录不存在，将使用演示模式")
        
        return True
        
    except Exception as e:
        print(f"❌ 本地完整版测试失败: {e}")
        return False

def test_port_allocation():
    """测试端口分配功能"""
    try:
        print("🔍 测试智能端口分配...")
        
        import socket
        
        def find_available_port(start_port=8080, max_attempts=10):
            for port in range(start_port, start_port + max_attempts):
                try:
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        s.bind(('localhost', port))
                        return port
                except OSError:
                    continue
            return None
        
        # 测试多个端口分配
        ports = []
        for i in range(3):
            port = find_available_port(8080 + i * 10)
            if port:
                ports.append(port)
        
        if len(ports) >= 3:
            print(f"✅ 端口分配测试成功: {ports}")
            return True
        else:
            print("❌ 端口分配测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 端口分配测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔬 耐应力腐蚀油套管智能预测系统")
    print("🧪 自动化测试程序")
    print("=" * 60)
    
    test_results = []
    
    # 1. 测试端口分配功能
    print("\n1️⃣ 测试智能端口分配功能")
    print("-" * 40)
    result1 = test_port_allocation()
    test_results.append(("端口分配功能", result1))
    
    # 2. 测试服务器端
    print("\n2️⃣ 测试服务器端")
    print("-" * 40)
    result2 = test_server_health()
    if result2:
        result2_models = test_server_models()
        test_results.append(("服务器健康检查", result2))
        test_results.append(("服务器模型接口", result2_models))
    else:
        test_results.append(("服务器健康检查", result2))
        print("⚠️ 服务器未启动，跳过模型接口测试")
    
    # 3. 测试客户端连接
    print("\n3️⃣ 测试客户端连接")
    print("-" * 40)
    if result2:  # 只有服务器正常时才测试客户端
        result3 = test_client_connection()
        test_results.append(("客户端连接", result3))
    else:
        print("⚠️ 服务器未启动，跳过客户端连接测试")
        test_results.append(("客户端连接", False))
    
    # 4. 测试本地完整版
    print("\n4️⃣ 测试本地完整版")
    print("-" * 40)
    result4 = test_complete_standalone()
    test_results.append(("本地完整版", result4))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关组件")
        return False

if __name__ == '__main__':
    try:
        success = main()
        if success:
            print("\n✅ 自动测试完成 - 系统正常")
        else:
            print("\n❌ 自动测试完成 - 发现问题")
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
