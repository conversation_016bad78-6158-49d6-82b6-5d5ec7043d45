#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试评估模式的脚本
"""

import requests
import json
import pandas as pd
import numpy as np
import time

def test_evaluation_mode():
    """测试评估模式"""
    base_url = "http://127.0.0.1:5000"
    
    print("🧪 测试评估模式")
    print("=" * 60)
    
    # 1. 测试模式信息接口
    print("1️⃣ 测试模式信息接口")
    try:
        response = requests.get(f"{base_url}/api/modes", timeout=5)
        if response.status_code == 200:
            modes_data = response.json()
            print("✅ 模式信息获取成功")
            if 'evaluation' in modes_data['modes']:
                eval_mode = modes_data['modes']['evaluation']
                print(f"   评估模式: {eval_mode['name']}")
                print(f"   描述: {eval_mode['description']}")
                print(f"   评估器可用: {modes_data['model_evaluator_available']}")
            else:
                print("❌ 评估模式未找到")
                return False
        else:
            print(f"❌ 模式信息获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 模式信息接口测试失败: {e}")
        return False
    
    # 2. 准备评估测试数据
    print(f"\n2️⃣ 准备评估测试数据")
    
    # 创建包含目标值的测试数据
    np.random.seed(42)  # 确保结果可重现
    
    # 基础特征数据
    test_data = []
    true_targets = []
    
    for i in range(10):
        # 生成特征数据
        features = {
            "外径": 88.9 + np.random.normal(0, 5),
            "壁厚": 18.6 + np.random.normal(0, 2),
            "软抱紧力2": 10 + np.random.normal(0, 1),
            "软抱紧力3": 10 + np.random.normal(0, 1),
            "上辊冲击电流": 800 + np.random.normal(0, 50),
            "上辊平均电流": 750 + np.random.normal(0, 30),
            "下辊冲击电流": 800 + np.random.normal(0, 50),
            "下辊平均电流": 750 + np.random.normal(0, 30),
            "倾角": 10 + np.random.normal(0, 1),
            "准备时间": 6.3 + np.random.normal(0, 0.5)
        }
        
        # 生成目标值（模拟真实的硬度值）
        target_value = 25 + np.random.normal(0, 2)
        features["硬度_油管硬度平均值"] = target_value
        
        test_data.append(features)
        true_targets.append(target_value)
    
    print(f"   生成测试数据: {len(test_data)} 条记录")
    print(f"   目标值范围: {min(true_targets):.2f} - {max(true_targets):.2f}")
    
    # 3. 测试评估模式
    print(f"\n3️⃣ 测试评估模式")
    
    # 3.1 评估模式 + 完整数据
    print("   3.1 评估模式 + 完整数据")
    try:
        payload = {
            "stage": "stage_1",
            "model_name": "硬度_油管硬度平均值",
            "mode": "evaluation",
            "target_column": "硬度_油管硬度平均值",
            "data": test_data
        }
        
        response = requests.post(f"{base_url}/api/predict", 
                               json=payload, timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 评估模式测试成功")
            print(f"      预测模式: {result.get('prediction_mode')}")
            print(f"      处理行数: {result.get('processed_rows')}")
            
            # 显示评估结果
            evaluation = result.get('evaluation')
            if evaluation and 'summary' in evaluation:
                summary = evaluation['summary']
                print(f"      评估状态: {summary['status']}")
                print(f"      有效样本: {summary['valid_samples']}")
                
                if summary['status'] == 'success':
                    metrics = summary['key_metrics']
                    print(f"      R²: {metrics['R²']}")
                    print(f"      RMSE: {metrics['RMSE']}")
                    print(f"      MAE: {metrics['MAE']}")
                    if metrics['MAPE(%)']:
                        print(f"      MAPE: {metrics['MAPE(%)']}%")
                    
                    grade = summary['performance_grade']
                    print(f"      性能等级: {grade['description']}")
                    
                    quality = summary.get('prediction_quality', [])
                    if quality:
                        print(f"      质量评估: {', '.join(quality)}")
            else:
                print(f"      评估失败: {evaluation.get('error', '未知错误')}")
                
        else:
            result = response.json()
            print(f"   ❌ 评估模式测试失败")
            print(f"      错误: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ 评估模式测试失败: {e}")
    
    # 3.2 评估模式 + 缺少目标列
    print("   3.2 评估模式 + 缺少目标列")
    try:
        # 移除目标列的数据
        data_without_target = []
        for item in test_data:
            item_copy = item.copy()
            if "硬度_油管硬度平均值" in item_copy:
                del item_copy["硬度_油管硬度平均值"]
            data_without_target.append(item_copy)
        
        payload = {
            "stage": "stage_1",
            "model_name": "硬度_油管硬度平均值",
            "mode": "evaluation",
            "target_column": "硬度_油管硬度平均值",
            "data": data_without_target
        }
        
        response = requests.post(f"{base_url}/api/predict", 
                               json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("   ⚠️ 评估模式 + 缺少目标列：意外成功")
        else:
            result = response.json()
            print("   ✅ 评估模式 + 缺少目标列：正确报错")
            print(f"      错误: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ 评估模式 + 缺少目标列测试失败: {e}")
    
    # 3.3 评估模式 + 未指定目标列
    print("   3.3 评估模式 + 未指定目标列")
    try:
        payload = {
            "stage": "stage_1",
            "model_name": "硬度_油管硬度平均值",
            "mode": "evaluation",
            "data": test_data
            # 故意不包含 target_column
        }
        
        response = requests.post(f"{base_url}/api/predict", 
                               json=payload, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            print("   ⚠️ 评估模式 + 未指定目标列：意外成功")
        else:
            result = response.json()
            print("   ✅ 评估模式 + 未指定目标列：正确报错")
            print(f"      错误: {result.get('error')}")
    except Exception as e:
        print(f"   ❌ 评估模式 + 未指定目标列测试失败: {e}")
    
    # 4. 对比不同模式
    print(f"\n4️⃣ 对比不同模式")
    
    modes_to_test = ['strict', 'lenient', 'evaluation']
    for mode in modes_to_test:
        print(f"   4.{modes_to_test.index(mode)+1} {mode}模式")
        try:
            payload = {
                "stage": "stage_1",
                "model_name": "硬度_油管硬度平均值",
                "mode": mode,
                "data": test_data[:3]  # 只用前3条数据
            }
            
            if mode == 'evaluation':
                payload["target_column"] = "硬度_油管硬度平均值"
            
            response = requests.post(f"{base_url}/api/predict", 
                                   json=payload, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"      ✅ {mode}模式成功")
                print(f"         处理行数: {result.get('processed_rows')}")
                
                if mode == 'evaluation' and 'evaluation' in result:
                    eval_result = result['evaluation']
                    if 'summary' in eval_result:
                        r2 = eval_result['summary']['key_metrics']['R²']
                        print(f"         R²: {r2}")
            else:
                result = response.json()
                print(f"      ❌ {mode}模式失败: {result.get('error')}")
                
        except Exception as e:
            print(f"      ❌ {mode}模式测试失败: {e}")
    
    print(f"\n{'='*60}")
    print("🎉 评估模式测试完成！")
    return True

def main():
    """主函数"""
    print("🚀 启动评估模式测试...")
    
    # 等待服务器启动
    print("⏳ 等待服务器启动...")
    time.sleep(2)
    
    # 检查服务器是否运行
    try:
        response = requests.get("http://127.0.0.1:5000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
        else:
            print("❌ 服务器响应异常")
            return
    except:
        print("❌ 无法连接到服务器，请先启动app.py")
        return
    
    # 运行测试
    test_evaluation_mode()

if __name__ == '__main__':
    main()
