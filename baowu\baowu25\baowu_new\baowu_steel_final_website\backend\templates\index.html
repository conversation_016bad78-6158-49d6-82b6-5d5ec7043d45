<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf-autotable/3.5.31/jspdf.plugin.autotable.min.js"></script>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/enhanced_styles.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">

</head>
<body>
    <div class="container">
        <div class="header">
            <div class="system-logo">
                <div class="logo-icon">🔬</div>
                <div class="system-info">
                    <h1>耐应力腐蚀油套管智能预测系统</h1>
                    <p class="system-subtitle">Machine Learning-Based Stress Corrosion Resistant Casing Prediction System</p>
                    <div class="system-badges">
                        <span class="badge badge-primary">机器学习</span>
                        <span class="badge badge-secondary">质量预测</span>
                        <span class="badge badge-success">宝武钢铁</span>
                    </div>
                </div>
            </div>
            <div class="header-actions">
                <button id="guideBtn" class="btn btn-guide">📖 操作引导</button>
                <button id="helpBtn" class="btn btn-help">❓ 帮助</button>
            </div>
        </div>

        <!-- 步骤1: 模型选择 -->
        <div class="step">
            <div class="step-header">
                <div class="step-indicator">
                    <span class="step-number">1</span>
                    <div class="step-line"></div>
                </div>
                <div class="step-content">
                    <h3>预测模型选择</h3>
                    <p class="step-description">选择预测模型，系统将自动确定目标列。共有5个阶段18个专业预测模型可选</p>
                </div>
            </div>
            <div id="modelLoading" class="info-box">
                <span class="loading"></span>
                正在连接后端服务并加载模型信息...
            </div>
            <div id="modelSelection" class="model-grid hidden"></div>
            <div id="selectedModelInfo" class="success-box hidden">
                <strong>✅ 已选择模型：</strong><span id="selectedModelName"></span><br>
                <strong>🎯 目标列：</strong><span id="selectedTargetColumn"></span>
            </div>
        </div>

        <!-- 步骤2: 预测模式选择 -->
        <div class="step">
            <div class="step-header">
                <div class="step-indicator">
                    <span class="step-number">2</span>
                    <div class="step-line"></div>
                </div>
                <div class="step-content">
                    <h3>预测模式选择</h3>
                    <p class="step-description">选择适合您数据情况的预测模式，系统提供三种不同的处理方式</p>
                </div>
            </div>
            <div id="modeLoading" class="info-box">
                <span class="loading"></span>
                正在加载预测模式信息...
            </div>
            <div id="modeSelection" class="mode-grid hidden">
                <!-- 模式选择将通过JavaScript动态加载 -->
            </div>
            <div id="selectedModeInfo" class="success-box hidden">
                <strong>✅ 已选择模式：</strong><span id="selectedModeName"></span>
                <div id="modeDescription" class="mode-description"></div>
            </div>
        </div>

        <!-- 步骤3: 文件上传 -->
        <div class="step">
            <div class="step-header">
                <div class="step-indicator">
                    <span class="step-number">3</span>
                    <div class="step-line"></div>
                </div>
                <div class="step-content">
                    <h3>工艺数据上传</h3>
                    <p class="step-description">上传包含工艺参数的结构化数据文件，支持CSV和Excel格式</p>
                </div>
            </div>
            <div class="file-upload" id="fileUploadArea">
                <input type="file" id="fileInput" accept=".csv,.xlsx,.xls" multiple>
                <label for="fileInput" class="file-upload-label">
                    <div class="upload-icon">📁</div>
                    <div class="upload-text">
                        <strong>拖拽文件到此处或点击选择</strong><br>
                        <small>支持 CSV, Excel (.xlsx, .xls) 格式，最大50MB</small>
                    </div>
                </label>
            </div>
            <div class="test-data-section">
                <button id="showTestDataBtn" class="btn btn-secondary">📥 下载测试数据</button>
                <div id="testDataList" class="test-data-list hidden"></div>
            </div>
            <div id="fileInfo" class="info-box hidden">
                <strong>📄 文件信息：</strong><span id="fileDetails"></span>
            </div>
            <div id="dataValidation" class="hidden"></div>
        </div>

        <!-- 步骤4: 数据预览 -->
        <div class="step">
            <div class="step-header">
                <div class="step-indicator">
                    <span class="step-number">4</span>
                    <div class="step-line"></div>
                </div>
                <div class="step-content">
                    <h3>数据质量验证</h3>
                    <p class="step-description">预览数据内容并验证数据质量，检查是否包含所需的目标列（评估模式）</p>
                </div>
            </div>
            <button id="previewBtn" class="btn" disabled>🔍 预览数据</button>
            <div id="dataStats" class="hidden"></div>
            <div id="dataPreview" class="data-preview hidden"></div>
        </div>

        <!-- 步骤5: 执行预测 -->
        <div class="step">
            <div class="step-header">
                <div class="step-indicator">
                    <span class="step-number">5</span>
                    <div class="step-line"></div>
                </div>
                <div class="step-content">
                    <h3>智能预测分析</h3>
                    <p class="step-description">使用已选择的模型对数据进行预测分析，生成专业的质量预测结果</p>
                </div>
            </div>
            <div class="prediction-info">
                <div class="info-card">
                    <div class="info-icon">🤖</div>
                    <div class="info-text">
                        <strong>AI算法</strong>
                        <p>基于AutoGluon框架的集成学习模型</p>
                    </div>
                </div>
                <div class="info-card">
                    <div class="info-icon">⚡</div>
                    <div class="info-text">
                        <strong>实时处理</strong>
                        <p>支持批量数据的快速预测分析</p>
                    </div>
                </div>
                <div class="info-card">
                    <div class="info-icon">🎯</div>
                    <div class="info-text">
                        <strong>高精度</strong>
                        <p>经过大量工业数据训练优化</p>
                    </div>
                </div>
            </div>
            <button id="predictBtn" class="btn btn-primary">🚀 开始预测</button>
            <div id="predictionProgress" class="hidden">
                <div class="warning-box">
                    <span class="loading"></span>
                    正在执行预测，请稍候...
                </div>
            </div>
            <div id="predictionResults" class="prediction-results hidden"></div>
        </div>

        <!-- 步骤6: 结果下载与报告生成 -->
        <div class="step">
            <div class="step-header">
                <div class="step-indicator">
                    <span class="step-number">6</span>
                </div>
                <div class="step-content">
                    <h3>结果导出与报告</h3>
                    <p class="step-description">导出预测结果数据并生成专业的质量分析报告</p>
                </div>
            </div>
            <div class="export-options">
                <button id="downloadExcelBtn" class="btn btn-export" disabled>
                    <span class="btn-icon">📊</span>
                    <span class="btn-text">Excel数据表</span>
                </button>
                <button id="downloadCSVBtn" class="btn btn-export" disabled>
                    <span class="btn-icon">📄</span>
                    <span class="btn-text">CSV格式</span>
                </button>
                <button id="generateReportBtn" class="btn btn-report" disabled>
                    <span class="btn-icon">📈</span>
                    <span class="btn-text">分析报告</span>
                </button>
            </div>
            <div id="reportProgress" class="hidden">
                <div class="warning-box">
                    <span class="loading"></span>
                    正在生成分析报告，请稍候...
                </div>
            </div>
        </div>
    </div>

    <!-- 后端状态指示器 -->
    <div id="backendStatus" class="backend-status offline">🔴 检查中...</div>

    <!-- 报告预览模态框 -->
    <div id="reportPreview" class="report-preview hidden">
        <div class="report-preview-content">
            <div class="report-preview-header">
                <h2>预测分析报告预览</h2>
                <button class="btn" onclick="downloadPDFReport()">📥 下载PDF报告</button>
            </div>
            <button class="close-preview" onclick="closeReportPreview()">&times;</button>
            <div id="reportContent" class="report-preview-body">
                <!-- 报告内容将动态生成 -->
            </div>
        </div>
    </div>

    <!-- 引导模态框 -->
    <div id="guideModal" class="guide-modal hidden">
        <div class="guide-modal-content">
            <div class="guide-modal-header">
                <h2>🎯 系统操作引导</h2>
                <button class="close-guide" onclick="closeGuideModal()">&times;</button>
            </div>
            <div class="guide-modal-body">
                <div class="guide-welcome">
                    <h3>欢迎使用耐应力腐蚀油套管质量预测系统！</h3>
                    <p>本系统基于先进的机器学习算法，为您提供专业的油套管质量预测服务。</p>
                    <div class="guide-options">
                        <button id="startGuideBtn" class="btn btn-primary">🚀 开始引导</button>
                        <button id="skipGuideBtn" class="btn btn-secondary">⏭️ 跳过引导</button>
                    </div>
                </div>
                <div id="guideSteps" class="guide-steps hidden">
                    <!-- 引导步骤将动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <!-- 引导高亮遮罩 -->
    <div id="guideOverlay" class="guide-overlay hidden"></div>

    <script>
        // 全局配置
        const CONFIG = {
            API_BASE_URL: window.location.origin, // 使用当前域名和端口
            MAX_PREVIEW_ROWS: 10,
            NOTIFICATION_DURATION: 3000,
            FILE_SIZE_LIMIT: 50 * 1024 * 1024,
            REQUEST_TIMEOUT: 30000
        };

        // 全局状态管理
        const AppState = {
            uploadedData: null,
            selectedModel: null,
            selectedStage: null,
            selectedMode: 'lenient', // 默认宽松模式
            targetColumn: null, // 评估模式的目标列
            predictionResults: null,
            fileName: '',
            availableModels: [],
            availableModes: [],
            currentModelFeatures: [],
            dataColumns: [],
            isProcessing: false,
            chartInstance: null,
            backendOnline: false,
            reportCharts: {}, // 存储报告中的图表实例
            modelStageDescriptions: {}, // 存储模型阶段描述

            // 预测按钮控制变量
            canPredict: false // 简单的全局变量控制预测按钮
        };

        // 工具函数
        const Utils = {
            formatFileSize: (bytes) => {
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                if (bytes === 0) return '0 Byte';
                const i = Math.floor(Math.log(bytes) / Math.log(1024));
                return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
            },

            formatNumber: (num, decimals = 2) => {
                return typeof num === 'number' ? num.toFixed(decimals) : num;
            },

            showNotification: (message, type = 'info') => {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.innerHTML = message;
                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.style.opacity = '0';
                    notification.style.transform = 'translateX(100px)';
                    setTimeout(() => notification.remove(), 300);
                }, CONFIG.NOTIFICATION_DURATION);
            },

            debounce: (func, wait) => {
                let timeout;
                return function executedFunction(...args) {
                    const later = () => {
                        clearTimeout(timeout);
                        func(...args);
                    };
                    clearTimeout(timeout);
                    timeout = setTimeout(later, wait);
                };
            },

            getCurrentDateTime: () => {
                const now = new Date();
                return now.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            },

            calculateSkewness: (data) => {
                const n = data.length;
                const mean = data.reduce((a, b) => a + b, 0) / n;
                const stdDev = Math.sqrt(data.reduce((sum, x) => sum + Math.pow(x - mean, 2), 0) / n);
                const skewness = data.reduce((sum, x) => sum + Math.pow((x - mean) / stdDev, 3), 0) / n;
                return skewness;
            },

            calculateKurtosis: (data) => {
                const n = data.length;
                const mean = data.reduce((a, b) => a + b, 0) / n;
                const stdDev = Math.sqrt(data.reduce((sum, x) => sum + Math.pow(x - mean, 2), 0) / n);
                const kurtosis = data.reduce((sum, x) => sum + Math.pow((x - mean) / stdDev, 4), 0) / n - 3;
                return kurtosis;
            }
        };

        // 改进的API服务
        const ApiService = {
            async request(url, options = {}) {
                const fullUrl = `${CONFIG.API_BASE_URL}${url}`;
                console.log(`🌐 API请求: ${options.method || 'GET'} ${fullUrl}`);

                try {
                    const controller = new AbortController();
                    const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

                    const requestOptions = {
                        mode: 'cors',
                        credentials: 'omit',
                        ...options,
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            ...options.headers
                        },
                        signal: controller.signal
                    };

                    const response = await fetch(fullUrl, requestOptions);
                    clearTimeout(timeoutId);

                    console.log(`📡 响应状态: ${response.status} ${response.statusText}`);

                    if (!response.ok) {
                        let errorMessage;
                        try {
                            const errorData = await response.json();
                            errorMessage = errorData.error || errorData.message || `HTTP ${response.status}`;
                        } catch {
                            errorMessage = `HTTP ${response.status}: ${response.statusText}`;
                        }
                        throw new Error(errorMessage);
                    }

                    const data = await response.json();
                    console.log('✅ API响应成功');
                    return data;

                } catch (error) {
                    console.error('❌ API请求失败:', error);

                    if (error.name === 'AbortError') {
                        throw new Error('请求超时，请检查网络连接或后端服务状态');
                    } else if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
                        throw new Error(`无法连接到后端服务 ${CONFIG.API_BASE_URL}`);
                    }

                    throw error;
                }
            },

            checkHealth: () => ApiService.request('/health'),
            getModels: () => ApiService.request('/api/models'),
            getModelInfo: (stage, modelName) => ApiService.request(`/api/model_info/${stage}/${modelName}`),
            validateData: (stage, modelName, data) => ApiService.request('/api/validate_data', {
                method: 'POST',
                body: JSON.stringify({ stage, model_name: modelName, data })
            }),
            predict: (stage, modelName, data, mode = 'lenient', targetColumn = null) => {
                const requestData = {
                    stage,
                    model_name: modelName,
                    data,
                    mode
                };

                // 评估模式需要目标列
                if (mode === 'evaluation' && targetColumn) {
                    requestData.target_column = targetColumn;
                }

                return ApiService.request('/api/predict', {
                    method: 'POST',
                    body: JSON.stringify(requestData)
                });
            },
            getGuideInfo: () => ApiService.request('/api/guide'),
            getTestDataList: () => ApiService.request('/api/test_data_list')
        };

        // 后端状态检查
        async function checkBackendStatus() {
            const statusElement = document.getElementById('backendStatus');
            
            try {
                const health = await ApiService.checkHealth();
                if (health.success) {
                    AppState.backendOnline = true;
                    statusElement.className = 'backend-status online';
                    statusElement.innerHTML = '🟢 后端在线';
                    statusElement.title = '后端服务连接正常';
                    return true;
                }
            } catch (error) {
                console.log('后端连接失败:', error.message);
            }

            AppState.backendOnline = false;
            statusElement.className = 'backend-status offline';
            statusElement.innerHTML = '🔴 后端离线';
            statusElement.title = '点击启用指导模式';
            statusElement.onclick = enableDemoMode;
            return false;
        }

        // 启用指导模式
        function enableDemoMode() {
            const statusElement = document.getElementById('backendStatus');
            statusElement.className = 'backend-status demo';
            statusElement.innerHTML = '📋 指导模式';
            statusElement.onclick = null;

            // 显示操作指导
            showOperationGuide();

            const modelLoading = document.getElementById('modelLoading');
            modelLoading.innerHTML = `
                <div class="info-box">
                    <h4>📋 操作指导模式</h4>
                    <p>后端服务未连接，系统将为您提供详细的操作指导。</p>
                    <p>点击下方按钮查看完整的操作步骤说明。</p>
                    <button class="btn btn-primary" onclick="showOperationGuide()">📖 查看操作指导</button>
                    <p style="margin-top: 15px;"><strong>要启用完整功能，请启动后端服务：</strong></p>
                    <code>python app.py</code>
                </div>
            `;

            Utils.showNotification('已启用指导模式', 'info');
        }

        // 显示操作指导
        function showOperationGuide() {
            const guideModal = document.getElementById('guideModal');
            const guideSteps = document.getElementById('guideSteps');

            // 操作步骤数据
            const operationSteps = [
                {
                    title: "步骤1：预测模型选择",
                    content: `
                        <div class="step-content">
                            <h4>🎯 选择预测模型</h4>
                            <p><strong>操作说明：</strong></p>
                            <ul>
                                <li>系统提供5个阶段共18个专业预测模型</li>
                                <li>点击任意模型卡片进行选择</li>
                                <li>推荐模型：<code>硬度_油井管硬度极差</code></li>
                                <li>选择后系统会自动设置目标列</li>
                            </ul>
                            <div class="example-box">
                                <strong>示例：</strong>选择 "stage_1 / 硬度_油井管硬度极差"<br>
                                <strong>结果：</strong>✅ 已选择模型 🎯 目标列：硬度_油井管硬度极差
                            </div>
                        </div>
                    `
                },
                {
                    title: "步骤2：预测模式选择",
                    content: `
                        <div class="step-content">
                            <h4>⚙️ 选择预测模式</h4>
                            <p><strong>三种模式说明：</strong></p>
                            <ul>
                                <li><strong>严格模式：</strong>高精度预测，适合关键质量控制</li>
                                <li><strong>宽松模式：</strong>快速预测，适合初步质量评估</li>
                                <li><strong>评估模式：</strong>模型性能评估，需要包含目标值的数据</li>
                            </ul>
                            <div class="example-box">
                                <strong>推荐：</strong>新用户选择 "宽松模式"<br>
                                <strong>说明：</strong>平衡了预测精度和处理速度
                            </div>
                        </div>
                    `
                },
                {
                    title: "步骤3：数据文件上传",
                    content: `
                        <div class="step-content">
                            <h4>📁 上传数据文件</h4>
                            <p><strong>支持格式：</strong></p>
                            <ul>
                                <li>CSV文件 (.csv)</li>
                                <li>Excel文件 (.xlsx, .xls)</li>
                                <li>文件大小限制：50MB</li>
                            </ul>
                            <p><strong>数据要求：</strong></p>
                            <ul>
                                <li>包含化学成分数据：C, Si, Mn, P, S, Cr, Ni, Mo等</li>
                                <li>包含工艺参数：加热温度、保温时间、冷却速度等</li>
                                <li>评估模式需包含目标列数据</li>
                            </ul>
                            <div class="example-box">
                                <strong>操作：</strong>拖拽文件到上传区域或点击选择文件<br>
                                <strong>结果：</strong>显示数据预览和统计信息
                            </div>
                        </div>
                    `
                },
                {
                    title: "步骤4：数据质量验证",
                    content: `
                        <div class="step-content">
                            <h4>✅ 数据质量检查</h4>
                            <p><strong>系统会自动检查：</strong></p>
                            <ul>
                                <li>数据完整性：是否有缺失值</li>
                                <li>数据格式：数值类型是否正确</li>
                                <li>目标列存在性（评估模式）</li>
                                <li>数据行数和列数统计</li>
                            </ul>
                            <div class="example-box">
                                <strong>成功提示：</strong>✅ 数据质量验证通过！<br>
                                <strong>显示：</strong>数据包含 X 行，Y 列 🚀 预测准备就绪！
                            </div>
                        </div>
                    `
                },
                {
                    title: "步骤5：智能预测分析",
                    content: `
                        <div class="step-content">
                            <h4>🚀 执行预测分析</h4>
                            <p><strong>预测过程：</strong></p>
                            <ul>
                                <li>点击 "🚀 开始预测" 按钮</li>
                                <li>系统使用已选择的模型进行预测</li>
                                <li>无需重新选择模型或参数</li>
                                <li>预测完成后显示结果统计</li>
                            </ul>
                            <div class="example-box">
                                <strong>注意：</strong>预测按钮在完成前4步后自动启用<br>
                                <strong>结果：</strong>显示预测值分布图和详细统计
                            </div>
                        </div>
                    `
                },
                {
                    title: "步骤6：结果导出与报告",
                    content: `
                        <div class="step-content">
                            <h4>📊 导出预测结果</h4>
                            <p><strong>导出选项：</strong></p>
                            <ul>
                                <li><strong>Excel格式：</strong>包含原始数据和预测结果</li>
                                <li><strong>CSV格式：</strong>纯数据格式，便于后续处理</li>
                                <li><strong>专业报告：</strong>包含图表和统计分析的完整报告</li>
                            </ul>
                            <div class="example-box">
                                <strong>操作：</strong>点击对应的导出按钮<br>
                                <strong>内容：</strong>序号、预测值、原始特征数据、统计汇总
                            </div>
                        </div>
                    `
                }
            ];

            // 显示指导步骤
            displayGuideSteps(operationSteps);
            guideModal.classList.remove('hidden');
        }

        // 显示指导步骤
        function displayGuideSteps(steps) {
            const guideSteps = document.getElementById('guideSteps');
            let currentStep = 0;

            function showStep(stepIndex) {
                if (stepIndex < 0 || stepIndex >= steps.length) return;

                const step = steps[stepIndex];
                guideSteps.innerHTML = `
                    <div class="guide-step">
                        <div class="step-header">
                            <span class="step-number">${stepIndex + 1}/${steps.length}</span>
                            <h3>${step.title}</h3>
                        </div>
                        <div class="step-body">
                            ${step.content}
                        </div>
                    </div>
                `;

                // 更新按钮状态
                const prevBtn = document.getElementById('prevStep');
                const nextBtn = document.getElementById('nextStep');
                const finishBtn = document.getElementById('finishGuide');

                prevBtn.disabled = stepIndex === 0;
                nextBtn.style.display = stepIndex === steps.length - 1 ? 'none' : 'inline-block';
                finishBtn.style.display = stepIndex === steps.length - 1 ? 'inline-block' : 'none';

                currentStep = stepIndex;
            }

            // 初始化按钮事件
            document.getElementById('prevStep').onclick = () => showStep(currentStep - 1);
            document.getElementById('nextStep').onclick = () => showStep(currentStep + 1);
            document.getElementById('finishGuide').onclick = () => {
                document.getElementById('guideModal').classList.add('hidden');
                Utils.showNotification('操作指导已完成，您可以按照步骤进行操作', 'success');
            };
            document.getElementById('closeGuide').onclick = () => {
                document.getElementById('guideModal').classList.add('hidden');
            };

            // 显示第一步
            showStep(0);
        }

        // 模式选择功能
        function initializeModeSelection() {
            console.log('🎯 初始化模式选择功能...');
        }

        async function loadModes() {
            console.log('📊 加载预测模式...');
            const modeLoading = document.getElementById('modeLoading');
            const modeSelection = document.getElementById('modeSelection');

            try {
                const response = await fetch(`${CONFIG.API_BASE_URL}/api/modes`, {
                    timeout: CONFIG.REQUEST_TIMEOUT
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();

                if (data.success && data.modes) {
                    AppState.availableModes = data.modes;
                    renderModeSelection(data.modes, data.default_mode);

                    // 隐藏加载提示，显示模式选择
                    modeLoading.classList.add('hidden');
                    modeSelection.classList.remove('hidden');

                    console.log('✅ 预测模式加载成功');
                } else {
                    throw new Error(data.error || '模式数据格式错误');
                }
            } catch (error) {
                console.error('❌ 加载预测模式失败:', error);
                modeLoading.innerHTML = `
                    <div class="error-box">
                        <strong>⚠️ 模式加载失败</strong><br>
                        ${error.message}<br>
                        <small>将使用默认宽松模式</small>
                    </div>
                `;

                // 使用默认模式
                AppState.selectedMode = 'lenient';
                renderDefaultMode();
            }
        }

        function renderModeSelection(modes, defaultMode) {
            const modeSelection = document.getElementById('modeSelection');

            const modeCards = Object.entries(modes).map(([modeKey, modeInfo]) => {
                const isDefault = modeKey === defaultMode;
                const badgeClass = modeKey === 'strict' ? 'strict' :
                                 modeKey === 'evaluation' ? 'evaluation' : 'default';

                return `
                    <div class="mode-card ${isDefault ? 'selected' : ''}"
                         data-mode="${modeKey}"
                         onclick="selectMode('${modeKey}')">
                        <div class="mode-badge ${badgeClass}">
                            ${isDefault ? '默认' : modeKey === 'strict' ? '严格' :
                              modeKey === 'evaluation' ? '评估' : '推荐'}
                        </div>
                        <div class="mode-header">
                            <div class="mode-icon">
                                ${modeKey === 'strict' ? '🔒' :
                                  modeKey === 'lenient' ? '🔧' : '📊'}
                            </div>
                            <h4 class="mode-title">${modeInfo.name}</h4>
                        </div>
                        <p class="mode-description">${modeInfo.description}</p>
                        <ul class="mode-features">
                            ${modeInfo.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                    </div>
                `;
            }).join('');

            modeSelection.innerHTML = modeCards;

            // 设置默认选择
            AppState.selectedMode = defaultMode;
            updateSelectedModeInfo(modes[defaultMode], defaultMode);
        }

        function renderDefaultMode() {
            const modeSelection = document.getElementById('modeSelection');
            modeSelection.innerHTML = `
                <div class="mode-card selected" data-mode="lenient">
                    <div class="mode-badge default">默认</div>
                    <div class="mode-header">
                        <div class="mode-icon">🔧</div>
                        <h4 class="mode-title">宽松模式</h4>
                    </div>
                    <p class="mode-description">自动填充缺失特征，基于统计数据智能处理</p>
                    <ul class="mode-features">
                        <li>自动填充缺失特征</li>
                        <li>基于真实数据统计</li>
                        <li>提供填充报告</li>
                        <li>用户友好体验</li>
                    </ul>
                </div>
            `;
            modeSelection.classList.remove('hidden');
        }

        function selectMode(modeKey) {
            // 更新选择状态
            document.querySelectorAll('.mode-card').forEach(card => {
                card.classList.remove('selected');
            });

            const selectedCard = document.querySelector(`[data-mode="${modeKey}"]`);
            if (selectedCard) {
                selectedCard.classList.add('selected');
            }

            // 更新应用状态
            AppState.selectedMode = modeKey;

            // 更新选择信息显示
            const modeInfo = AppState.availableModes[modeKey];
            if (modeInfo) {
                updateSelectedModeInfo(modeInfo, modeKey);
            }

            // 显示/隐藏评估模式特殊输入
            toggleEvaluationInputs(modeKey === 'evaluation');

            // 如果已有数据和模型，重新验证
            if (AppState.uploadedData && AppState.selectedModel) {
                if (AppState.backendOnline) {
                    validateDataCompatibility();
                } else {
                    validateDataInDemoMode();
                }
            }

            // 检查预测按钮状态
            checkPredictionReadiness();

            console.log(`🎯 已选择预测模式: ${modeKey}`);
        }

        function updateSelectedModeInfo(modeInfo, modeKey) {
            const selectedModeInfo = document.getElementById('selectedModeInfo');
            const selectedModeName = document.getElementById('selectedModeName');
            const modeDescription = document.getElementById('modeDescription');

            selectedModeName.textContent = modeInfo.name;
            modeDescription.textContent = modeInfo.description;
            selectedModeInfo.classList.remove('hidden');
        }

        function toggleEvaluationInputs(show) {
            let evaluationInputs = document.getElementById('evaluationInputs');

            if (show && !evaluationInputs) {
                // 创建评估模式说明区域
                const inputsHtml = `
                    <div id="evaluationInputs" class="evaluation-inputs">
                        <div class="info-box">
                            <strong>📊 评估模式说明：</strong><br>
                            • 系统将自动使用所选模型的目标值进行评估<br>
                            • 请确保上传的数据包含对应的目标值列<br>
                            • 评估将计算R²、RMSE、MAE、MAPE等专业指标
                        </div>
                        <div id="targetColumnInfo" class="success-box hidden">
                            <strong>🎯 目标列：</strong><span id="targetColumnName"></span>
                        </div>
                    </div>
                `;

                const modeSelection = document.getElementById('modeSelection');
                modeSelection.insertAdjacentHTML('afterend', inputsHtml);
                evaluationInputs = document.getElementById('evaluationInputs');
            }

            if (evaluationInputs) {
                if (show) {
                    evaluationInputs.classList.remove('hidden');
                    updateTargetColumnInfo();
                } else {
                    evaluationInputs.classList.add('hidden');
                }
            }
        }

        function updateTargetColumnInfo() {
            // 目标列已在模型选择时确定，这里只需要更新显示
            if (AppState.targetColumn) {
                const targetColumnInfo = document.getElementById('targetColumnInfo');
                const targetColumnName = document.getElementById('targetColumnName');

                if (targetColumnInfo && targetColumnName) {
                    targetColumnName.textContent = AppState.targetColumn;
                    targetColumnInfo.classList.remove('hidden');
                }

                console.log(`📊 评估模式目标列: ${AppState.targetColumn}`);
            }
        }

        function getSelectedModelInfo() {
            if (!AppState.selectedModel || !AppState.selectedStage || !AppState.availableModels) {
                return null;
            }

            // 在availableModels中查找选中的模型信息
            for (const stage of AppState.availableModels) {
                if (stage.stage === AppState.selectedStage) {
                    for (const model of stage.models) {
                        if (model.name === AppState.selectedModel) {
                            return model;
                        }
                    }
                }
            }
            return null;
        }

        // 系统初始化
        async function initializeSystem() {
            console.log('🚀 系统初始化开始...');
            
            const modelLoading = document.getElementById('modelLoading');
            
            // 检查后端状态
            const isOnline = await checkBackendStatus();
            
            if (isOnline) {
                try {
                    // 加载预测模式
                    await loadModes();

                    modelLoading.innerHTML = `
                        <div class="success-box">
                            <span class="loading"></span>
                            后端连接成功，正在加载模型列表...
                        </div>
                    `;

                    AppState.availableModels = await ApiService.getModels();
                    console.log('✅ 成功获取模型列表:', AppState.availableModels);

                    // 存储模型阶段描述
                    AppState.availableModels.forEach(stage => {
                        AppState.modelStageDescriptions[stage.stage] = stage.stage_desc;
                    });

                    renderModelSelection();
                    Utils.showNotification('🎉 系统初始化成功', 'success');

                } catch (error) {
                    console.error('❌ 模型加载失败:', error);
                    modelLoading.innerHTML = `
                        <div class="error-box">
                            <h4>❌ 模型加载失败</h4>
                            <p><strong>错误:</strong> ${error.message}</p>
                            <button class="btn" onclick="enableDemoMode()">启用指导模式</button>
                        </div>
                    `;
                    Utils.showNotification(`模型加载失败: ${error.message}`, 'error');
                }
            } else {
                modelLoading.innerHTML = `
                    <div class="error-box">
                        <h4>🔌 后端服务未连接</h4>
                        <p><strong>解决方案:</strong></p>
                        <ul>
                            <li>启动后端服务: <code>python app.py</code></li>
                            <li>确认端口5000未被占用</li>
                            <li>检查防火墙设置</li>
                        </ul>
                        <button class="btn" onclick="enableDemoMode()">启用指导模式</button>
                        <button class="btn" onclick="location.reload()">重新连接</button>
                    </div>
                `;
            }
        }

        // 渲染模型选择界面
        function renderModelSelection() {
            const modelSelection = document.getElementById('modelSelection');
            const modelLoading = document.getElementById('modelLoading');
            
            modelLoading.classList.add('hidden');
            modelSelection.classList.remove('hidden');

            if (!AppState.availableModels || AppState.availableModels.length === 0) {
                modelSelection.innerHTML = `
                    <div class="error-box">
                        <h4>⚠️ 未找到可用模型</h4>
                        <p>后端服务中没有发现任何可用的模型。</p>
                    </div>
                `;
                return;
            }

            let html = '';
            let totalModels = 0;
            let availableCount = 0;

            AppState.availableModels.forEach(stage => {
                if (stage.models && stage.models.length > 0) {
                    html += `<div class="stage-section">
                        <h4>🔬 ${stage.stage_desc}</h4>
                        <div class="stage-models">`;
                    
                    stage.models.forEach(model => {
                        totalModels++;
                        if (model.available) availableCount++;
                        
                        const statusClass = model.available ? 'available' : 'unavailable';
                        const statusIcon = model.available ? '✅' : '❌';
                        
                        html += `
                            <div class="model-card ${statusClass}"
                                 data-model="${model.name}"
                                 data-stage="${stage.stage.replace('../models/', '')}"
                                 data-target="${model.target}"
                                 ${!model.available ? 'data-disabled="true"' : ''}>
                                <div class="model-title">
                                    ${statusIcon} ${model.name}
                                </div>
                                <div class="model-info">
                                    <div>目标变量: ${model.target}</div>
                                    <div>状态: <span style="color: ${model.available ? 'green' : 'red'}">
                                        ${model.available ? '可用' : '不可用'}
                                    </span></div>
                                </div>
                            </div>
                        `;
                    });
                    
                    html += '</div></div>';
                }
            });

            // 添加统计信息
            const statsHtml = `
                <div class="info-box" style="margin-bottom: 20px;">
                    <strong>📊 模型统计:</strong> 
                    共发现 ${totalModels} 个模型，其中 ${availableCount} 个可用
                </div>
            `;

            modelSelection.innerHTML = statsHtml + html;
            bindModelSelectionEvents();
        }

        // 绑定模型选择事件
        function bindModelSelectionEvents() {
            document.querySelectorAll('.model-card.available').forEach(card => {
                card.addEventListener('click', async function() {
                    if (AppState.isProcessing) return;
                    
                    const modelName = this.dataset.model;
                    const stageName = this.dataset.stage;
                    const targetName = this.dataset.target;
                    
                    // 移除其他选中状态
                    document.querySelectorAll('.model-card').forEach(c => 
                        c.classList.remove('selected')
                    );
                    
                    // 选中当前模型
                    this.classList.add('selected');
                    AppState.selectedModel = modelName;
                    AppState.selectedStage = stageName;

                    if (AppState.backendOnline) {
                        try {
                            AppState.isProcessing = true;
                            await loadModelInfo(stageName, modelName);
                        } catch (error) {
                            Utils.showNotification(`获取模型信息失败: ${error.message}`, 'error');
                        } finally {
                            AppState.isProcessing = false;
                        }
                    }
                    
                    // 更新UI
                    updateSelectedModelInfo(stageName, modelName, targetName);

                    // 如果是评估模式，更新目标列信息
                    if (AppState.selectedMode === 'evaluation') {
                        updateTargetColumnInfo();
                    }

                    // 模型选择后，如果有数据就启用预测
                    if (AppState.uploadedData && AppState.uploadedData.length > 0) {
                        enablePrediction('模型已选择且数据已上传');
                    } else {
                        console.log('🎯 模型已选择，等待数据上传...');
                    }
                    
                    // 如果已上传数据，验证数据质量
                    if (AppState.uploadedData && AppState.backendOnline) {
                        await validateDataCompatibility();
                    } else if (AppState.uploadedData) {
                        // 指导模式下提示用户
                        showGuideForDataValidation();
                    } else {
                        // 没有数据时，显示提示信息
                        Utils.showNotification('✅ 模型已选择，请上传数据文件', 'success');
                    }

                    // 检查预测按钮状态
                    checkPredictionReadiness();
                });
            });
        }

        // 简单的预测按钮控制函数
        function updatePredictButton() {
            const predictBtn = document.getElementById('predictBtn');
            predictBtn.disabled = !AppState.canPredict;

            console.log(`🔘 预测按钮状态更新: ${AppState.canPredict ? '启用' : '禁用'}`);
            console.log('📊 当前状态:', {
                canPredict: AppState.canPredict,
                selectedModel: AppState.selectedModel,
                selectedStage: AppState.selectedStage,
                hasData: AppState.uploadedData ? AppState.uploadedData.length : 0
            });
        }

        // 设置可以预测的状态
        function enablePrediction(reason = '') {
            AppState.canPredict = true;
            updatePredictButton();
            console.log(`✅ 预测已启用: ${reason}`);
        }

        // 禁用预测的状态
        function disablePrediction(reason = '') {
            AppState.canPredict = false;
            updatePredictButton();
            console.log(`❌ 预测已禁用: ${reason}`);
        }

        // 检查是否可以启用预测按钮
        function checkPredictionReadiness() {
            return updatePredictButton();
        }

        // 强制启用预测按钮（用户可在控制台调用）
        window.forceEnablePredictBtn = function() {
            enablePrediction('用户强制启用');
            return '✅ 预测按钮已强制启用，现在可以点击预测了！';
        };

        // 强制禁用预测按钮（用于调试）
        window.forceDisablePredictBtn = function() {
            disablePrediction('用户强制禁用');
            return '❌ 预测按钮已强制禁用';
        };

        // 逐步检查问题的调试函数
        window.stepByStepCheck = function() {
            console.log('🔍 逐步检查预测按钮问题...');

            // 步骤1：检查按钮元素
            const predictBtn = document.getElementById('predictBtn');
            console.log('1️⃣ 预测按钮元素:', {
                exists: !!predictBtn,
                disabled: predictBtn ? predictBtn.disabled : 'N/A',
                text: predictBtn ? predictBtn.textContent : 'N/A'
            });

            // 步骤2：检查AppState
            console.log('2️⃣ AppState状态:', {
                canPredict: AppState.canPredict,
                selectedModel: AppState.selectedModel,
                selectedStage: AppState.selectedStage,
                selectedMode: AppState.selectedMode,
                uploadedData: AppState.uploadedData ? `${AppState.uploadedData.length} 行` : '无',
                dataColumns: AppState.dataColumns ? `${AppState.dataColumns.length} 列` : '无'
            });

            // 步骤3：检查条件
            const hasModel = AppState.selectedModel && AppState.selectedStage;
            const hasData = AppState.uploadedData && AppState.uploadedData.length > 0;
            console.log('3️⃣ 启用条件检查:', {
                hasModel,
                hasData,
                shouldEnable: hasModel && hasData
            });

            // 步骤4：尝试手动启用
            if (hasModel && hasData) {
                console.log('4️⃣ 条件满足，尝试启用预测...');
                enablePrediction('手动检查启用');
            } else {
                console.log('4️⃣ 条件不满足，无法启用预测');
                if (!hasModel) console.log('   - 缺少模型选择');
                if (!hasData) console.log('   - 缺少数据上传');
            }

            return '检查完成，请查看控制台输出';
        };

        // 检查并修复预测按钮状态
        function checkAndFixPredictButton() {
            const predictBtn = document.getElementById('predictBtn');

            // 检查基本条件
            const hasModel = AppState.selectedModel && AppState.selectedStage;
            const hasData = AppState.uploadedData && AppState.uploadedData.length > 0;
            const hasMode = AppState.selectedMode;

            console.log('🔍 预测按钮状态检查:', {
                hasModel,
                hasData,
                hasMode,
                selectedModel: AppState.selectedModel,
                selectedStage: AppState.selectedStage,
                selectedMode: AppState.selectedMode,
                dataRows: AppState.uploadedData ? AppState.uploadedData.length : 0
            });

            // 如果用户已经选择了模型和模式，并且上传了数据，就应该启用预测按钮
            if (hasModel && hasData && hasMode) {
                // 对于评估模式，检查目标列
                if (AppState.selectedMode === 'evaluation') {
                    if (AppState.targetColumn && AppState.dataColumns && AppState.dataColumns.includes(AppState.targetColumn)) {
                        predictBtn.disabled = false;
                        console.log('✅ 评估模式：预测按钮已启用');
                    } else {
                        console.log('❌ 评估模式：目标列问题', {
                            targetColumn: AppState.targetColumn,
                            hasDataColumns: AppState.dataColumns ? AppState.dataColumns.length : 0,
                            includes: AppState.dataColumns ? AppState.dataColumns.includes(AppState.targetColumn) : false
                        });
                        // 即使目标列有问题，也给用户一个提示，让他们可以尝试
                        predictBtn.disabled = false;
                        console.log('🔧 评估模式：预测按钮已强制启用（可能需要用户检查目标列）');
                    }
                } else {
                    // 严格模式和宽松模式直接启用
                    predictBtn.disabled = false;
                    console.log('✅ 严格/宽松模式：预测按钮已启用');
                }
            } else {
                predictBtn.disabled = true;
                console.log('❌ 预测按钮保持禁用，缺少必要条件');
            }

            return !predictBtn.disabled;
        }

        // 全局调试函数（用户可在控制台调用）
        window.debugAppState = function() {
            console.log('🔍 当前系统状态调试信息:');
            console.log('📊 AppState:', {
                selectedModel: AppState.selectedModel,
                selectedStage: AppState.selectedStage,
                selectedMode: AppState.selectedMode,
                targetColumn: AppState.targetColumn,
                uploadedData: AppState.uploadedData ? `${AppState.uploadedData.length} 行数据` : '无数据',
                dataColumns: AppState.dataColumns ? `${AppState.dataColumns.length} 列: [${AppState.dataColumns.slice(0, 5).join(', ')}${AppState.dataColumns.length > 5 ? '...' : ''}]` : '无列信息',
                fileName: AppState.fileName,
                backendOnline: AppState.backendOnline,
                isProcessing: AppState.isProcessing
            });

            const predictBtn = document.getElementById('predictBtn');
            console.log('🔘 预测按钮状态:', {
                disabled: predictBtn.disabled,
                text: predictBtn.textContent,
                visible: !predictBtn.classList.contains('hidden')
            });

            // 重新检查预测按钮状态
            checkPredictionReadiness();

            return '调试信息已输出到控制台';
        };

        // 加载模型信息
        async function loadModelInfo(stage, modelName) {
            const modelInfo = await ApiService.getModelInfo(stage, modelName);
            
            if (!modelInfo.success) {
                throw new Error(modelInfo.error || '获取模型信息失败');
            }

            AppState.currentModelFeatures = modelInfo.features || [];
            console.log(`模型 ${modelName} 需要的特征:`, AppState.currentModelFeatures);
            
            return modelInfo;
        }

        // 更新选中模型信息显示
        function updateSelectedModelInfo(stage, modelName, target) {
            const selectedInfo = document.getElementById('selectedModelInfo');
            const selectedName = document.getElementById('selectedModelName');
            const selectedTarget = document.getElementById('selectedTargetColumn');

            selectedName.textContent = `${stage} / ${modelName}`;
            selectedTarget.textContent = target;
            selectedInfo.classList.remove('hidden');

            // 自动设置目标列用于评估模式
            AppState.targetColumn = target;
            console.log(`🎯 已自动设置目标列: ${target}`);
        }

        // 文件上传处理
        function initializeFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const fileUploadArea = document.getElementById('fileUploadArea');
            const showTestDataBtn = document.getElementById('showTestDataBtn');

            fileInput.addEventListener('change', handleFileSelect);
            showTestDataBtn.addEventListener('click', showTestDataList);
            
            // 拖拽上传
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                fileUploadArea.addEventListener(eventName, preventDefaults, false);
            });
            
            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }
            
            ['dragenter', 'dragover'].forEach(eventName => {
                fileUploadArea.addEventListener(eventName, () => {
                    fileUploadArea.classList.add('drag-over');
                }, false);
            });
            
            ['dragleave', 'drop'].forEach(eventName => {
                fileUploadArea.addEventListener(eventName, () => {
                    fileUploadArea.classList.remove('drag-over');
                }, false);
            });
            
            fileUploadArea.addEventListener('drop', handleFileDrop, false);
        }

        function handleFileDrop(e) {
            const files = e.dataTransfer.files;
            handleFiles(files);
        }

        function handleFileSelect(e) {
            const files = e.target.files;
            handleFiles(files);
        }

        async function handleFiles(files) {
            if (files.length === 0) return;
            
            const file = files[0];
            
            // 文件大小检查
            if (file.size > CONFIG.FILE_SIZE_LIMIT) {
                Utils.showNotification(`文件大小超过限制 (最大 ${Utils.formatFileSize(CONFIG.FILE_SIZE_LIMIT)})`, 'error');
                return;
            }
            
            AppState.fileName = file.name;
            const fileType = file.name.split('.').pop().toLowerCase();
            
            // 显示文件信息
            document.getElementById('fileDetails').innerHTML = `
                ${AppState.fileName} (${Utils.formatFileSize(file.size)}, ${fileType.toUpperCase()} 格式)
            `;
            document.getElementById('fileInfo').classList.remove('hidden');
            
            // 读取文件
            try {
                if (fileType === 'csv') {
                    await readCSVFile(file);
                } else if (fileType === 'xlsx' || fileType === 'xls') {
                    await readExcelFile(file);
                } else {
                    throw new Error('不支持的文件格式');
                }
                
                document.getElementById('previewBtn').disabled = false;
                Utils.showNotification('文件上传成功！', 'success');
                
            } catch (error) {
                Utils.showNotification(`文件读取失败: ${error.message}`, 'error');
            }
        }

        async function readCSVFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const csvText = e.target.result;
                        const lines = csvText.split('\n').filter(line => line.trim());
                        const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
                        const data = [];

                        for (let i = 1; i < lines.length; i++) {
                            const values = lines[i].split(',');
                            const row = {};
                            headers.forEach((header, index) => {
                                const value = values[index] ? values[index].trim().replace(/"/g, '') : '';
                                row[header] = isNaN(value) || value === '' ? value : parseFloat(value);
                            });
                            data.push(row);
                        }

                        AppState.uploadedData = data;
                        AppState.dataColumns = headers;
                        onDataLoaded();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = reject;
                reader.readAsText(file, 'UTF-8');
            });
        }

        async function readExcelFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const workbook = XLSX.read(e.target.result, { type: 'array' });
                        const firstSheetName = workbook.SheetNames[0];
                        const worksheet = workbook.Sheets[firstSheetName];
                        const data = XLSX.utils.sheet_to_json(worksheet);

                        AppState.uploadedData = data;
                        AppState.dataColumns = data.length > 0 ? Object.keys(data[0]) : [];
                        onDataLoaded();
                        resolve();
                    } catch (error) {
                        reject(error);
                    }
                };
                reader.onerror = reject;
                reader.readAsArrayBuffer(file);
            });
        }

        async function onDataLoaded() {
            if (!AppState.uploadedData || AppState.uploadedData.length === 0) {
                Utils.showNotification('数据文件为空', 'error');
                return;
            }

            // 显示数据统计
            const dataStats = document.getElementById('dataStats');
            dataStats.innerHTML = `
                <div class="success-box">
                    <strong>✅ 数据上传成功！</strong>
                    <div style="margin-top: 10px;">
                        <div>📊 数据行数: ${AppState.uploadedData.length}</div>
                        <div>📋 特征列数: ${AppState.dataColumns.length}</div>
                        <div>🏷️ 特征列: ${AppState.dataColumns.slice(0, 5).join(', ')}${AppState.dataColumns.length > 5 ? '...' : ''}</div>
                    </div>
                </div>
            `;
            dataStats.classList.remove('hidden');

            // 更新评估模式的目标列选项
            updateTargetColumnOptions();

            // 如果已选择模型，进行数据质量验证
            if (AppState.selectedModel && AppState.selectedStage && AppState.backendOnline) {
                await validateDataCompatibility();
            } else if (AppState.selectedModel && !AppState.backendOnline) {
                // 指导模式下提示用户
                showGuideForDataValidation();
            }

            // 数据上传后，如果已选择模型就启用预测
            if (AppState.selectedModel && AppState.selectedStage) {
                enablePrediction('数据已上传且模型已选择');
            } else {
                console.log('📊 数据已上传，等待模型选择...');
            }
        }

        // 指导模式的数据验证提示
        function showGuideForDataValidation() {
            const validationDiv = document.getElementById('dataValidation');
            validationDiv.classList.remove('hidden');

            validationDiv.innerHTML = `
                <div class="info-box">
                    <h4>📋 指导模式 - 数据验证步骤</h4>
                    <p><strong>在真实环境中，系统会自动验证：</strong></p>
                    <ul style="text-align: left; margin: 10px 0;">
                        <li>✅ 数据完整性检查</li>
                        <li>✅ 数据格式验证</li>
                        <li>✅ 目标列存在性（评估模式）</li>
                        <li>✅ 数据统计分析</li>
                    </ul>
                    <p><strong>验证通过后会显示：</strong></p>
                    <div class="example-result">
                        "✅ 数据质量验证通过！数据包含 X 行，Y 列<br>
                        🎯 预测目标: ${AppState.targetColumn || '目标列名'}<br>
                        🚀 预测准备就绪！"
                    </div>
                    <button class="btn btn-primary" onclick="showOperationGuide()" style="margin-top: 10px;">
                        📖 查看完整操作指导
                    </button>
                </div>
            `;

            // 在指导模式下也启用预测按钮，让用户了解完整流程
            enablePrediction('指导模式 - 演示流程');
        }

        // 真实模式数据验证
        async function validateDataCompatibility() {
            if (!AppState.selectedModel || !AppState.selectedStage || !AppState.uploadedData || !AppState.backendOnline) {
                return;
            }

            const validationDiv = document.getElementById('dataValidation');
            validationDiv.innerHTML = '<div class="info-box"><span class="loading"></span>正在验证数据质量...</div>';
            validationDiv.classList.remove('hidden');

            try {
                const result = await ApiService.validateData(
                    AppState.selectedStage,
                    AppState.selectedModel,
                    AppState.uploadedData
                );
                
                if (result.valid) {
                    validationDiv.innerHTML = `
                        <div class="success-box">
                            <strong>✅ 数据验证通过！</strong>
                            <div style="margin-top: 10px;">
                                模型需要 ${result.features_count} 个特征，数据包含 ${result.rows} 行
                            </div>
                        </div>
                    `;
                    document.getElementById('predictBtn').disabled = false;
                } else {
                    validationDiv.innerHTML = `
                        <div class="error-box">
                            <strong>❌ 数据验证失败</strong>
                            <div style="margin-top: 10px;">${result.error}</div>
                        </div>
                    `;
                    document.getElementById('predictBtn').disabled = true;
                }
                
            } catch (error) {
                validationDiv.innerHTML = `
                    <div class="error-box">
                        <strong>❌ 验证失败</strong>
                        <div style="margin-top: 10px;">${error.message}</div>
                    </div>
                `;
                document.getElementById('predictBtn').disabled = true;
            }
        }

        // 数据预览
        function initializeDataPreview() {
            const previewBtn = document.getElementById('previewBtn');
            previewBtn.addEventListener('click', showDataPreview);
        }

        function showDataPreview() {
            if (!AppState.uploadedData) {
                Utils.showNotification('请先上传数据文件', 'warning');
                return;
            }

            const previewDiv = document.getElementById('dataPreview');
            const dataToShow = AppState.uploadedData.slice(0, CONFIG.MAX_PREVIEW_ROWS);

            let html = `
                <div class="info-box">
                    <strong>数据预览:</strong> 
                    显示前 ${Math.min(CONFIG.MAX_PREVIEW_ROWS, AppState.uploadedData.length)} 行
                    (共 ${AppState.uploadedData.length} 行)
                </div>
                <table class="data-table">
                    <thead><tr>
                        <th>#</th>
            `;

            // 表头
            AppState.dataColumns.forEach(header => {
                html += `<th>${header}</th>`;
            });
            html += '</tr></thead><tbody>';

            // 数据行
            dataToShow.forEach((row, index) => {
                html += `<tr><td>${index + 1}</td>`;
                AppState.dataColumns.forEach(header => {
                    const value = row[header];
                    const displayValue = typeof value === 'number' ? Utils.formatNumber(value, 4) : (value || '');
                    html += `<td>${displayValue}</td>`;
                });
                html += '</tr>';
            });

            html += '</tbody></table>';
            previewDiv.innerHTML = html;
            previewDiv.classList.remove('hidden');
        }

        // 预测功能
        function initializePrediction() {
            const predictBtn = document.getElementById('predictBtn');
            predictBtn.addEventListener('click', performPrediction);
        }

        async function performPrediction() {
            console.log('🚀 开始执行预测...');
            console.log('📊 当前状态:', {
                selectedModel: AppState.selectedModel,
                selectedStage: AppState.selectedStage,
                selectedMode: AppState.selectedMode,
                targetColumn: AppState.targetColumn,
                hasData: AppState.uploadedData ? AppState.uploadedData.length : 0,
                dataColumns: AppState.dataColumns ? AppState.dataColumns.length : 0
            });

            // 基本验证：数据必须存在
            if (!AppState.uploadedData || AppState.uploadedData.length === 0) {
                Utils.showNotification('❌ 请先上传数据文件', 'warning');
                return;
            }

            // 基本验证：模型必须选择（这应该在前面步骤完成）
            if (!AppState.selectedModel || !AppState.selectedStage) {
                Utils.showNotification('❌ 系统错误：模型信息丢失，请刷新页面重新操作', 'error');
                console.error('模型信息丢失:', { selectedModel: AppState.selectedModel, selectedStage: AppState.selectedStage });
                return;
            }

            // 评估模式特殊验证：需要目标列
            if (AppState.selectedMode === 'evaluation') {
                if (!AppState.targetColumn) {
                    Utils.showNotification('❌ 系统错误：评估模式目标列未设置，请刷新页面重新操作', 'error');
                    console.error('评估模式目标列未设置');
                    return;
                }
                if (!AppState.dataColumns.includes(AppState.targetColumn)) {
                    Utils.showNotification(`❌ 数据中缺少目标列：${AppState.targetColumn}`, 'warning');
                    return;
                }
            }

            const predictBtn = document.getElementById('predictBtn');
            const progressDiv = document.getElementById('predictionProgress');
            
            predictBtn.disabled = true;
            predictBtn.innerHTML = '<span class="loading"></span>正在预测...';
            progressDiv.classList.remove('hidden');

            try {
                let result;
                
                if (AppState.backendOnline) {
                    // 真实预测
                    result = await ApiService.predict(
                        AppState.selectedStage,
                        AppState.selectedModel,
                        AppState.uploadedData,
                        AppState.selectedMode,
                        AppState.targetColumn
                    );
                    
                    if (!result.success) {
                        throw new Error(result.error || '预测处理失败');
                    }
                } else {
                    // 指导模式 - 显示预测流程说明
                    showPredictionGuide();
                    return;
                }

                AppState.predictionResults = result.results;
                AppState.predictionStatistics = result.statistics;
                AppState.transformRecord = result.results[0]?.['特征变换记录'] || {};
                displayPredictionResults(result);
                
                // 启用下载和报告生成按钮
                document.getElementById('downloadExcelBtn').disabled = false;
                document.getElementById('downloadCSVBtn').disabled = false;
                document.getElementById('generateReportBtn').disabled = false;
                
                Utils.showNotification(`预测完成！共预测 ${result.statistics['数量']} 个样本`, 'success');
                
            } catch (error) {
                Utils.showNotification(`预测失败: ${error.message}`, 'error');
                console.error(error);
            } finally {
                predictBtn.disabled = false;
                predictBtn.textContent = '重新预测';
                progressDiv.classList.add('hidden');
            }
        }

        // 显示预测流程指导
        function showPredictionGuide() {
            const predictBtn = document.getElementById('predictBtn');
            const progressDiv = document.getElementById('predictionProgress');
            const resultsDiv = document.getElementById('predictionResults');

            // 重置按钮状态
            predictBtn.disabled = false;
            predictBtn.textContent = '🚀 开始预测';
            progressDiv.classList.add('hidden');

            // 显示预测流程说明
            resultsDiv.innerHTML = `
                <div class="guide-prediction">
                    <div class="info-box">
                        <h3>📋 预测流程指导</h3>
                        <p><strong>在真实环境中，点击预测按钮后系统会：</strong></p>

                        <div class="prediction-steps">
                            <div class="prediction-step">
                                <span class="step-icon">1️⃣</span>
                                <div class="step-content">
                                    <h4>数据预处理</h4>
                                    <p>对上传的数据进行标准化和特征工程处理</p>
                                </div>
                            </div>

                            <div class="prediction-step">
                                <span class="step-icon">2️⃣</span>
                                <div class="step-content">
                                    <h4>模型加载</h4>
                                    <p>加载已选择的 "${AppState.selectedModel || '预测模型'}" 预测模型</p>
                                </div>
                            </div>

                            <div class="prediction-step">
                                <span class="step-icon">3️⃣</span>
                                <div class="step-content">
                                    <h4>预测计算</h4>
                                    <p>使用机器学习算法对每行数据进行预测计算</p>
                                </div>
                            </div>

                            <div class="prediction-step">
                                <span class="step-icon">4️⃣</span>
                                <div class="step-content">
                                    <h4>结果统计</h4>
                                    <p>计算预测结果的统计指标：均值、标准差、分布等</p>
                                </div>
                            </div>

                            <div class="prediction-step">
                                <span class="step-icon">5️⃣</span>
                                <div class="step-content">
                                    <h4>结果展示</h4>
                                    <p>生成预测结果表格、分布图表和统计报告</p>
                                </div>
                            </div>
                        </div>

                        <div class="guide-actions">
                            <button class="btn btn-primary" onclick="showOperationGuide()">
                                📖 查看完整操作指导
                            </button>
                            <button class="btn btn-success" onclick="showStartupInstructions()">
                                🚀 启动后端服务指导
                            </button>
                        </div>
                    </div>
                </div>
            `;

            resultsDiv.classList.remove('hidden');
            Utils.showNotification('预测流程指导已显示，要执行真实预测请启动后端服务', 'info');
        }

        // 显示启动后端服务指导
        function showStartupInstructions() {
            const modal = document.getElementById('guideModal');
            const guideSteps = document.getElementById('guideSteps');

            guideSteps.innerHTML = `
                <div class="startup-guide">
                    <div class="guide-step">
                        <div class="step-header">
                            <h3>🚀 启动后端服务指导</h3>
                        </div>
                        <div class="step-body">
                            <div class="startup-steps">
                                <div class="startup-step">
                                    <span class="step-number">1</span>
                                    <div class="step-content">
                                        <h4>打开命令行终端</h4>
                                        <p>Windows: 按 Win+R，输入 cmd 或 PowerShell</p>
                                        <p>Mac/Linux: 打开 Terminal</p>
                                    </div>
                                </div>

                                <div class="startup-step">
                                    <span class="step-number">2</span>
                                    <div class="step-content">
                                        <h4>进入项目目录</h4>
                                        <code>cd /path/to/baowu_steel_final_website</code>
                                    </div>
                                </div>

                                <div class="startup-step">
                                    <span class="step-number">3</span>
                                    <div class="step-content">
                                        <h4>进入后端目录</h4>
                                        <code>cd backend</code>
                                    </div>
                                </div>

                                <div class="startup-step">
                                    <span class="step-number">4</span>
                                    <div class="step-content">
                                        <h4>启动Flask应用</h4>
                                        <code>python app.py</code>
                                        <p style="margin-top: 10px;">看到 "Running on http://127.0.0.1:5000" 表示启动成功</p>
                                    </div>
                                </div>

                                <div class="startup-step">
                                    <span class="step-number">5</span>
                                    <div class="step-content">
                                        <h4>刷新页面</h4>
                                        <p>启动成功后，刷新浏览器页面即可使用完整功能</p>
                                        <button class="btn btn-primary" onclick="location.reload()">
                                            🔄 刷新页面
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            modal.classList.remove('hidden');
        }

        // 显示预测结果
        function displayPredictionResults(result) {
            const resultsDiv = document.getElementById('predictionResults');
            
            // 统计卡片
            let html = `
                <h4 style="margin-bottom: 20px;">📊 预测结果统计</h4>
                <div class="result-stats">
                    <div class="stat-card">
                        <div class="stat-label">预测样本数</div>
                        <div class="stat-value">${result.statistics['数量']}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">平均预测值</div>
                        <div class="stat-value">${Utils.formatNumber(result.statistics['平均值'] || result.statistics['均值'])}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">最小值</div>
                        <div class="stat-value">${Utils.formatNumber(result.statistics['最小值'])}</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-label">最大值</div>
                        <div class="stat-value">${Utils.formatNumber(result.statistics['最大值'])}</div>
                    </div>
                </div>
            `;

            // 模型信息
            html += `
                <div class="info-box" style="margin-top: 20px;">
                    <strong>🤖 使用模型:</strong> ${result.stage} / ${result.model}<br>
                    <strong>🎯 预测目标:</strong> ${result.target || '预测值'}<br>
                    <strong>🔧 预测模式:</strong> ${getModeName(result.prediction_mode || AppState.selectedMode)}
                    ${!AppState.backendOnline ? '<br><strong>📋 注意:</strong> 当前为指导模式，已显示操作流程说明' : ''}
                </div>
            `;

            // 评估模式结果
            if (result.evaluation) {
                html += renderEvaluationResults(result.evaluation);
            }

            // 填充报告
            if (result.fill_report && result.fill_report.length > 0) {
                html += renderFillReport(result.fill_report);
            }

            // 特征变换信息（如果有）
            if (result.missing_info && Object.keys(result.missing_info).length > 0) {
                html += `
                    <div class="warning-box" style="margin-top: 15px;">
                        <strong>⚠️ 数据处理信息:</strong><br>
                        以下特征包含缺失值，已进行处理：<br>
                        ${Object.entries(result.missing_info).map(([feat, count]) => 
                            `${feat}: ${count} 个缺失值`
                        ).join('<br>')}
                    </div>
                `;
            }

            // 预测结果预览和查看按钮
            html += `
                <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 30px; margin-bottom: 15px;">
                    <h4 style="margin: 0;">📋 预测结果预览（前10条）</h4>
                    <button class="btn" onclick="showAllPredictions()" style="padding: 10px 20px; font-size: 14px;">
                        查看全部结果
                    </button>
                </div>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>预测值</th>
                            ${AppState.dataColumns.slice(0, 3).map(col => `<th>${col}</th>`).join('')}
                            ${AppState.dataColumns.length > 3 ? '<th>...</th>' : ''}
                        </tr>
                    </thead>
                    <tbody>
            `;

            result.results.slice(0, 10).forEach((res, index) => {
                html += `
                    <tr>
                        <td>${index + 1}</td>
                        <td style="font-weight: bold; color: var(--primary-color);">
                            ${Utils.formatNumber(res.pred)}
                        </td>
                        ${AppState.dataColumns.slice(0, 3).map(col => 
                            `<td>${Utils.formatNumber(res['原始'][col])}</td>`
                        ).join('')}
                        ${AppState.dataColumns.length > 3 ? '<td>...</td>' : ''}
                    </tr>
                `;
            });

            html += '</tbody></table>';

            // 添加图表容器
            html += `
                <h4 style="margin-top: 30px; margin-bottom: 15px;">📈 预测值分布图</h4>
                <div class="chart-container">
                    <canvas id="predictionChart"></canvas>
                </div>
            `;

            resultsDiv.innerHTML = html;
            resultsDiv.classList.remove('hidden');

            // 确保DOM更新后再绘制图表
            requestAnimationFrame(() => {
                drawPredictionChart(result.results);
            });
        }

        // 显示所有预测结果
        function showAllPredictions() {
            if (!AppState.predictionResults) return;
            
            const modal = document.createElement('div');
            modal.className = 'report-preview';
            modal.innerHTML = `
                <div class="report-preview-content">
                    <div class="report-preview-header">
                        <h2>所有预测结果</h2>
                        <button class="btn" onclick="downloadResults('xlsx')">📥 下载Excel</button>
                    </div>
                    <button class="close-preview" onclick="this.parentElement.parentElement.remove()">&times;</button>
                    <div class="report-preview-body">
                        <div style="overflow-x: auto;">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>序号</th>
                                        <th>预测值</th>
                                        ${AppState.dataColumns.map(col => `<th>${col}</th>`).join('')}
                                    </tr>
                                </thead>
                                <tbody>
                                    ${AppState.predictionResults.map((res, index) => `
                                        <tr>
                                            <td>${index + 1}</td>
                                            <td style="font-weight: bold; color: var(--primary-color);">
                                                ${Utils.formatNumber(res.pred)}
                                            </td>
                                            ${AppState.dataColumns.map(col => 
                                                `<td>${Utils.formatNumber(res['原始'][col])}</td>`
                                            ).join('')}
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 绘制预测图表
        function drawPredictionChart(results) {
            const canvas = document.getElementById('predictionChart');
            if (!canvas) return;

            // 销毁之前的图表实例
            if (AppState.chartInstance) {
                AppState.chartInstance.destroy();
            }

            const ctx = canvas.getContext('2d');
            const predictions = results.map(r => r.pred);
            
            // 计算直方图数据
            const bins = 10;
            const min = Math.min(...predictions);
            const max = Math.max(...predictions);
            const binWidth = (max - min) / bins;
            
            const histogram = Array(bins).fill(0);
            predictions.forEach(pred => {
                const binIndex = Math.min(Math.floor((pred - min) / binWidth), bins - 1);
                if (binIndex >= 0) {
                    histogram[binIndex]++;
                }
            });

            const labels = Array(bins).fill(0).map((_, i) => 
                `${(min + i * binWidth).toFixed(2)} - ${(min + (i + 1) * binWidth).toFixed(2)}`
            );

            // 创建新的图表实例
            AppState.chartInstance = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '预测值分布',
                        data: histogram,
                        backgroundColor: 'rgba(102, 126, 234, 0.6)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1,
                        borderRadius: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '预测结果分布直方图',
                            font: { size: 16, weight: 'bold' }
                        },
                        legend: { display: true, position: 'top' }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '样本数量' },
                            ticks: { stepSize: 1 }
                        },
                        x: {
                            title: { display: true, text: '预测值区间' },
                            ticks: { maxRotation: 45, minRotation: 45 }
                        }
                    }
                }
            });
        }

        // // 改进的质量等级判断函数
        // function getQualityGrade(value, modelType) {
        //     // 根据不同的模型类型使用不同的质量标准
        //     if (modelType && modelType.includes('硬度')) {
        //         // 硬度模型的质量等级
        //         if (value >= 300) return { label: '优秀', color: '#4caf50' };
        //         if (value >= 280) return { label: '良好', color: '#2196f3' };
        //         if (value >= 260) return { label: '合格', color: '#ff9800' };
        //         return { label: '待改进', color: '#f44336' };
        //     } else if (modelType && modelType.includes('拉伸')) {
        //         // 拉伸模型的质量等级
        //         if (value >= 500) return { label: '优秀', color: '#4caf50' };
        //         if (value >= 450) return { label: '良好', color: '#2196f3' };
        //         if (value >= 400) return { label: '合格', color: '#ff9800' };
        //         return { label: '待改进', color: '#f44336' };
        //     } else if (modelType && modelType.includes('抗硫')) {
        //         // 抗硫模型的质量等级（时间越长越好）
        //         if (value >= 720) return { label: '优秀', color: '#4caf50' };
        //         if (value >= 500) return { label: '良好', color: '#2196f3' };
        //         if (value >= 300) return { label: '合格', color: '#ff9800' };
        //         return { label: '待改进', color: '#f44336' };
        //     } else {
        //         // 默认质量等级
        //         if (value >= 280) return { label: '优秀', color: '#4caf50' };
        //         if (value >= 260) return { label: '良好', color: '#2196f3' };
        //         if (value >= 240) return { label: '合格', color: '#ff9800' };
        //         return { label: '待改进', color: '#f44336' };
        //     }
        // }

        // 报告生成功能
        function initializeReportGeneration() {
            const generateReportBtn = document.getElementById('generateReportBtn');
            generateReportBtn.addEventListener('click', generateAnalysisReport);
        }

        async function generateAnalysisReport() {
            if (!AppState.predictionResults) {
                Utils.showNotification('没有可分析的预测结果', 'warning');
                return;
            }

            const progressDiv = document.getElementById('reportProgress');
            progressDiv.classList.remove('hidden');

            try {
                // 生成报告内容
                const reportContent = await createReportContent();
                
                // 显示报告预览
                showReportPreview(reportContent);
                
                Utils.showNotification('分析报告生成成功！', 'success');
            } catch (error) {
                Utils.showNotification(`报告生成失败: ${error.message}`, 'error');
                console.error(error);
            } finally {
                progressDiv.classList.add('hidden');
            }
        }

        async function createReportContent() {
            const predictions = AppState.predictionResults.map(r => r.pred);
            const stats = AppState.predictionStatistics;
            
            // 计算额外的统计信息
            const median = calculateMedian(predictions);
            const quartiles = calculateQuartiles(predictions);
            const variance = stats['标准差'] * stats['标准差'];
            const cv = (stats['标准差'] / stats['均值']) * 100; // 变异系数
            const skewness = Utils.calculateSkewness(predictions);
            const kurtosis = Utils.calculateKurtosis(predictions);

            // 获取模型阶段描述
            const stageDesc = AppState.modelStageDescriptions[`./${AppState.selectedStage}`] || AppState.selectedStage;

            // 生成报告HTML
            let html = `
                <div class="report-section">
                    <h2>📊 耐应力腐蚀油套管质量预测分析报告</h2>
                    <p><strong>生成时间：</strong>${Utils.getCurrentDateTime()}</p>
                    <p><strong>报告编号：</strong>${generateReportId()}</p>
                </div>

                <div class="report-section">
                    <h3>1. 项目概览</h3>
                    <p><strong>数据文件：</strong>${AppState.fileName}</p>
                    <p><strong>模型阶段：</strong>${stageDesc}</p>
                    <p><strong>使用模型：</strong>${AppState.selectedModel}</p>
                    <p><strong>预测目标：</strong>${AppState.availableModels.find(s => s.stage.includes(AppState.selectedStage))?.models.find(m => m.name === AppState.selectedModel)?.target || '预测值'}</p>
                    <p><strong>样本数量：</strong>${AppState.uploadedData.length} 条</p>
                    <p><strong>特征数量：</strong>${AppState.dataColumns.length} 个</p>
                    <p><strong>有效预测数：</strong>${stats['数量']} 条</p>
                </div>

                <div class="report-section">
                    <h3>2. 预测结果统计</h3>
                    <table class="data-table" style="max-width: 600px;">
                        <tr><th>统计指标</th><th>数值</th></tr>
                        <tr><td>样本数量</td><td>${stats['数量']}</td></tr>
                        <tr><td>平均值</td><td>${Utils.formatNumber(stats['均值'], 4)}</td></tr>
                        <tr><td>中位数</td><td>${Utils.formatNumber(median, 4)}</td></tr>
                        <tr><td>标准差</td><td>${Utils.formatNumber(stats['标准差'], 4)}</td></tr>
                        <tr><td>方差</td><td>${Utils.formatNumber(variance, 4)}</td></tr>
                        <tr><td>变异系数</td><td>${Utils.formatNumber(cv, 2)}%</td></tr>
                        <tr><td>偏度</td><td>${Utils.formatNumber(skewness, 4)}</td></tr>
                        <tr><td>峰度</td><td>${Utils.formatNumber(kurtosis, 4)}</td></tr>
                        <tr><td>最小值</td><td>${Utils.formatNumber(stats['最小值'], 4)}</td></tr>
                        <tr><td>第一四分位数</td><td>${Utils.formatNumber(quartiles.q1, 4)}</td></tr>
                        <tr><td>第三四分位数</td><td>${Utils.formatNumber(quartiles.q3, 4)}</td></tr>
                        <tr><td>最大值</td><td>${Utils.formatNumber(stats['最大值'], 4)}</td></tr>
                        <tr><td>极差</td><td>${Utils.formatNumber(stats['最大值'] - stats['最小值'], 4)}</td></tr>
                        <tr><td>四分位距</td><td>${Utils.formatNumber(quartiles.q3 - quartiles.q1, 4)}</td></tr>
                    </table>
                </div>

                <div class="report-section">
                    <h3>3. 数据分布分析</h3>
                    <div class="report-charts">
                        <div class="report-chart">
                            <h4>预测值分布直方图</h4>
                            <canvas id="reportHistogram"></canvas>
                        </div>
                        <div class="report-chart">
                            <h4>预测值累积分布</h4>
                            <canvas id="reportCumulative"></canvas>
                        </div>
                    </div>
                </div>

                <div class="report-section">
                    <h3>4. 预测结果详情（前20条）</h3>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>预测值</th>
                                ${AppState.dataColumns.slice(0, 3).map(col => `<th>${col}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
                            ${AppState.predictionResults.slice(0, 20).map((res, idx) => {
                                return `
                                    <tr>
                                        <td>${idx + 1}</td>
                                        <td>${Utils.formatNumber(res.pred, 4)}</td>
                                        ${AppState.dataColumns.slice(0, 3).map(col => 
                                            `<td>${Utils.formatNumber(res['原始'][col], 4)}</td>`
                                        ).join('')}
                                    </tr>
                                `;
                            }).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="report-section">
                    <h3>5. 结论与建议</h3>
                    ${generateConclusions(stats, median, cv, skewness, kurtosis)}
                </div>

                <div class="report-section" style="margin-top: 40px; text-align: center; color: #666;">
                    <p>—— 报告结束 ——</p>
                    <p>本报告由基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统自动生成</p>
                    ${!AppState.backendOnline ? '<p style="color: #2196F3;">注：指导模式下显示的是操作流程说明</p>' : ''}
                </div>
            `;

            return html;
        }

        function calculateMedian(arr) {
            const sorted = [...arr].sort((a, b) => a - b);
            const mid = Math.floor(sorted.length / 2);
            return sorted.length % 2 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
        }

        function calculateQuartiles(arr) {
            const sorted = [...arr].sort((a, b) => a - b);
            const q1Index = Math.floor(sorted.length * 0.25);
            const q3Index = Math.floor(sorted.length * 0.75);
            return {
                q1: sorted[q1Index],
                q3: sorted[q3Index]
            };
        }

        function generateReportId() {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
            return `RPT-${year}${month}${day}-${random}`;
        }

        function generateConclusions(stats, median, cv, skewness, kurtosis) {
            let conclusions = '<ul>';
            
            // 分析偏度
            if (Math.abs(skewness) < 0.5) {
                conclusions += '<li>预测值分布较为对称，数据质量稳定。</li>';
            } else if (skewness > 0) {
                conclusions += '<li>预测值分布呈右偏态，存在部分高值样本，建议关注高端产品的质量控制。</li>';
            } else {
                conclusions += '<li>预测值分布呈左偏态，存在部分低值样本，需要关注低端产品的质量提升。</li>';
            }
            
            // 分析峰度
            if (Math.abs(kurtosis) < 0.5) {
                conclusions += '<li>预测值分布接近正态分布，生产过程稳定。</li>';
            } else if (kurtosis > 0) {
                conclusions += '<li>预测值分布呈尖峰态，数据集中度高，但需注意极端值。</li>';
            } else {
                conclusions += '<li>预测值分布呈扁平态，数据分散度较大，建议加强过程控制。</li>';
            }
            
            // 分析变异系数
            if (cv < 10) {
                conclusions += '<li>变异系数较小（<10%），预测结果一致性高，生产质量控制良好。</li>';
            } else if (cv < 20) {
                conclusions += '<li>变异系数适中（10-20%），预测结果存在一定差异，建议加强质量控制。</li>';
            } else {
                conclusions += '<li>变异系数较大（>20%），预测结果差异明显，需要重点关注生产工艺稳定性。</li>';
            }
            
            // 分析极差
            const range = stats['最大值'] - stats['最小值'];
            if (range < stats['均值'] * 0.2) {
                conclusions += '<li>预测值极差较小，产品质量稳定性好。</li>';
            } else {
                conclusions += '<li>预测值极差较大，建议分析异常值产生原因。</li>';
            }
            
            // 根据模型类型给出特定建议
            if (AppState.selectedModel && AppState.selectedModel.includes('硬度')) {
                conclusions += '<li><strong>硬度控制建议：</strong>关注热处理工艺参数，确保硬度值的一致性。</li>';
            } else if (AppState.selectedModel && AppState.selectedModel.includes('拉伸')) {
                conclusions += '<li><strong>拉伸性能建议：</strong>优化轧制工艺，提高材料的拉伸强度和延展性。</li>';
            } else if (AppState.selectedModel && AppState.selectedModel.includes('抗硫')) {
                conclusions += '<li><strong>抗硫性能建议：</strong>严格控制合金成分，特别是铬、钼等元素的含量。</li>';
            }
            
            // 通用建议
            conclusions += '<li><strong>数据质量建议：</strong>定期更新模型训练数据，确保预测准确性。</li>';
            conclusions += '<li><strong>过程监控建议：</strong>建立实时监控系统，及时发现和处理异常情况。</li>';
            
            conclusions += '</ul>';
            return conclusions;
        }

        function showReportPreview(content) {
            const preview = document.getElementById('reportPreview');
            const reportContent = document.getElementById('reportContent');
            
            reportContent.innerHTML = content;
            preview.classList.remove('hidden');
            
            // 延迟绘制图表，确保DOM已更新
            setTimeout(() => {
                drawReportCharts();
            }, 100);
        }

        function drawReportCharts() {
            const predictions = AppState.predictionResults.map(r => r.pred);
            
            // 1. 绘制直方图
            drawReportHistogram(predictions);
            
            // 2. 绘制累积分布图
            drawReportCumulative(predictions);
        }

        function drawReportHistogram(predictions) {
            const canvas = document.getElementById('reportHistogram');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const bins = 15;
            const min = Math.min(...predictions);
            const max = Math.max(...predictions);
            const binWidth = (max - min) / bins;
            
            const histogram = Array(bins).fill(0);
            predictions.forEach(pred => {
                const binIndex = Math.min(Math.floor((pred - min) / binWidth), bins - 1);
                if (binIndex >= 0) histogram[binIndex]++;
            });
            
            const labels = Array(bins).fill(0).map((_, i) => 
                `${(min + i * binWidth).toFixed(1)}`
            );
            
            if (AppState.reportCharts.histogram) {
                AppState.reportCharts.histogram.destroy();
            }
            
            AppState.reportCharts.histogram = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '频数',
                        data: histogram,
                        backgroundColor: 'rgba(102, 126, 234, 0.6)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: { display: true, text: '频数' }
                        },
                        x: {
                            title: { display: true, text: '预测值' }
                        }
                    }
                }
            });
        }

        function drawReportCumulative(predictions) {
            const canvas = document.getElementById('reportCumulative');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const sorted = [...predictions].sort((a, b) => a - b);
            const cumulative = sorted.map((_, i) => ((i + 1) / sorted.length) * 100);
            
            if (AppState.reportCharts.cumulative) {
                AppState.reportCharts.cumulative.destroy();
            }
            
            AppState.reportCharts.cumulative = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: sorted.map(v => v.toFixed(1)),
                    datasets: [{
                        label: '累积百分比',
                        data: cumulative,
                        borderColor: 'rgba(118, 75, 162, 1)',
                        backgroundColor: 'rgba(118, 75, 162, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            title: { display: true, text: '累积百分比 (%)' }
                        },
                        x: {
                            title: { display: true, text: '预测值' },
                            ticks: {
                                maxTicksLimit: 10
                            }
                        }
                    },
                    elements: {
                        point: {
                            radius: 0
                        }
                    }
                }
            });
        }

        function drawQualityPieChart(predictions) {
            const canvas = document.getElementById('reportPieChart');
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            
            // // 统计质量等级
            // const gradeCount = {
            //     '优秀': 0,
            //     '良好': 0,
            //     '合格': 0,
            //     '待改进': 0
            // };
            
            predictions.forEach(pred => {
                const grade = getQualityGrade(pred, AppState.selectedModel);
                gradeCount[grade.label]++;
            });
            
            // 更新分布说明
            const distributionDiv = document.getElementById('qualityDistribution');
            distributionDiv.innerHTML = `
                <table class="data-table" style="max-width: 500px; margin: 0 auto;">
                    <tr><th>质量等级</th><th>数量</th><th>占比</th></tr>
                    ${Object.entries(gradeCount).map(([grade, count]) => `
                        <tr>
                            <td>${grade}</td>
                            <td>${count}</td>
                            <td>${(count / predictions.length * 100).toFixed(2)}%</td>
                        </tr>
                    `).join('')}
                </table>
            `;
            
            if (AppState.reportCharts.pie) {
                AppState.reportCharts.pie.destroy();
            }
            
            AppState.reportCharts.pie = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: Object.keys(gradeCount),
                    datasets: [{
                        data: Object.values(gradeCount),
                        backgroundColor: [
                            'rgba(76, 175, 80, 0.8)',
                            'rgba(33, 150, 243, 0.8)',
                            'rgba(255, 152, 0, 0.8)',
                            'rgba(244, 67, 54, 0.8)'
                        ],
                        borderColor: '#fff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        },
                        title: {
                            display: true,
                            text: '质量等级分布图'
                        }
                    }
                }
            });
        }

        function closeReportPreview() {
            document.getElementById('reportPreview').classList.add('hidden');
        }

        async function downloadPDFReport() {
            const { jsPDF } = window.jspdf;
            
            try {
                Utils.showNotification('正在生成PDF报告，请稍候...', 'info');
                
                const pdf = new jsPDF('p', 'mm', 'a4');
                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();
                const margin = 15;
                let yPosition = margin;
                
                // 添加标题
                pdf.setFontSize(20);
                pdf.setTextColor(102, 126, 234);
                pdf.text('耐应力腐蚀油套管质量预测分析报告', pageWidth / 2, yPosition, { align: 'center' });
                yPosition += 15;
                
                // 添加基本信息
                pdf.setFontSize(12);
                pdf.setTextColor(0, 0, 0);
                pdf.text(`生成时间: ${Utils.getCurrentDateTime()}`, margin, yPosition);
                yPosition += 8;
                pdf.text(`报告编号: ${generateReportId()}`, margin, yPosition);
                yPosition += 15;
                
                // 添加项目概览
                pdf.setFontSize(14);
                pdf.setTextColor(102, 126, 234);
                pdf.text('1. 项目概览', margin, yPosition);
                yPosition += 10;
                
                pdf.setFontSize(11);
                pdf.setTextColor(0, 0, 0);
                pdf.text(`数据文件: ${AppState.fileName}`, margin + 5, yPosition);
                yPosition += 7;
                pdf.text(`使用模型: ${AppState.selectedStage} / ${AppState.selectedModel}`, margin + 5, yPosition);
                yPosition += 7;
                pdf.text(`样本数量: ${AppState.uploadedData.length} 条`, margin + 5, yPosition);
                yPosition += 7;
                pdf.text(`特征数量: ${AppState.dataColumns.length} 个`, margin + 5, yPosition);
                yPosition += 7;
                pdf.text(`有效预测数: ${AppState.predictionStatistics['数量']} 条`, margin + 5, yPosition);
                yPosition += 15;
                
                // 添加统计信息表格
                pdf.setFontSize(14);
                pdf.setTextColor(102, 126, 234);
                pdf.text('2. 预测结果统计', margin, yPosition);
                yPosition += 10;
                
                const stats = AppState.predictionStatistics;
                const predictions = AppState.predictionResults.map(r => r.pred);
                const median = calculateMedian(predictions);
                const cv = (stats['标准差'] / stats['均值']) * 100;
                
                const tableData = [
                    ['统计指标', '数值'],
                    ['样本数量', stats['数量'].toString()],
                    ['平均值', Utils.formatNumber(stats['均值'], 4)],
                    ['中位数', Utils.formatNumber(median, 4)],
                    ['标准差', Utils.formatNumber(stats['标准差'], 4)],
                    ['变异系数', Utils.formatNumber(cv, 2) + '%'],
                    ['最小值', Utils.formatNumber(stats['最小值'], 4)],
                    ['最大值', Utils.formatNumber(stats['最大值'], 4)]
                ];
                
                pdf.autoTable({
                    startY: yPosition,
                    head: [tableData[0]],
                    body: tableData.slice(1),
                    margin: { left: margin },
                    styles: { fontSize: 10 },
                    headStyles: { fillColor: [102, 126, 234] }
                });
                
                yPosition = pdf.lastAutoTable.finalY + 20;
                
                // 检查是否需要新页面
                if (yPosition > pageHeight - 50) {
                    pdf.addPage();
                    yPosition = margin;
                }
                
                // 添加图表
                pdf.setFontSize(14);
                pdf.setTextColor(102, 126, 234);
                pdf.text('3. 数据分布分析', margin, yPosition);
                yPosition += 10;
                
                // 将canvas转换为图片并添加到PDF
                const histogramCanvas = document.getElementById('reportHistogram');
                if (histogramCanvas) {
                    const histogramImg = await html2canvas(histogramCanvas.parentElement);
                    const histogramData = histogramImg.toDataURL('image/png');
                    pdf.addImage(histogramData, 'PNG', margin, yPosition, pageWidth - 2 * margin, 60);
                    yPosition += 70;
                }
                
                // 添加结论
                if (yPosition > pageHeight - 50) {
                    pdf.addPage();
                    yPosition = margin;
                }
                
                pdf.setFontSize(14);
                pdf.setTextColor(102, 126, 234);
                pdf.text('4. 结论与建议', margin, yPosition);
                yPosition += 10;
                
                pdf.setFontSize(10);
                pdf.setTextColor(0, 0, 0);
                const conclusions = [
                    '• 预测模型运行正常，结果符合预期分布。',
                    '• 建议定期监控预测指标变化趋势。',
                    '• 对异常值样本进行重点关注和分析。',
                    '• 持续优化生产工艺，提高产品质量稳定性。'
                ];
                
                conclusions.forEach(conclusion => {
                    pdf.text(conclusion, margin + 5, yPosition);
                    yPosition += 7;
                });
                
                // 添加页脚
                const pageCount = pdf.internal.getNumberOfPages();
                for (let i = 1; i <= pageCount; i++) {
                    pdf.setPage(i);
                    pdf.setFontSize(10);
                    pdf.setTextColor(128, 128, 128);
                    pdf.text(`第 ${i} 页 / 共 ${pageCount} 页`, pageWidth / 2, pageHeight - 10, { align: 'center' });
                }
                
                // 保存PDF
                const fileName = `预测分析报告_${AppState.selectedModel}_${new Date().toISOString().split('T')[0]}.pdf`;
                pdf.save(fileName);
                
                Utils.showNotification('PDF报告已成功生成并下载！', 'success');
                
            } catch (error) {
                console.error('PDF生成错误:', error);
                Utils.showNotification('PDF生成失败，请重试', 'error');
            }
        }

        // 结果下载
        function initializeDownload() {
            document.getElementById('downloadExcelBtn').addEventListener('click', () => downloadResults('xlsx'));
            document.getElementById('downloadCSVBtn').addEventListener('click', () => downloadResults('csv'));
        }

        function downloadResults(format) {
            if (!AppState.predictionResults) {
                Utils.showNotification('没有可下载的预测结果', 'warning');
                return;
            }

            // 准备下载数据
            const downloadData = AppState.predictionResults.map((result, index) => {
                const prediction = result.pred;
                
                // 基础数据
                const rowData = {
                    '序号': index + 1,
                    '预测值': prediction,
                    ...result['原始']
                };
                
                return rowData;
            });

            // 添加汇总信息行
            const stats = AppState.predictionStatistics;
            const summaryRow = {
                '序号': '汇总',
                '预测值': `平均值: ${Utils.formatNumber(stats['均值'], 4)}`
            };
            
            // 将原始特征列填充为空或统计信息
            AppState.dataColumns.forEach(col => {
                summaryRow[col] = '';
            });
            
            downloadData.push(summaryRow);

            const timestamp = new Date().toISOString().split('T')[0];
            const fileName = `预测结果_${AppState.selectedStage}_${AppState.selectedModel}_${timestamp}`;

            if (format === 'xlsx') {
                const ws = XLSX.utils.json_to_sheet(downloadData);
                
                // 设置列宽
                const wscols = [];
                Object.keys(downloadData[0]).forEach(() => {
                    wscols.push({ wch: 15 });
                });
                ws['!cols'] = wscols;
                
                const wb = XLSX.utils.book_new();
                XLSX.utils.book_append_sheet(wb, ws, "预测结果");
                
                // 添加模型信息工作表
                const infoData = [
                    ['项目', '信息'],
                    ['文件名', AppState.fileName],
                    ['模型阶段', AppState.modelStageDescriptions[`./${AppState.selectedStage}`] || AppState.selectedStage],
                    ['模型名称', AppState.selectedModel],
                    ['预测时间', Utils.getCurrentDateTime()],
                    ['样本数量', AppState.uploadedData.length],
                    ['有效预测数', stats['数量']],
                    ['平均值', Utils.formatNumber(stats['均值'], 4)],
                    ['标准差', Utils.formatNumber(stats['标准差'], 4)],
                    ['最小值', Utils.formatNumber(stats['最小值'], 4)],
                    ['最大值', Utils.formatNumber(stats['最大值'], 4)]
                ];
                
                const wsInfo = XLSX.utils.aoa_to_sheet(infoData);
                XLSX.utils.book_append_sheet(wb, wsInfo, "模型信息");
                
                XLSX.writeFile(wb, `${fileName}.xlsx`);
            } else if (format === 'csv') {
                const ws = XLSX.utils.json_to_sheet(downloadData);
                const csv = XLSX.utils.sheet_to_csv(ws);
                const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = `${fileName}.csv`;
                link.click();
            }

            Utils.showNotification(`预测结果已下载（${format.toUpperCase()}格式）`, 'success');
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🚀 页面加载完成，开始初始化...');

            // 初始化各个模块
            initializeFileUpload();
            initializeDataPreview();
            initializePrediction();
            initializeDownload();
            initializeReportGeneration();
            initializeModeSelection();

            // 初始化系统
            await initializeSystem();

            // 初始化完成后检查预测按钮状态
            setTimeout(() => {
                console.log('🔍 页面初始化完成，开始检查状态...');
                console.log('📊 初始状态:', {
                    canPredict: AppState.canPredict,
                    selectedModel: AppState.selectedModel,
                    selectedStage: AppState.selectedStage,
                    uploadedData: AppState.uploadedData ? AppState.uploadedData.length : 0,
                    selectedMode: AppState.selectedMode
                });

                updatePredictButton();
                console.log('✅ 页面初始化完成，预测按钮状态已检查');
            }, 1000);

            // 定期检查后端状态
            setInterval(checkBackendStatus, 30000); // 每30秒检查一次
        });

        // 全局错误处理
        window.addEventListener('error', function(event) {
            console.error('全局错误:', event.error);
            Utils.showNotification('系统发生错误，请刷新页面重试', 'error');
        });

        // 防止页面刷新时丢失数据提醒
        window.addEventListener('beforeunload', function(e) {
            if (AppState.uploadedData || AppState.predictionResults) {
                e.preventDefault();
                e.returnValue = '';
            }
        });

        // 键盘快捷键支持
        document.addEventListener('keydown', function(e) {
            // Ctrl/Cmd + S: 下载结果
            if ((e.ctrlKey || e.metaKey) && e.key === 's') {
                e.preventDefault();
                if (!document.getElementById('downloadExcelBtn').disabled) {
                    downloadResults('xlsx');
                }
            }
            
            // Ctrl/Cmd + P: 生成报告
            if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                e.preventDefault();
                if (!document.getElementById('generateReportBtn').disabled) {
                    generateAnalysisReport();
                }
            }
            
            // ESC: 关闭报告预览
            if (e.key === 'Escape') {
                const reportPreview = document.getElementById('reportPreview');
                if (!reportPreview.classList.contains('hidden')) {
                    closeReportPreview();
                }
            }
        });

        // 添加拖拽文件到整个页面的支持
        document.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
        });

        document.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            // 只有在文件上传区域外才处理
            if (!e.target.closest('#fileUploadArea')) {
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFiles(files);
                }
            }
        });

        console.log('✅ 系统初始化完成');

        // 引导功能相关代码
        let guideData = null;
        let currentGuideStep = 0;

        // 引导功能初始化
        async function initializeGuide() {
            const guideBtn = document.getElementById('guideBtn');
            const helpBtn = document.getElementById('helpBtn');

            guideBtn.addEventListener('click', showGuideModal);
            helpBtn.addEventListener('click', showHelpInfo);

            // 检查是否是首次访问
            const hasVisited = localStorage.getItem('hasVisitedBefore');
            if (!hasVisited) {
                // 首次访问，延迟显示引导
                setTimeout(() => {
                    showGuideModal();
                }, 2000);
                localStorage.setItem('hasVisitedBefore', 'true');
            }
        }

        // 显示引导模态框
        async function showGuideModal() {
            try {
                if (!guideData) {
                    guideData = await ApiService.getGuideInfo();
                }

                const modal = document.getElementById('guideModal');
                modal.classList.remove('hidden');

                // 绑定事件
                document.getElementById('startGuideBtn').onclick = startGuide;
                document.getElementById('skipGuideBtn').onclick = closeGuideModal;

            } catch (error) {
                console.error('获取引导信息失败:', error);
                Utils.showNotification('引导功能暂时不可用', 'warning');
            }
        }

        // 关闭引导模态框
        function closeGuideModal() {
            document.getElementById('guideModal').classList.add('hidden');
            document.getElementById('guideOverlay').classList.add('hidden');
        }

        // 开始引导
        function startGuide() {
            if (!guideData || !guideData.guide_steps) return;

            const welcomeDiv = document.querySelector('.guide-welcome');
            const stepsDiv = document.getElementById('guideSteps');

            welcomeDiv.classList.add('hidden');
            stepsDiv.classList.remove('hidden');

            currentGuideStep = 0;
            showGuideStep(currentGuideStep);
        }

        // 显示引导步骤
        function showGuideStep(stepIndex) {
            console.log('showGuideStep called with stepIndex:', stepIndex);

            if (!guideData || !guideData.guide_steps) {
                console.error('引导数据未加载');
                return;
            }

            const steps = guideData.guide_steps;
            if (stepIndex >= steps.length) {
                completeGuide();
                return;
            }

            const step = steps[stepIndex];
            const stepsDiv = document.getElementById('guideSteps');

            if (!stepsDiv) {
                console.error('找不到引导步骤容器');
                return;
            }

            // 构建HTML内容
            let html = '<div class="guide-step">';
            html += '<div class="guide-step-header">';
            html += '<h3>步骤 ' + step.step + ': ' + step.title + '</h3>';
            html += '<div class="guide-progress">';
            html += '<div class="guide-progress-bar" style="width: ' + (((stepIndex + 1) / steps.length) * 100) + '%"></div>';
            html += '</div>';
            html += '</div>';
            html += '<div class="guide-step-content">';
            html += '<p class="guide-description">' + step.description + '</p>';
            html += '<div class="guide-details">';
            html += '<h4>详细说明：</h4>';
            html += '<ul>';
            for (let i = 0; i < step.details.length; i++) {
                html += '<li>' + step.details[i] + '</li>';
            }
            html += '</ul>';
            html += '</div>';
            html += '<div class="guide-tips">';
            html += '<strong>💡 提示：</strong> ' + step.tips;
            html += '</div>';
            if (step.test_data_available) {
                html += '<div class="guide-demo-section">';
                html += '<h4>🎯 快速体验</h4>';
                html += '<p>点击下方按钮快速加载测试数据进行体验：</p>';
                html += '<button class="btn btn-demo" onclick="quickStartDemo()">🚀 一键体验</button>';
                html += '</div>';
            }
            html += '</div>';
            html += '<div class="guide-step-actions">';
            if (stepIndex > 0) {
                html += '<button class="btn btn-secondary" data-step="' + (stepIndex - 1) + '">上一步</button>';
            }
            html += '<button class="btn btn-primary" data-step="' + (stepIndex + 1) + '">';
            html += (stepIndex === steps.length - 1) ? '完成引导' : '下一步';
            html += '</button>';
            html += '<button class="btn btn-secondary" onclick="closeGuideModal()">跳过</button>';
            html += '</div>';
            html += '</div>';

            stepsDiv.innerHTML = html;

            // 添加按钮事件监听器
            const stepButtons = stepsDiv.querySelectorAll('button[data-step]');
            console.log('找到', stepButtons.length, '个步骤按钮');

            stepButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    try {
                        const nextStep = parseInt(button.dataset.step);
                        console.log('点击按钮，下一步:', nextStep);
                        showGuideStep(nextStep);
                    } catch (error) {
                        console.error('按钮点击处理错误:', error);
                    }
                });
            });

            // 高亮相关元素
            highlightGuideElement(step.step);
        }

        // 高亮引导元素
        function highlightGuideElement(stepNumber) {
            const overlay = document.getElementById('guideOverlay');
            overlay.classList.remove('hidden');

            let targetElement = null;
            switch(stepNumber) {
                case 1:
                    targetElement = document.getElementById('modelSelection');
                    break;
                case 2:
                    targetElement = document.getElementById('fileUploadArea');
                    break;
                case 3:
                    targetElement = document.getElementById('previewBtn');
                    break;
                case 4:
                    targetElement = document.getElementById('predictBtn');
                    break;
                case 5:
                    targetElement = document.querySelector('.step:last-child');
                    break;
            }

            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                targetElement.style.position = 'relative';
                targetElement.style.zIndex = '1001';
                targetElement.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.8)';

                // 清除之前的高亮
                setTimeout(() => {
                    targetElement.style.position = '';
                    targetElement.style.zIndex = '';
                    targetElement.style.boxShadow = '';
                }, 3000);
            }
        }

        // 完成引导
        function completeGuide() {
            closeGuideModal();
            Utils.showNotification('🎉 引导完成！您现在可以开始使用系统了', 'success');
        }

        // 一键体验演示
        async function quickStartDemo() {
            try {
                Utils.showNotification('🚀 正在启动快速体验...', 'info');

                // 关闭引导模态框
                closeGuideModal();

                // 等待一下让用户看到界面
                await new Promise(resolve => setTimeout(resolve, 500));

                // 1. 自动选择第一个可用模型
                const firstAvailableModel = document.querySelector('.model-card.available');
                if (firstAvailableModel) {
                    firstAvailableModel.click();
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // 2. 自动加载测试数据
                const testDataResponse = await ApiService.getTestDataList();
                if (testDataResponse.success && testDataResponse.test_files.length > 0) {
                    const firstTestFile = testDataResponse.test_files[0];
                    await loadTestDataDemo(firstTestFile.stage, firstTestFile.filename);
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }

                // 3. 自动预览数据
                const previewBtn = document.getElementById('previewBtn');
                if (!previewBtn.disabled) {
                    previewBtn.click();
                    await new Promise(resolve => setTimeout(resolve, 1500));
                }

                // 4. 自动开始预测
                const predictBtn = document.getElementById('predictBtn');
                if (!predictBtn.disabled) {
                    predictBtn.click();
                    await new Promise(resolve => setTimeout(resolve, 3000));
                }

                Utils.showNotification('✨ 快速体验完成！您可以查看预测结果并生成报告', 'success');

            } catch (error) {
                console.error('快速体验失败:', error);
                Utils.showNotification(`快速体验失败: ${error.message}`, 'error');
            }
        }

        // 显示帮助信息
        function showHelpInfo() {
            const helpContent = `
                <div class="help-content">
                    <h3>🔧 系统功能说明</h3>
                    <ul>
                        <li><strong>模型选择：</strong>根据预测需求选择合适的机器学习模型</li>
                        <li><strong>数据上传：</strong>支持CSV和Excel格式的批量数据处理</li>
                        <li><strong>智能验证：</strong>自动检查数据与模型的兼容性</li>
                        <li><strong>预测分析：</strong>使用先进算法进行质量预测</li>
                        <li><strong>结果导出：</strong>生成专业的分析报告和数据文件</li>
                    </ul>

                    <h3>⌨️ 快捷键</h3>
                    <ul>
                        <li><strong>Ctrl+S：</strong>下载预测结果</li>
                        <li><strong>Ctrl+P：</strong>生成分析报告</li>
                        <li><strong>ESC：</strong>关闭弹窗</li>
                    </ul>

                    <h3>📞 技术支持</h3>
                    <p>如遇问题，请联系技术支持团队</p>
                </div>
            `;

            const modal = document.createElement('div');
            modal.className = 'guide-modal';
            modal.innerHTML = `
                <div class="guide-modal-content">
                    <div class="guide-modal-header">
                        <h2>❓ 系统帮助</h2>
                        <button class="close-guide" onclick="this.parentElement.parentElement.parentElement.remove()">&times;</button>
                    </div>
                    <div class="guide-modal-body">
                        ${helpContent}
                    </div>
                </div>
            `;
            document.body.appendChild(modal);
        }

        // 测试数据功能
        async function showTestDataList() {
            const testDataList = document.getElementById('testDataList');
            const showTestDataBtn = document.getElementById('showTestDataBtn');

            if (!testDataList.classList.contains('hidden')) {
                testDataList.classList.add('hidden');
                showTestDataBtn.textContent = '📥 下载测试数据';
                return;
            }

            try {
                showTestDataBtn.textContent = '⏳ 加载中...';
                const response = await ApiService.getTestDataList();

                if (response.success && response.test_files.length > 0) {
                    let html = `
                        <div class="test-data-header">
                            <h4>📊 可用测试数据</h4>
                            <p>选择合适的测试数据文件进行下载和试用</p>
                        </div>
                        <div class="test-data-grid">
                    `;

                    response.test_files.forEach(file => {
                        html += `
                            <div class="test-data-card">
                                <div class="test-data-info">
                                    <h5>${file.description}</h5>
                                    <p>文件名: ${file.filename}</p>
                                    <p>阶段: ${file.stage}</p>
                                </div>
                                <div class="test-data-actions">
                                    <a href="${CONFIG.API_BASE_URL}${file.download_url}"
                                       class="btn btn-download"
                                       download="${file.filename}">
                                        📥 下载
                                    </a>
                                    <button class="btn btn-demo"
                                            onclick="loadTestDataDemo('${file.stage}', '${file.filename}')">
                                        🚀 演示
                                    </button>
                                </div>
                            </div>
                        `;
                    });

                    html += '</div>';
                    testDataList.innerHTML = html;
                    testDataList.classList.remove('hidden');
                    showTestDataBtn.textContent = '🔼 收起列表';
                } else {
                    testDataList.innerHTML = `
                        <div class="warning-box">
                            <p>暂无可用的测试数据文件</p>
                        </div>
                    `;
                    testDataList.classList.remove('hidden');
                    showTestDataBtn.textContent = '🔼 收起列表';
                }
            } catch (error) {
                console.error('获取测试数据列表失败:', error);
                testDataList.innerHTML = `
                    <div class="error-box">
                        <p>获取测试数据列表失败: ${error.message}</p>
                    </div>
                `;
                testDataList.classList.remove('hidden');
                showTestDataBtn.textContent = '🔼 收起列表';
            }
        }

        // 加载测试数据演示
        async function loadTestDataDemo(stage, filename) {
            try {
                Utils.showNotification('正在加载测试数据...', 'info');

                // 下载并解析测试数据
                const response = await fetch(`${CONFIG.API_BASE_URL}/api/test_data/${stage}`);
                if (!response.ok) {
                    throw new Error('下载测试数据失败');
                }

                const csvText = await response.text();
                const lines = csvText.split('\n').filter(line => line.trim());
                const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
                const data = [];

                // 只取前10行作为演示
                for (let i = 1; i < Math.min(11, lines.length); i++) {
                    const values = lines[i].split(',');
                    const row = {};
                    headers.forEach((header, index) => {
                        const value = values[index] ? values[index].trim().replace(/"/g, '') : '';
                        row[header] = isNaN(value) || value === '' ? value : parseFloat(value);
                    });
                    data.push(row);
                }

                // 设置数据到全局状态
                AppState.uploadedData = data;
                AppState.dataColumns = headers;
                AppState.fileName = filename;

                // 更新UI
                document.getElementById('fileDetails').innerHTML = `
                    ${filename} (测试数据, ${data.length} 行样本)
                `;
                document.getElementById('fileInfo').classList.remove('hidden');
                document.getElementById('previewBtn').disabled = false;

                // 自动选择对应的模型
                const modelCard = document.querySelector(`[data-stage="${stage}"]`);
                if (modelCard && !modelCard.dataset.disabled) {
                    modelCard.click();
                }

                // 显示数据统计
                onDataLoaded();

                Utils.showNotification(`✅ 测试数据加载成功！包含 ${data.length} 行样本`, 'success');

                // 收起测试数据列表
                document.getElementById('testDataList').classList.add('hidden');
                document.getElementById('showTestDataBtn').textContent = '📥 下载测试数据';

            } catch (error) {
                console.error('加载测试数据失败:', error);
                Utils.showNotification(`加载测试数据失败: ${error.message}`, 'error');
            }
        }

        // 初始化引导功能
        initializeGuide();

        // 辅助函数
        function getModeName(mode) {
            const modeNames = {
                'strict': '严格模式',
                'lenient': '宽松模式',
                'evaluation': '评估模式'
            };
            return modeNames[mode] || mode;
        }

        function renderEvaluationResults(evaluation) {
            if (evaluation.error) {
                return `
                    <div class="error-box" style="margin-top: 20px;">
                        <strong>❌ 评估失败:</strong> ${evaluation.error}
                    </div>
                `;
            }

            if (!evaluation.summary || evaluation.summary.status !== 'success') {
                return `
                    <div class="warning-box" style="margin-top: 20px;">
                        <strong>⚠️ 评估信息:</strong> ${evaluation.summary?.message || '评估数据不完整'}
                    </div>
                `;
            }

            const summary = evaluation.summary;
            const metrics = summary.key_metrics;
            const grade = summary.performance_grade;

            return `
                <div class="success-box" style="margin-top: 20px;">
                    <h4 style="margin-bottom: 15px;">📊 模型评估结果</h4>
                    <div class="result-stats">
                        <div class="stat-card">
                            <div class="stat-label">R² 决定系数</div>
                            <div class="stat-value">${metrics['R²']}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">RMSE 均方根误差</div>
                            <div class="stat-value">${metrics['RMSE']}</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-label">MAE 平均绝对误差</div>
                            <div class="stat-value">${metrics['MAE']}</div>
                        </div>
                        ${metrics['MAPE(%)'] ? `
                        <div class="stat-card">
                            <div class="stat-label">MAPE 平均绝对百分比误差</div>
                            <div class="stat-value">${metrics['MAPE(%)']}%</div>
                        </div>
                        ` : ''}
                    </div>
                    <div style="margin-top: 15px;">
                        <strong>🏆 性能等级:</strong> ${grade.description}<br>
                        <strong>📈 质量评估:</strong> ${summary.prediction_quality.join(', ')}<br>
                        <strong>📊 有效样本:</strong> ${summary.valid_samples}/${summary.total_samples}
                    </div>
                </div>
            `;
        }

        function renderFillReport(fillReport) {
            const addedFeatures = fillReport.filter(item => item.action === 'added_column');
            const filledFeatures = fillReport.filter(item => item.action === 'filled_missing');

            let html = `
                <div class="info-box" style="margin-top: 20px;">
                    <h4 style="margin-bottom: 15px;">🔧 数据填充报告</h4>
            `;

            if (addedFeatures.length > 0) {
                html += `
                    <div style="margin-bottom: 10px;">
                        <strong>➕ 新增特征 (${addedFeatures.length}个):</strong><br>
                        ${addedFeatures.slice(0, 5).map(item =>
                            `<small>• ${item.feature}: ${item.fill_value} (${item.method})</small>`
                        ).join('<br>')}
                        ${addedFeatures.length > 5 ? `<br><small>... 还有 ${addedFeatures.length - 5} 个特征</small>` : ''}
                    </div>
                `;
            }

            if (filledFeatures.length > 0) {
                html += `
                    <div style="margin-bottom: 10px;">
                        <strong>🔄 填充缺失值 (${filledFeatures.length}个):</strong><br>
                        ${filledFeatures.slice(0, 5).map(item =>
                            `<small>• ${item.feature}: ${item.missing_count}个缺失值 → ${item.fill_value} (${item.method})</small>`
                        ).join('<br>')}
                        ${filledFeatures.length > 5 ? `<br><small>... 还有 ${filledFeatures.length - 5} 个特征</small>` : ''}
                    </div>
                `;
            }

            html += `</div>`;
            return html;
        }
    </script>

    <!-- 外部JavaScript文件 -->
    <script src="{{ url_for('static', filename='js/enhanced_interactions.js') }}"></script>
</body>
</html>