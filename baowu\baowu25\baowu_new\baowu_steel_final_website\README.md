# 🤖 基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统

基于机器学习的油套管质量预测平台，支持多阶段模型预测和智能分析。

## 📁 项目结构

```
baowu_steel_final_website/
├── backend/                    # 后端服务
│   ├── app.py                 # Flask主应用（完整版）
│   ├── app_test.py            # Flask测试应用（简化版）
│   └── utils/                 # 工具模块
│       └── feature_transformer.py
├── frontend/                   # 前端文件
│   ├── index.html             # 主页面
│   ├── css/                   # 样式文件
│   └── js/                    # JavaScript文件
├── models/                     # 机器学习模型（18个模型）
│   ├── stage_1/               # 硬度相关预测模型（2个）
│   ├── stage_2/               # 拉伸屈服预测模型（6个）
│   ├── stage_3_A/             # A法抗硫预测模型（2个）
│   ├── stage_3_D/             # D法抗硫预测模型（3个）
│   └── stage_4/               # 硬度和拉伸预测抗硫模型（5个）
├── config/                     # 配置文件
├── requirements.txt            # Python依赖
├── install_dependencies.py    # 依赖检查安装脚本
├── start_app.bat              # Windows启动脚本
├── start_app.sh               # Linux/Mac启动脚本
└── README.md                  # 项目说明
```

## 🚀 快速开始

### 一键启动

#### Windows用户
```bash
start_app.bat
```

#### Linux/Mac用户
```bash
chmod +x start_app.sh
./start_app.sh
```

### 手动启动

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动后端服务
cd backend
python app.py

# 3. 打开前端页面
# 在浏览器中打开 frontend/index.html
```

## 📋 系统要求

- Python 3.8+
- 现代浏览器

## 🔧 主要功能

- 多阶段模型预测（5个阶段，18个模型）
- 数据上传和预处理
- 预测结果可视化
- 报告生成和导出

## 🌐 API接口

- `GET /api/models` - 获取模型列表
- `POST /api/predict` - 执行预测
- `POST /api/validate_data` - 验证数据
- `GET /health` - 健康检查

---

**宝武钢铁集团**
