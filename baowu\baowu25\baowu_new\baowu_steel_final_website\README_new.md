# 🔬 耐应力腐蚀油套管智能预测系统

## 🚀 快速启动

双击运行：`start_app.bat`

或手动启动：
```bash
cd backend
python app.py
```

访问地址：http://localhost:5000

## ✨ 三种预测模式

### 🎯 严格模式 (Strict)
- 要求输入数据包含所有必需特征
- 缺失特征时立即报错
- 确保预测结果的准确性

### 🔧 宽松模式 (Lenient) - 默认
- 自动填充缺失特征
- 基于final.csv统计数据智能填充
- 提供详细的填充报告

### 📊 评估模式 (Evaluation)
- 对含有目标值的数据进行模型效果评估
- 计算R²、RMSE、MAE、MAPE等专业指标
- 提供性能等级评估和详细报告

## 🌐 系统特色

- ✅ **智能演示模式**：无需AutoGluon即可体验完整功能
- ✅ **真实模型支持**：18个AutoGluon模型，5个预测阶段
- ✅ **现代化界面**：响应式设计，操作简单直观
- ✅ **专业评估**：完整的模型性能评估体系
- ✅ **数据智能处理**：基于985条真实数据的统计填充

## 📁 项目结构

```
baowu_steel_final_website/
├── start_app.bat              # 一键启动脚本
├── backend/                   # Flask后端
│   ├── app.py                # 主应用
│   ├── feature_statistics.py # 特征统计分析
│   ├── model_evaluator.py    # 模型评估器
│   ├── templates/            # HTML模板
│   └── static/               # 静态资源
├── models/                   # AutoGluon模型
├── test_data/               # 测试数据
├── final.csv                # 完整数据集
└── README.md                # 本文件
```

## 🔧 环境要求

### 基础要求（演示模式）
- Python 3.8+
- Flask, pandas, numpy, scikit-learn

### 完整功能（真实预测）
- 基础要求 +
- AutoGluon (`pip install autogluon`)
- 模型文件（models目录）

## 💡 使用说明

### 快速开始
1. 双击 `start_app.bat`
2. 系统自动检测环境并启动
3. 浏览器打开 http://localhost:5000
4. 选择预测模式开始使用

### API使用示例

**严格模式**：
```json
{
  "stage": "stage_1",
  "model_name": "硬度预测模型",
  "mode": "strict",
  "data": [完整特征数据]
}
```

**宽松模式**：
```json
{
  "stage": "stage_1",
  "model_name": "硬度预测模型", 
  "mode": "lenient",
  "data": [不完整特征数据]
}
```

**评估模式**：
```json
{
  "stage": "stage_1",
  "model_name": "硬度预测模型",
  "mode": "evaluation",
  "target_column": "硬度_油管硬度平均值",
  "data": [含目标值的数据]
}
```

## 🎯 主要功能

- **智能特征填充**：基于985条真实数据的统计分析
- **多模式预测**：严格、宽松、评估三种模式
- **专业评估体系**：10+种评估指标，5级性能分级
- **用户友好界面**：现代化设计，操作简单
- **完整演示模式**：无需依赖即可体验所有功能

## 📊 评估指标说明

### 性能等级
- **优秀**：R² ≥ 0.9, MAPE ≤ 5%
- **良好**：R² ≥ 0.8, MAPE ≤ 10%
- **一般**：R² ≥ 0.6, MAPE ≤ 20%
- **较差**：R² ≥ 0.3
- **很差**：R² < 0.3

### 评估指标
- **R²**：决定系数，衡量模型解释数据变异的能力
- **RMSE**：均方根误差，预测值与真实值的平均偏差
- **MAE**：平均绝对误差，预测误差的平均值
- **MAPE**：平均绝对百分比误差，相对误差的百分比

---

© 2024 宝武钢铁集团
