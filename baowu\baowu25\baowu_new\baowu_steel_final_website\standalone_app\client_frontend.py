#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
耐应力腐蚀油套管智能预测系统 - 客户端
运行在Windows上，连接Linux服务器
"""

import os
import sys
import json
import webbrowser
import threading
import time
import shutil
import tempfile
import logging
import requests
import socket
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import socketserver

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 全局配置
CONFIG = {
    'HOST': 'localhost',
    'PORT': None,                    # 将自动寻找可用端口
    'APP_NAME': '耐应力腐蚀油套管智能预测系统',
    'VERSION': '2.0 客户端版',
    'SERVER_HOST': '127.0.0.1',      # 默认服务器IP，启动时可修改
    'SERVER_PORT': 5000,             # 默认服务器端口
    'SERVER_URL': None               # 将在启动时设置
}

class ClientHandler(SimpleHTTPRequestHandler):
    """客户端HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=get_web_root(), **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        if self.path == '/':
            self.path = '/index.html'
        elif self.path.startswith('/api/'):
            self.handle_api_request()
            return
        
        super().do_GET()
    
    def do_POST(self):
        """处理POST请求"""
        if self.path.startswith('/api/'):
            self.handle_api_request()
        else:
            self.send_error(404)
    
    def handle_api_request(self):
        """处理API请求 - 转发到服务器"""
        try:
            # 构建服务器URL
            server_url = f"{CONFIG['SERVER_URL']}{self.path}"
            
            if self.command == 'GET':
                # GET请求
                response = requests.get(server_url, timeout=30)
            elif self.command == 'POST':
                # POST请求
                content_length = int(self.headers.get('Content-Length', 0))
                post_data = self.rfile.read(content_length) if content_length > 0 else b''
                
                headers = {'Content-Type': 'application/json'}
                response = requests.post(server_url, data=post_data, headers=headers, timeout=30)
            else:
                self.send_error(405, "Method Not Allowed")
                return
            
            # 转发响应
            self.send_response(response.status_code)
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            self.end_headers()
            self.wfile.write(response.content)
            
        except requests.exceptions.ConnectionError:
            logger.error(f"❌ 无法连接到服务器: {CONFIG['SERVER_URL']}")
            self.send_json_response({
                'success': False, 
                'error': f'无法连接到服务器 {CONFIG["SERVER_HOST"]}:{CONFIG["SERVER_PORT"]}'
            })
        except requests.exceptions.Timeout:
            logger.error("❌ 服务器响应超时")
            self.send_json_response({
                'success': False, 
                'error': '服务器响应超时，请稍后重试'
            })
        except Exception as e:
            logger.error(f"API请求处理错误: {e}")
            self.send_json_response({
                'success': False, 
                'error': f'请求处理失败: {str(e)}'
            })
    
    def send_json_response(self, data):
        """发送JSON响应"""
        response = json.dumps(data, ensure_ascii=False, indent=2)
        self.send_response(500)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        self.wfile.write(response.encode('utf-8'))

def find_available_port(start_port=8080, max_attempts=50):
    """寻找可用端口"""
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                logger.info(f"✅ 找到可用端口: {port}")
                return port
        except OSError:
            continue

    # 如果没找到可用端口，使用系统分配
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        s.bind(('localhost', 0))
        port = s.getsockname()[1]
        logger.info(f"✅ 使用系统分配端口: {port}")
        return port

def get_web_root():
    """获取Web根目录"""
    if getattr(sys, 'frozen', False):
        # 如果是打包后的可执行文件
        return Path(sys._MEIPASS) / 'web'
    else:
        # 开发环境
        return Path(__file__).parent / 'client_web'

def setup_client_web_files():
    """设置客户端Web文件"""
    web_root = get_web_root()
    web_root.mkdir(parents=True, exist_ok=True)
    
    source_dir = Path(__file__).parent.parent
    
    logger.info(f"设置Web文件到: {web_root}")
    
    # 复制静态文件
    static_dir = web_root / 'static'
    source_static = source_dir / 'backend' / 'static'
    if source_static.exists():
        if static_dir.exists():
            shutil.rmtree(static_dir)
        shutil.copytree(source_static, static_dir)
        logger.info("✅ 静态文件已复制")
    
    # 处理HTML文件
    html_source = source_dir / 'backend' / 'templates' / 'index.html'
    html_target = web_root / 'index.html'
    if html_source.exists():
        convert_flask_template_to_static(html_source, html_target)
        logger.info("✅ HTML文件已转换")
    
    # 复制测试数据（客户端也需要测试数据下载功能）
    test_data_dir = web_root / 'test_data'
    source_test_data = source_dir / 'test_data'
    if source_test_data.exists():
        if test_data_dir.exists():
            shutil.rmtree(test_data_dir)
        shutil.copytree(source_test_data, test_data_dir)
        logger.info("✅ 测试数据已复制")

def convert_flask_template_to_static(source_file, target_file):
    """将Flask模板语法转换为静态路径"""
    try:
        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换Flask模板语法为静态路径
        replacements = [
            ("{{ url_for('static', filename='css/enhanced_styles.css') }}", "static/css/enhanced_styles.css"),
            ("{{ url_for('static', filename='css/main.css') }}", "static/css/main.css"),
            ("{{ url_for('static', filename='js/enhanced_interactions.js') }}", "static/js/enhanced_interactions.js"),
        ]
        
        for old, new in replacements:
            content = content.replace(old, new)
        
        with open(target_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        logger.info(f"✅ HTML模板已转换: {target_file}")
        
    except Exception as e:
        logger.error(f"⚠️ HTML模板转换失败: {e}")
        shutil.copy2(source_file, target_file)

def test_server_connection():
    """测试服务器连接"""
    try:
        response = requests.get(f"{CONFIG['SERVER_URL']}/api/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ 服务器连接成功: {data.get('message', '未知状态')}")
            logger.info(f"📊 服务器版本: {data.get('version', '未知版本')}")
            logger.info(f"🤖 AutoGluon状态: {'可用' if data.get('autogluon_available') else '不可用'}")
            return True
        else:
            logger.error(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 服务器连接失败: {e}")
        return False

def get_server_config():
    """获取服务器配置"""
    print("🔧 配置服务器连接")
    print("=" * 40)

    # 检查环境变量
    env_host = os.environ.get('SERVER_HOST')
    env_port = os.environ.get('SERVER_PORT')

    if env_host:
        CONFIG['SERVER_HOST'] = env_host
        logger.info(f"📡 使用环境变量服务器IP: {env_host}")

    if env_port:
        try:
            CONFIG['SERVER_PORT'] = int(env_port)
            logger.info(f"📡 使用环境变量服务器端口: {env_port}")
        except ValueError:
            logger.warning("环境变量端口格式错误，使用默认端口")

    # 设置服务器URL
    CONFIG['SERVER_URL'] = f"http://{CONFIG['SERVER_HOST']}:{CONFIG['SERVER_PORT']}"

    print(f"📡 服务器地址: {CONFIG['SERVER_URL']}")

    # 如果没有环境变量，提供交互式配置
    if not env_host and not env_port:
        print("💡 如需修改服务器地址，请设置环境变量 SERVER_HOST 和 SERVER_PORT")
        print("💡 或者按回车使用默认配置")

        user_input = input("按回车继续，或输入 'config' 进行配置: ").strip().lower()
        if user_input == 'config':
            # 获取服务器IP
            server_host = input(f"请输入服务器IP地址 (默认: {CONFIG['SERVER_HOST']}): ").strip()
            if server_host:
                CONFIG['SERVER_HOST'] = server_host

            # 获取服务器端口
            server_port = input(f"请输入服务器端口 (默认: {CONFIG['SERVER_PORT']}): ").strip()
            if server_port:
                try:
                    CONFIG['SERVER_PORT'] = int(server_port)
                except ValueError:
                    logger.warning("端口格式错误，使用默认端口")

            # 重新设置服务器URL
            CONFIG['SERVER_URL'] = f"http://{CONFIG['SERVER_HOST']}:{CONFIG['SERVER_PORT']}"
            print(f"📡 更新后服务器地址: {CONFIG['SERVER_URL']}")

def start_client():
    """启动客户端"""
    try:
        logger.info(f"🚀 {CONFIG['APP_NAME']} v{CONFIG['VERSION']}")
        logger.info("=" * 60)
        
        # 获取服务器配置
        get_server_config()
        
        # 测试服务器连接
        logger.info("🔍 测试服务器连接...")
        if not test_server_connection():
            logger.error("❌ 无法连接到服务器，请检查服务器是否启动")
            input("按回车键退出...")
            return
        
        # 设置Web文件
        setup_client_web_files()

        # 寻找可用端口
        CONFIG['PORT'] = find_available_port()

        # 创建服务器
        with socketserver.TCPServer((CONFIG['HOST'], CONFIG['PORT']), ClientHandler) as httpd:
            logger.info(f"🌐 客户端启动成功: http://{CONFIG['HOST']}:{CONFIG['PORT']}")
            logger.info(f"📁 Web根目录: {get_web_root()}")
            logger.info(f"📡 后端服务器: {CONFIG['SERVER_URL']}")
            logger.info("🔧 按 Ctrl+C 停止服务")
            logger.info("=" * 60)
            
            # 延迟打开浏览器
            def open_browser():
                time.sleep(3)
                try:
                    webbrowser.open(f'http://{CONFIG["HOST"]}:{CONFIG["PORT"]}')
                    logger.info("🌍 已自动打开浏览器")
                except:
                    logger.warning("⚠️ 无法自动打开浏览器，请手动访问上述地址")
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        logger.info("\n👋 客户端已停止")
    except Exception as e:
        logger.error(f"❌ 客户端启动失败: {e}")
        input("按回车键退出...")

if __name__ == '__main__':
    start_client()
