📁 standalone_app/ - 两套完整版本目录

🔹 方案一：客户端-服务器分离版
├── server_backend.py                   # 服务器端（Linux/Windows）
├── client_frontend.py                  # 客户端（Windows）
├── start_server.sh                     # Linux服务器启动脚本
├── 启动服务器.bat                      # Windows服务器启动脚本
├── 启动客户端.bat                      # Windows客户端启动脚本
├── test_client_server.py               # 测试脚本
└── client_web/                         # 客户端Web资源

🔹 方案二：本地完整版
├── complete_standalone_app.py          # 本地完整版程序
├── build_complete_distribution.py      # 单版本构建脚本
├── 构建发布包.bat                      # 单版本构建工具
└── complete_web/                       # 完整版Web资源

🔹 两套版本构建
├── build_two_versions.py               # 两套版本构建脚本
├── 构建两套完整版.bat                  # 两套版本构建工具
└── two_versions_distribution/          # 构建输出（运行后生成）
    ├── 客户端-服务器分离版/
    │   ├── 服务器端(Linux)/
    │   └── 客户端(Windows)/
    └── 本地完整版(Windows)/

🔹 通用文件
├── README.md                           # 使用说明
└── 项目结构.txt                        # 本文件

🚀 快速开始：

方案一（分离版）：
1. 服务器端：python server_backend.py 或 ./start_server.sh
2. 客户端：python client_frontend.py 或双击 启动客户端.bat

方案二（本地版）：
1. 测试运行：python complete_standalone_app.py
2. 构建发布：双击 构建发布包.bat

两套版本构建：
双击 构建两套完整版.bat

✨ 主要特色：
- 支持客户端-服务器分离部署
- 支持本地完整版部署
- 包含18个真实AutoGluon模型
- 完整前端资源（已修复渲染问题）
- 专业数据验证和预测功能
- 智能操作引导系统
