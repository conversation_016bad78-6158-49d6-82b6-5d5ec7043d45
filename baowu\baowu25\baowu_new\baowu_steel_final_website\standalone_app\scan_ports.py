#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扫描运行中的服务端口
"""

import requests
import socket

def scan_ports():
    """扫描端口"""
    print("🔍 扫描运行中的服务...")
    print("=" * 50)
    
    # 扫描常见端口范围
    ports_to_scan = list(range(5000, 5010)) + list(range(8080, 8100))
    
    running_services = []
    
    for port in ports_to_scan:
        try:
            # 检查端口是否被占用
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(0.1)
                result = s.connect_ex(('127.0.0.1', port))
                if result == 0:  # 端口被占用
                    # 尝试HTTP请求
                    try:
                        response = requests.get(f'http://127.0.0.1:{port}', timeout=1)
                        service_type = "HTTP服务"
                        
                        # 尝试健康检查
                        try:
                            health_response = requests.get(f'http://127.0.0.1:{port}/api/health', timeout=1)
                            if health_response.status_code == 200:
                                data = health_response.json()
                                service_type = f"预测系统 - {data.get('version', '未知版本')}"
                        except:
                            pass
                            
                        running_services.append((port, service_type, "✅"))
                        
                    except requests.exceptions.RequestException:
                        running_services.append((port, "非HTTP服务", "⚠️"))
                        
        except Exception:
            continue
    
    # 输出结果
    if running_services:
        print("🌐 发现运行中的服务:")
        print("-" * 50)
        for port, service_type, status in running_services:
            print(f"{status} 端口 {port}: {service_type}")
            
            # 如果是预测系统，测试API
            if "预测系统" in service_type:
                try:
                    # 测试模型接口
                    models_response = requests.get(f'http://127.0.0.1:{port}/api/models', timeout=2)
                    if models_response.status_code == 200:
                        data = models_response.json()
                        if data.get('success'):
                            models = data.get('models', {})
                            model_count = sum(len(stage_models) for stage_models in models.values())
                            print(f"   📊 模型数量: {model_count}")
                        
                    # 测试引导接口
                    guide_response = requests.get(f'http://127.0.0.1:{port}/api/guide', timeout=2)
                    if guide_response.status_code == 200:
                        print(f"   📋 引导接口: 正常")
                        
                except Exception as e:
                    print(f"   ⚠️ API测试失败: {e}")
                    
        print("-" * 50)
        print(f"总计发现 {len(running_services)} 个运行中的服务")
        
        # 验证两套方案
        server_found = any("服务器版" in service for _, service, _ in running_services)
        complete_found = any("完整版" in service for _, service, _ in running_services)
        
        print("\n🎯 方案验证:")
        print(f"服务器端 (分离版): {'✅ 运行中' if server_found else '❌ 未运行'}")
        print(f"本地完整版: {'✅ 运行中' if complete_found else '❌ 未运行'}")
        
        if server_found and complete_found:
            print("\n🎉 两套完整版方案都在正常运行！")
            return True
        elif server_found or complete_found:
            print("\n✅ 至少有一套方案在正常运行")
            return True
        else:
            print("\n⚠️ 预测系统服务未检测到")
            return False
            
    else:
        print("❌ 未发现运行中的HTTP服务")
        return False

if __name__ == '__main__':
    scan_ports()
