"""
特征变换管理模块
"""
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.preprocessing import PowerTransformer, QuantileTransformer, RobustScaler
from sklearn.compose import ColumnTransformer
import json
import pickle
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

class FeatureTransformer:
    """特征变换管理器，用于处理高偏度特征的检测、变换和记录"""

    def __init__(self, config_path=None):
        self.high_skew_features = {
            '转速27': {'threshold': 31.38, 'transform': 'yeo_johnson', 'priority': 'extreme'},
            '转速23': {'threshold': 31.38, 'transform': 'yeo_johnson', 'priority': 'extreme'},
            '辅助时间': {'threshold': 24.99, 'transform': 'yeo_johnson', 'priority': 'extreme'},
            '电流22': {'threshold': 17.59, 'transform': 'yeo_johnson', 'priority': 'high'},
            '转速22': {'threshold': 14.38, 'transform': 'yeo_johnson', 'priority': 'high'},
            '电流21': {'threshold': 14.07, 'transform': 'yeo_johnson', 'priority': 'high'},
            '转速26': {'threshold': 11.05, 'transform': 'yeo_johnson', 'priority': 'high'},
            '电流26': {'threshold': 9.77, 'transform': 'power_transformer', 'priority': 'high'},
            '电流20': {'threshold': 6.76, 'transform': 'power_transformer', 'priority': 'medium'},
            '电流19': {'threshold': 6.69, 'transform': 'power_transformer', 'priority': 'medium'},
            '转速20': {'threshold': 5.63, 'transform': 'power_transformer', 'priority': 'medium'},
            '转速19': {'threshold': 5.43, 'transform': 'power_transformer', 'priority': 'medium'},
            '2机架转速调整': {'threshold': 5.16, 'transform': 'quantile_uniform', 'priority': 'medium'},
            '管坯重量': {'threshold': 4.55, 'transform': 'power_transformer', 'priority': 'medium'},
            '1机架辊缝调整': {'threshold': 4.39, 'transform': 'quantile_uniform', 'priority': 'medium'},
            '铈实际值': {'threshold': 3.76, 'transform': 'log1p', 'priority': 'medium'},
            'AR': {'threshold': 3.70, 'transform': 'power_transformer', 'priority': 'medium'},
            '6机架转速调整': {'threshold': 3.10, 'transform': 'quantile_uniform', 'priority': 'low'},
            '回退电流(%)': {'threshold': 2.60, 'transform': 'log1p', 'priority': 'low'},
            '2机架辊缝调整': {'threshold': 2.39, 'transform': 'quantile_uniform', 'priority': 'low'}
        }
        
        self.transform_history = {}
        self.config_path = config_path

        # 初始化高级变换器
        self.power_transformer = PowerTransformer(method='yeo-johnson', standardize=True)
        self.quantile_transformer_uniform = QuantileTransformer(output_distribution='uniform', n_quantiles=1000)
        self.quantile_transformer_normal = QuantileTransformer(output_distribution='normal', n_quantiles=1000)
        self.robust_scaler = RobustScaler()

        # 存储已拟合的变换器
        self.fitted_transformers = {}

        if config_path and Path(config_path).exists():
            self.load_config(config_path)
    
    def detect_skew_features(self, df, skew_threshold=2.0):
        """
        检测数据中的高偏度特征
        
        Args:
            df: 输入数据框
            skew_threshold: 偏度阈值
        
        Returns:
            dict: 高偏度特征信息
        """
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        skew_features = {}
        
        for col in numeric_cols:
            if col in df.columns and len(df[col].dropna()) > 0:
                try:
                    skewness = stats.skew(df[col].dropna())
                    if abs(skewness) > skew_threshold:
                        skew_features[col] = {
                            'skewness': float(skewness),
                            'recommended_transform': self._recommend_transform(skewness),
                            'data_range': {
                                'min': float(df[col].min()),
                                'max': float(df[col].max()),
                                'mean': float(df[col].mean()),
                                'std': float(df[col].std())
                            }
                        }
                except Exception as e:
                    print(f"计算特征 {col} 偏度时出错: {str(e)}")
                    continue
        
        return skew_features
    
    def _recommend_transform(self, skewness):
        """
        推荐变换类型 - 基于偏度程度智能选择最优变换方法

        Args:
            skewness: 特征的偏度值

        Returns:
            str: 推荐的变换方法
        """
        abs_skew = abs(skewness)

        if abs_skew > 10:
            # 极高偏度：使用Yeo-Johnson变换，能处理任意实数且效果最佳
            return 'yeo_johnson'
        elif abs_skew > 5:
            # 高偏度：使用PowerTransformer，自动寻找最优参数
            return 'power_transformer'
        elif abs_skew > 2:
            # 中等偏度：使用分位数变换，稳定且鲁棒
            return 'quantile_uniform'
        elif abs_skew > 1:
            # 轻微偏度：使用简单的对数变换
            return 'log1p' if skewness > 0 else 'sqrt'
        else:
            # 接近正态：使用鲁棒标准化
            return 'robust_scale'
    
    def apply_transform(self, df, feature_name, transform_type='power_transformer'):
        """
        对特征应用高级变换方法

        Args:
            df: 输入数据框
            feature_name: 特征名称
            transform_type: 变换类型

        Returns:
            pd.Series: 变换后的特征
        """
        if feature_name not in df.columns:
            return None

        feature_data = df[feature_name].copy()
        original_skew = stats.skew(feature_data.dropna())

        # 处理缺失值
        feature_data = feature_data.fillna(feature_data.median())

        try:
            transformer_key = f"{feature_name}_{transform_type}"

            if transform_type == 'power_transformer':
                # PowerTransformer (Yeo-Johnson) - 最推荐的方法
                if transformer_key not in self.fitted_transformers:
                    transformer = PowerTransformer(method='yeo-johnson', standardize=True)
                    transformed = transformer.fit_transform(feature_data.values.reshape(-1, 1)).flatten()
                    self.fitted_transformers[transformer_key] = transformer
                else:
                    transformer = self.fitted_transformers[transformer_key]
                    transformed = transformer.transform(feature_data.values.reshape(-1, 1)).flatten()

            elif transform_type == 'yeo_johnson':
                # 直接使用scipy的Yeo-Johnson变换
                transformed, lambda_param = stats.yeojohnson(feature_data)
                self.fitted_transformers[transformer_key] = {'lambda': lambda_param}

            elif transform_type == 'quantile_uniform':
                # 分位数变换到均匀分布 - 对异常值鲁棒
                if transformer_key not in self.fitted_transformers:
                    transformer = QuantileTransformer(output_distribution='uniform', n_quantiles=min(1000, len(feature_data)))
                    transformed = transformer.fit_transform(feature_data.values.reshape(-1, 1)).flatten()
                    self.fitted_transformers[transformer_key] = transformer
                else:
                    transformer = self.fitted_transformers[transformer_key]
                    transformed = transformer.transform(feature_data.values.reshape(-1, 1)).flatten()

            elif transform_type == 'quantile_normal':
                # 分位数变换到正态分布
                if transformer_key not in self.fitted_transformers:
                    transformer = QuantileTransformer(output_distribution='normal', n_quantiles=min(1000, len(feature_data)))
                    transformed = transformer.fit_transform(feature_data.values.reshape(-1, 1)).flatten()
                    self.fitted_transformers[transformer_key] = transformer
                else:
                    transformer = self.fitted_transformers[transformer_key]
                    transformed = transformer.transform(feature_data.values.reshape(-1, 1)).flatten()

            elif transform_type == 'robust_scale':
                # 鲁棒标准化 - 对异常值不敏感
                if transformer_key not in self.fitted_transformers:
                    transformer = RobustScaler()
                    transformed = transformer.fit_transform(feature_data.values.reshape(-1, 1)).flatten()
                    self.fitted_transformers[transformer_key] = transformer
                else:
                    transformer = self.fitted_transformers[transformer_key]
                    transformed = transformer.transform(feature_data.values.reshape(-1, 1)).flatten()

            elif transform_type == 'log1p':
                # 传统对数变换
                transformed = np.log1p(np.maximum(feature_data, 0))

            elif transform_type == 'sqrt':
                # 平方根变换
                transformed = np.sqrt(np.maximum(feature_data, 0))

            elif transform_type == 'boxcox':
                # Box-Cox变换（需要正值）
                if (feature_data > 0).all():
                    transformed, lambda_param = stats.boxcox(feature_data)
                    self.fitted_transformers[transformer_key] = {'lambda': lambda_param}
                else:
                    # 如果有非正值，回退到log1p
                    transformed = np.log1p(np.maximum(feature_data, 0))

            else:
                transformed = feature_data

            new_skew = stats.skew(transformed)
            improvement = abs(original_skew) - abs(new_skew)

            # 记录详细的变换信息
            self.transform_history[feature_name] = {
                'transform_type': transform_type,
                'original_skew': float(original_skew),
                'new_skew': float(new_skew),
                'improvement': float(improvement),
                'improvement_rate': float(improvement / abs(original_skew)) if abs(original_skew) > 0 else 0,
                'original_stats': {
                    'mean': float(feature_data.mean()),
                    'std': float(feature_data.std()),
                    'min': float(feature_data.min()),
                    'max': float(feature_data.max())
                },
                'transformed_stats': {
                    'mean': float(np.mean(transformed)),
                    'std': float(np.std(transformed)),
                    'min': float(np.min(transformed)),
                    'max': float(np.max(transformed))
                }
            }

            return pd.Series(transformed, index=feature_data.index, name=feature_name)

        except Exception as e:
            print(f"变换特征 {feature_name} 时出错: {str(e)}")
            print(f"回退到简单log1p变换")
            # 回退到简单变换
            transformed = np.log1p(np.maximum(feature_data, 0))
            return pd.Series(transformed, index=feature_data.index, name=feature_name)
    
    def transform_dataset(self, df, auto_detect=True, custom_transforms=None):
        """
        对整个数据集应用特征变换
        
        Args:
            df: 输入数据框
            auto_detect: 是否自动检测高偏度特征
            custom_transforms: 自定义变换配置
        
        Returns:
            tuple: (变换后的数据框, 变换记录)
        """
        df_transformed = df.copy()
        applied_transforms = {}
        
        # 自动检测高偏度特征
        if auto_detect:
            detected_skew = self.detect_skew_features(df_transformed)
            print(f"检测到 {len(detected_skew)} 个高偏度特征")
        
        # 应用预定义的变换
        for feature_name, config in self.high_skew_features.items():
            if feature_name in df_transformed.columns:
                current_skew = stats.skew(df_transformed[feature_name].dropna())
                
                # 只对确实高偏度的特征进行变换
                if abs(current_skew) > 2.0:
                    transform_type = config.get('transform', 'log1p')
                    transformed_feature = self.apply_transform(
                        df_transformed, feature_name, transform_type
                    )
                    
                    if transformed_feature is not None:
                        df_transformed[feature_name] = transformed_feature
                        applied_transforms[feature_name] = self.transform_history[feature_name]
        
        # 应用自定义变换
        if custom_transforms:
            for feature_name, transform_type in custom_transforms.items():
                if feature_name in df_transformed.columns:
                    transformed_feature = self.apply_transform(
                        df_transformed, feature_name, transform_type
                    )
                    if transformed_feature is not None:
                        df_transformed[feature_name] = transformed_feature
                        applied_transforms[feature_name] = self.transform_history[feature_name]
        
        return df_transformed, applied_transforms
    
    def save_config(self, path):
        """保存配置到文件"""
        config = {
            'high_skew_features': self.high_skew_features,
            'transform_history': self.transform_history
        }
        
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
    
    def load_config(self, path):
        """从文件加载配置"""
        with open(path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        self.high_skew_features.update(config.get('high_skew_features', {}))
        self.transform_history.update(config.get('transform_history', {}))
    
    def get_transform_summary(self):
        """获取变换摘要"""
        return {
            'predefined_features': len(self.high_skew_features),
            'transform_history': self.transform_history,
            'available_transforms': [
                'log1p', 'sqrt', 'square', 'boxcox', 'yeo_johnson'
            ]
        }
    
    def recommend_transforms_for_data(self, df):
        """为数据推荐变换方案"""
        skew_features = self.detect_skew_features(df)
        recommendations = {}
        
        for feature, info in skew_features.items():
            recommendations[feature] = {
                'current_skew': info['skewness'],
                'recommended_transform': info['recommended_transform'],
                'priority': 'high' if abs(info['skewness']) > 5 else 'medium'
            }
        
        return recommendations

# 全局特征变换器实例
feature_transformer = FeatureTransformer()
