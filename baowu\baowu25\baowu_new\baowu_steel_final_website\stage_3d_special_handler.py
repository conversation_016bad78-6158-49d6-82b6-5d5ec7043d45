#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
第三阶段D-抗硫因子特殊处理器
针对第三阶段D数据稀少的特殊情况（每列只有4个有效数据），采用保守策略
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class Stage3DSpecialHandler:
    """第三阶段D特殊处理器"""
    
    def __init__(self, input_dir="test_data", output_dir="stage_3d_special_filtered"):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 可视化输出目录
        self.viz_dir = Path("stage_3d_special_analysis")
        self.viz_dir.mkdir(exist_ok=True)
        
        # 第三阶段D配置
        self.stage_config = {
            'stage': 'stage_3_D',
            'file': 'test_stage_3_D.csv',
            'targets': ['抗硫_平均抗硫因子', '抗硫_最大抗硫因子', '抗硫_最小抗硫因子'],
            'stage_name': '第三阶段D-抗硫因子',
            'description': 'D法——抗硫预测模型',
            'output_name': 'special_filtered_test_stage_3_D.csv'
        }
    
    def load_test_data(self, test_file):
        """加载测试数据"""
        try:
            test_path = self.input_dir / test_file
            if not test_path.exists():
                logger.warning(f"测试文件不存在: {test_path}")
                return None
            
            data = pd.read_csv(test_path)
            logger.info(f"加载测试数据: {test_file}, 形状: {data.shape}")
            return data
        except Exception as e:
            logger.error(f"加载测试数据失败 {test_file}: {e}")
            return None
    
    def analyze_data_availability(self, data):
        """分析数据可用性"""
        logger.info("📊 分析第三阶段D数据可用性...")
        
        analysis = {}
        for target in self.stage_config['targets']:
            if target in data.columns:
                valid_count = data[target].notna().sum()
                total_count = len(data)
                valid_rate = valid_count / total_count * 100
                
                analysis[target] = {
                    'total_samples': total_count,
                    'valid_samples': valid_count,
                    'missing_samples': total_count - valid_count,
                    'valid_rate': valid_rate
                }
                
                logger.info(f"  {target}:")
                logger.info(f"    总样本: {total_count}")
                logger.info(f"    有效样本: {valid_count}")
                logger.info(f"    有效率: {valid_rate:.1f}%")
                
                if valid_count > 0:
                    values = data[target].dropna().values
                    logger.info(f"    数值范围: {values.min():.3f} ~ {values.max():.3f}")
        
        return analysis
    
    def create_data_availability_visualization(self, analysis):
        """创建数据可用性可视化"""
        targets = list(analysis.keys())
        valid_counts = [analysis[target]['valid_samples'] for target in targets]
        missing_counts = [analysis[target]['missing_samples'] for target in targets]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        fig.suptitle('第三阶段D-抗硫因子数据可用性分析', fontsize=16)
        
        # 1. 堆叠柱状图
        x = np.arange(len(targets))
        width = 0.6
        
        ax1.bar(x, valid_counts, width, label='有效数据', color='lightgreen', alpha=0.8)
        ax1.bar(x, missing_counts, width, bottom=valid_counts, label='缺失数据', color='lightcoral', alpha=0.8)
        
        ax1.set_xlabel('目标列')
        ax1.set_ylabel('样本数量')
        ax1.set_title('数据可用性分布')
        ax1.set_xticks(x)
        ax1.set_xticklabels([t.replace('抗硫_', '') for t in targets], rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 在柱状图上添加数值标签
        for i, (valid, missing) in enumerate(zip(valid_counts, missing_counts)):
            ax1.text(i, valid/2, str(valid), ha='center', va='center', fontweight='bold')
            ax1.text(i, valid + missing/2, str(missing), ha='center', va='center', fontweight='bold')
        
        # 2. 有效率饼图
        valid_rates = [analysis[target]['valid_rate'] for target in targets]
        colors = ['lightgreen', 'lightblue', 'lightyellow']
        
        wedges, texts, autotexts = ax2.pie(valid_rates, labels=[t.replace('抗硫_', '') for t in targets], 
                                          autopct='%1.1f%%', colors=colors, startangle=90)
        ax2.set_title('有效数据比例')
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.viz_dir / 'stage_3d_data_availability_analysis.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"数据可用性分析图已保存: {output_path}")
    
    def apply_conservative_strategy(self, data):
        """应用保守策略处理第三阶段D数据"""
        logger.info("🛡️ 应用保守策略处理第三阶段D数据...")
        
        # 策略：由于有效数据太少，保留所有有任一目标列有效数据的行
        target_columns = self.stage_config['targets']
        
        # 找到至少有一个目标列有效数据的行
        has_any_valid = data[target_columns].notna().any(axis=1)
        
        # 统计信息
        total_rows = len(data)
        valid_rows = has_any_valid.sum()
        
        logger.info(f"保守策略统计:")
        logger.info(f"  总行数: {total_rows}")
        logger.info(f"  至少有一个目标列有效的行数: {valid_rows}")
        logger.info(f"  保留率: {valid_rows/total_rows*100:.1f}%")
        
        # 应用筛选
        filtered_data = data[has_any_valid].copy()
        
        # 详细统计每个目标列在筛选后的情况
        logger.info("筛选后各目标列统计:")
        for target in target_columns:
            if target in filtered_data.columns:
                valid_count = filtered_data[target].notna().sum()
                logger.info(f"  {target}: {valid_count}个有效数据")
        
        return filtered_data, {
            'strategy': 'conservative',
            'description': '保留所有至少有一个目标列有效数据的行',
            'original_samples': total_rows,
            'filtered_samples': valid_rows,
            'retention_rate': valid_rows / total_rows,
            'target_columns': target_columns
        }
    
    def create_strategy_comparison_visualization(self, original_data, filtered_data):
        """创建策略对比可视化"""
        targets = self.stage_config['targets']
        
        original_counts = []
        filtered_counts = []
        
        for target in targets:
            original_count = original_data[target].notna().sum()
            filtered_count = filtered_data[target].notna().sum()
            original_counts.append(original_count)
            filtered_counts.append(filtered_count)
        
        fig, ax = plt.subplots(1, 1, figsize=(12, 6))
        fig.suptitle('第三阶段D保守策略效果对比', fontsize=16)
        
        x = np.arange(len(targets))
        width = 0.35
        
        bars1 = ax.bar(x - width/2, original_counts, width, label='原始有效数据', alpha=0.8, color='lightblue')
        bars2 = ax.bar(x + width/2, filtered_counts, width, label='筛选后有效数据', alpha=0.8, color='lightgreen')
        
        ax.set_xlabel('目标列')
        ax.set_ylabel('有效数据数量')
        ax.set_title('保守策略前后对比')
        ax.set_xticks(x)
        ax.set_xticklabels([t.replace('抗硫_', '') for t in targets], rotation=45, ha='right')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 在柱状图上添加数值标签
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.05,
                       f'{int(height)}', ha='center', va='bottom')
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.viz_dir / 'stage_3d_conservative_strategy_comparison.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"保守策略对比图已保存: {output_path}")
    
    def process_stage_3d_special(self):
        """特殊处理第三阶段D"""
        logger.info("🔄 开始特殊处理第三阶段D-抗硫因子...")
        
        # 加载数据
        test_data = self.load_test_data(self.stage_config['file'])
        if test_data is None:
            return None
        
        # 分析数据可用性
        analysis = self.analyze_data_availability(test_data)
        self.create_data_availability_visualization(analysis)
        
        # 应用保守策略
        filtered_data, strategy_info = self.apply_conservative_strategy(test_data)
        
        # 创建对比可视化
        self.create_strategy_comparison_visualization(test_data, filtered_data)
        
        # 保存筛选后的数据
        output_file = self.output_dir / self.stage_config['output_name']
        filtered_data.to_csv(output_file, index=False)
        
        logger.info(f"✅ 第三阶段D特殊处理完成: "
                   f"{len(test_data)} → {len(filtered_data)} 样本 "
                   f"(保留 {len(filtered_data)/len(test_data)*100:.1f}%)")
        
        filter_info = {
            'stage': self.stage_config['stage'],
            'stage_name': self.stage_config['stage_name'],
            'description': self.stage_config['description'],
            'original_samples': len(test_data),
            'filtered_samples': len(filtered_data),
            'retention_rate': len(filtered_data) / len(test_data),
            'output_file': str(output_file),
            'target_columns': self.stage_config['targets'],
            'strategy_info': strategy_info,
            'data_analysis': analysis
        }
        
        return filter_info
    
    def run_special_processing(self):
        """运行第三阶段D特殊处理"""
        logger.info("🚀 开始第三阶段D-抗硫因子特殊处理...")
        
        filter_info = self.process_stage_3d_special()
        
        # 生成处理报告
        self.generate_processing_report(filter_info)
        
        logger.info("✅ 第三阶段D特殊处理完成!")
    
    def generate_processing_report(self, filter_info):
        """生成处理报告"""
        report = {
            'processing_summary': {
                'method': 'stage_3d_special_conservative_processing',
                'description': '第三阶段D-抗硫因子特殊处理，采用保守策略应对数据稀少问题',
                'strategy': 'conservative',
                'reason': '目标列有效数据太少（每列只有4个有效数据，有效率8%）',
                'timestamp': pd.Timestamp.now().isoformat()
            },
            'filter_info': filter_info
        }
        
        # 保存报告
        report_file = self.output_dir / "stage_3d_special_processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"📊 特殊处理报告已保存: {report_file}")
        
        # 打印摘要
        self.print_processing_summary(filter_info)
    
    def print_processing_summary(self, filter_info):
        """打印处理摘要"""
        print("\n" + "="*70)
        print("📊 第三阶段D-抗硫因子特殊处理摘要")
        print("="*70)
        
        if filter_info:
            print(f"\n{filter_info['stage_name']} ({filter_info['stage']}):")
            print(f"  描述: {filter_info['description']}")
            print(f"  处理策略: {filter_info['strategy_info']['strategy']} - {filter_info['strategy_info']['description']}")
            print(f"  原始样本: {filter_info['original_samples']}")
            print(f"  处理后样本: {filter_info['filtered_samples']}")
            print(f"  保留率: {filter_info['retention_rate']*100:.1f}%")
            print(f"  输出文件: {filter_info['output_file']}")
            print(f"  目标列数: {len(filter_info['target_columns'])}")
            
            print(f"\n📊 数据可用性详情:")
            for target, info in filter_info['data_analysis'].items():
                print(f"  {target}:")
                print(f"    有效数据: {info['valid_samples']}个 ({info['valid_rate']:.1f}%)")
        else:
            print("\n⚠️ 未能生成处理结果")
        
        print(f"\n📋 特殊处理说明:")
        print(f"  • 问题: 每个目标列只有4个有效数据，有效率仅8%")
        print(f"  • 策略: 保留所有至少有一个目标列有效数据的行")
        print(f"  • 原因: 数据太少，不适合进行距离筛选")
        print(f"  • 建议: 需要补充更多第三阶段D的测试数据")

def main():
    """主函数"""
    print("🔬 启动第三阶段D-抗硫因子特殊处理器...")
    
    # 创建处理器
    handler = Stage3DSpecialHandler()
    
    # 运行特殊处理
    handler.run_special_processing()
    
    print("🎉 第三阶段D特殊处理完成!")

if __name__ == '__main__':
    main()
