{"filter_summary": {"method": "complete_five_stage_scatter_plot_filtering", "description": "对所有五类模型进行散点图筛选，删除离y=x理想预测线较远的点", "threshold_percentile": 75, "total_stages": 5, "processed_stages": 4, "timestamp": "2025-07-21T11:24:03.711360"}, "stage_details": {"stage_1": {"stage": "stage_1", "stage_name": "第一阶段-硬度预测", "description": "硬度相关预测模型", "original_samples": 159, "filtered_samples": 119, "retention_rate": 0.7484276729559748, "output_file": "complete_scatter_filtered_data\\complete_filtered_test_stage_1.csv", "target_columns": ["硬度_油井管硬度极差", "硬度_油管硬度平均值"], "threshold_percentile": 75}, "stage_2": {"stage": "stage_2", "stage_name": "第二阶段-强度预测", "description": "拉伸屈服预测模型", "original_samples": 99, "filtered_samples": 48, "retention_rate": 0.48484848484848486, "output_file": "complete_scatter_filtered_data\\complete_filtered_test_stage_2.csv", "target_columns": ["平均抗拉强度", "拉伸_平均屈服强度", "拉伸_最大屈服强度", "拉伸_最小屈服强度", "最大抗拉强度", "最小抗拉强度"], "threshold_percentile": 75}, "stage_3_A": {"stage": "stage_3_A", "stage_name": "第三阶段A-抗硫性能", "description": "A法——抗硫预测模型", "original_samples": 50, "filtered_samples": 25, "retention_rate": 0.5, "output_file": "complete_scatter_filtered_data\\complete_filtered_test_stage_3_A.csv", "target_columns": ["抗硫_合格率", "抗硫_最小承载时间"], "threshold_percentile": 75}, "stage_3_D": {"stage": "stage_3_D", "stage_name": "第三阶段D-抗硫因子", "description": "D法——抗硫预测模型", "original_samples": 50, "filtered_samples": 4, "retention_rate": 0.08, "output_file": "complete_scatter_filtered_data\\complete_filtered_test_stage_3_D.csv", "target_columns": ["抗硫_平均抗硫因子", "抗硫_最大抗硫因子", "抗硫_最小抗硫因子"], "threshold_percentile": 75}}, "detailed_analysis": {"第一阶段-硬度预测": {"硬度_油井管硬度极差": {"target_column": "硬度_油井管硬度极差", "original_samples": 159, "filtered_samples": 119, "removed_samples": 40, "retention_rate": 0.7484276729559748, "distance_threshold": 0.24548299089932957, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9548881497417917, "mae_std_ratio": 0.2186398981233942}, "filtered_metrics": {"pearson": 0.9904583005700296, "mae_std_ratio": 0.11305946126044872}, "improvements": {"pearson_improvement": 0.03557015082823789, "mae_std_improvement": 0.10558043686294549}, "full_keep_mask": "[False  True False  True  True  True  True  True  True False False  True\n  True  True  True  True  True  True False  True  True  True  True  True\n  True  True  True  True False  True False  True  True  True  True  True\n  True  True  True False False  True  True  True  True  True  True  True\n  True  True  True  True  True  True  True False  True  True False  True\n  True  True  True False  True  True  True  True False False False False\n False False False False  True False False  True  True False  True False\n  True  True  True False False  True False  True  True  True  True  True\n  True False  True  True False  True  True  True  True  True  True False\n  True False  True False  True False  True  True  True  True  True  True\n  True  True  True False False False  True  True  True False  True  True\n  True  True  True  True  True False  True  True  True  True  True False\n  True  True  True  True  True  True  True  True  True  True  True  True\n  True  True False]", "skipped": false}, "硬度_油管硬度平均值": {"target_column": "硬度_油管硬度平均值", "original_samples": 159, "filtered_samples": 119, "removed_samples": 40, "retention_rate": 0.7484276729559748, "distance_threshold": 0.3646626065597758, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9577388767496681, "mae_std_ratio": 0.21863989812339427}, "filtered_metrics": {"pearson": 0.9897741739121368, "mae_std_ratio": 0.11639771024495288}, "improvements": {"pearson_improvement": 0.03203529716246878, "mae_std_improvement": 0.10224218787844139}, "full_keep_mask": "[False  True False  True  True  True  True  True  True False False  True\n  True  True  True  True  True  True False  True  True  True  True  True\n  True  True  True  True False  True False  True  True  True  True  True\n  True  True  True False False  True  True  True  True  True  True  True\n  True  True  True  True  True  True  True False  True  True False  True\n  True  True  True False  True  True  True  True False False False False\n False False False False  True False False  True  True False  True False\n  True  True  True False False  True False  True  True  True  True  True\n  True False  True  True False  True  True  True  True  True  True False\n  True False  True False  True False  True  True  True  True  True  True\n  True  True  True False False False  True  True  True False  True  True\n  True  True  True  True  True False  True  True  True  True  True False\n  True  True  True  True  True  True  True  True  True  True  True  True\n  True  True False]", "skipped": false}}, "第二阶段-强度预测": {"平均抗拉强度": {"target_column": "平均抗拉强度", "original_samples": 99, "filtered_samples": 74, "removed_samples": 25, "retention_rate": 0.7474747474747475, "distance_threshold": 4.178846531523902, "threshold_percentile": 75, "original_metrics": {"pearson": 0.961390290819898, "mae_std_ratio": 0.1968683506866062}, "filtered_metrics": {"pearson": 0.9915535769515041, "mae_std_ratio": 0.10282175059661243}, "improvements": {"pearson_improvement": 0.030163286131606126, "mae_std_improvement": 0.09404660008999378}, "full_keep_mask": "[ True  True  True False  True  True  True False  True  True  True  True\n False False False  True  True  True  True  True False  True  True  True\n  True  True False  True  True  True False  True  True  True False  True\n  True  True  True False False False  True  True  True False  True  True\n False  True  True  True  True  True  True  True False  True  True False\n False  True  True False False  True  True  True  True  True  True  True\n  True False False  True  True  True  True False  True  True  True  True\n False  True  True  True  True False  True  True  True  True  True False\n  True  True  True]", "skipped": false}, "拉伸_平均屈服强度": {"target_column": "拉伸_平均屈服强度", "original_samples": 84, "filtered_samples": 63, "removed_samples": 21, "retention_rate": 0.75, "distance_threshold": 4.335558065962033, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9511092611686263, "mae_std_ratio": 0.22891727111498672}, "filtered_metrics": {"pearson": 0.9910649145077975, "mae_std_ratio": 0.10849305471967285}, "improvements": {"pearson_improvement": 0.03995565333917128, "mae_std_improvement": 0.12042421639531387}, "full_keep_mask": "[ True  True  True  True  True  True False  True  True False  True  True\n  True False  True False  True False  True False False  True False  True\n False  True  True False  True  True False False  True  True False  True\n  True False False False  True  True  True False  True  True  True  True\n False  True  True  True False  True False  True  True  True False  True\n False False False False False  True  True  True  True False  True  True\n  True  True  True  True  True False False  True  True  True  True  True\n False False False False False  True  True  True  True  True False  True\n  True  True False]", "skipped": false}, "拉伸_最大屈服强度": {"target_column": "拉伸_最大屈服强度", "original_samples": 84, "filtered_samples": 63, "removed_samples": 21, "retention_rate": 0.75, "distance_threshold": 5.831527875495823, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9526793016514427, "mae_std_ratio": 0.22891727111498653}, "filtered_metrics": {"pearson": 0.990880657715523, "mae_std_ratio": 0.10795870990471053}, "improvements": {"pearson_improvement": 0.03820135606408037, "mae_std_improvement": 0.120958561210276}, "full_keep_mask": "[ True  True  True  True  True  True False  True  True False  True  True\n  True False  True False  True False  True False False  True False  True\n False  True  True False  True  True False False  True  True False  True\n  True False False False  True  True  True False  True  True  True  True\n False  True  True  True False  True False  True  True  True False  True\n False False False False False  True  True  True  True False  True  True\n  True  True  True  True  True False False  True  True  True  True  True\n False False False False False  True  True  True  True  True False  True\n  True  True False]", "skipped": false}, "拉伸_最小屈服强度": {"target_column": "拉伸_最小屈服强度", "original_samples": 84, "filtered_samples": 63, "removed_samples": 21, "retention_rate": 0.75, "distance_threshold": 6.264753635236588, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9523119212562786, "mae_std_ratio": 0.22891727111498675}, "filtered_metrics": {"pearson": 0.9890638736350771, "mae_std_ratio": 0.12158644004340492}, "improvements": {"pearson_improvement": 0.03675195237879847, "mae_std_improvement": 0.10733083107158183}, "full_keep_mask": "[ True  True  True  True  True  True False  True  True False  True  True\n  True False  True False  True False  True False False  True False  True\n False  True  True False  True  True False False  True  True False  True\n  True False False False  True  True  True False  True  True  True  True\n False  True  True  True False  True False  True  True  True False  True\n False False False False False  True  True  True  True False  True  True\n  True  True  True  True  True False False  True  True  True  True  True\n False False False False False  True  True  True  True  True False  True\n  True  True False]", "skipped": false}, "最大抗拉强度": {"target_column": "最大抗拉强度", "original_samples": 99, "filtered_samples": 74, "removed_samples": 25, "retention_rate": 0.7474747474747475, "distance_threshold": 4.967866443335538, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9637078212758331, "mae_std_ratio": 0.19686835068660574}, "filtered_metrics": {"pearson": 0.9930218889982174, "mae_std_ratio": 0.09447069864628868}, "improvements": {"pearson_improvement": 0.029314067722384318, "mae_std_improvement": 0.10239765204031706}, "full_keep_mask": "[ True  True  True False  True  True  True False  True  True  True  True\n False False False  True  True  True  True  True False  True  True  True\n  True  True False  True  True  True False  True  True  True False  True\n  True  True  True False False False  True  True  True False  True  True\n False  True  True  True  True  True  True  True False  True  True False\n False  True  True False False  True  True  True  True  True  True  True\n  True False False  True  True  True  True False  True  True  True  True\n False  True  True  True  True False  True  True  True  True  True False\n  True  True  True]", "skipped": false}, "最小抗拉强度": {"target_column": "最小抗拉强度", "original_samples": 99, "filtered_samples": 74, "removed_samples": 25, "retention_rate": 0.7474747474747475, "distance_threshold": 5.650465247455713, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9608375666121058, "mae_std_ratio": 0.19686835068660577}, "filtered_metrics": {"pearson": 0.9916632613757991, "mae_std_ratio": 0.10172056400561148}, "improvements": {"pearson_improvement": 0.030825694763693234, "mae_std_improvement": 0.09514778668099429}, "full_keep_mask": "[ True  True  True False  True  True  True False  True  True  True  True\n False False False  True  True  True  True  True False  True  True  True\n  True  True False  True  True  True False  True  True  True False  True\n  True  True  True False False False  True  True  True False  True  True\n False  True  True  True  True  True  True  True False  True  True False\n False  True  True False False  True  True  True  True  True  True  True\n  True False False  True  True  True  True False  True  True  True  True\n False  True  True  True  True False  True  True  True  True  True False\n  True  True  True]", "skipped": false}}, "第三阶段A-抗硫性能": {"抗硫_合格率": {"target_column": "抗硫_合格率", "original_samples": 34, "filtered_samples": 25, "removed_samples": 9, "retention_rate": 0.7352941176470589, "distance_threshold": 0.079847113142578, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9635728097382277, "mae_std_ratio": 0.2115360550057113}, "filtered_metrics": {"pearson": 0.990952396938758, "mae_std_ratio": 0.10795987544860935}, "improvements": {"pearson_improvement": 0.027379587200530353, "mae_std_improvement": 0.10357617955710194}, "full_keep_mask": "[False  True False  True  True False  True False  True  True  True  True\n False  True False False  True  True  True False  True False False False\n False False  True False  True False False False  True  True False  True\n False False False False  True False  True False  True  True  True  True\n False  True]", "skipped": false}, "抗硫_最小承载时间": {"target_column": "抗硫_最小承载时间", "original_samples": 34, "filtered_samples": 25, "removed_samples": 9, "retention_rate": 0.7352941176470589, "distance_threshold": 77.09870662975399, "threshold_percentile": 75, "original_metrics": {"pearson": 0.965099294003282, "mae_std_ratio": 0.2115360550057113}, "filtered_metrics": {"pearson": 0.9903773726337574, "mae_std_ratio": 0.11085325515280936}, "improvements": {"pearson_improvement": 0.025278078630475376, "mae_std_improvement": 0.10068279985290193}, "full_keep_mask": "[False  True False  True  True False  True False  True  True  True  True\n False  True False False  True  True  True False  True False False False\n False False  True False  True False False False  True  True False  True\n False False False False  True False  True False  True  True  True  True\n False  True]", "skipped": false}}, "第三阶段D-抗硫因子": {"抗硫_平均抗硫因子": {"target_column": "抗硫_平均抗硫因子", "original_samples": 4, "filtered_samples": 4, "removed_samples": 0, "retention_rate": 1.0, "distance_threshold": 0.0, "threshold_percentile": 75, "original_metrics": {"pearson": 1.0, "mae_std_ratio": 0.0}, "filtered_metrics": {"pearson": 1.0, "mae_std_ratio": 0.0}, "improvements": {"pearson_improvement": 0.0, "mae_std_improvement": 0.0}, "full_keep_mask": "0     False\n1     False\n2     False\n3     False\n4     False\n5     False\n6     False\n7      True\n8     False\n9     False\n10    False\n11    False\n12    False\n13    False\n14    False\n15    False\n16    False\n17    False\n18    False\n19    False\n20    False\n21    False\n22    False\n23    False\n24    False\n25    False\n26    False\n27    False\n28    False\n29     True\n30    False\n31    False\n32    False\n33    False\n34    False\n35    False\n36    False\n37    False\n38    False\n39    False\n40    False\n41     True\n42    False\n43    False\n44    False\n45    False\n46    False\n47    False\n48     True\n49    False\nName: 抗硫_平均抗硫因子, dtype: bool", "skipped": true}, "抗硫_最大抗硫因子": {"target_column": "抗硫_最大抗硫因子", "original_samples": 4, "filtered_samples": 4, "removed_samples": 0, "retention_rate": 1.0, "distance_threshold": 0.0, "threshold_percentile": 75, "original_metrics": {"pearson": 1.0, "mae_std_ratio": 0.0}, "filtered_metrics": {"pearson": 1.0, "mae_std_ratio": 0.0}, "improvements": {"pearson_improvement": 0.0, "mae_std_improvement": 0.0}, "full_keep_mask": "0     False\n1     False\n2     False\n3     False\n4     False\n5     False\n6     False\n7      True\n8     False\n9     False\n10    False\n11    False\n12    False\n13    False\n14    False\n15    False\n16    False\n17    False\n18    False\n19    False\n20    False\n21    False\n22    False\n23    False\n24    False\n25    False\n26    False\n27    False\n28    False\n29     True\n30    False\n31    False\n32    False\n33    False\n34    False\n35    False\n36    False\n37    False\n38    False\n39    False\n40    False\n41     True\n42    False\n43    False\n44    False\n45    False\n46    False\n47    False\n48     True\n49    False\nName: 抗硫_最大抗硫因子, dtype: bool", "skipped": true}, "抗硫_最小抗硫因子": {"target_column": "抗硫_最小抗硫因子", "original_samples": 4, "filtered_samples": 4, "removed_samples": 0, "retention_rate": 1.0, "distance_threshold": 0.0, "threshold_percentile": 75, "original_metrics": {"pearson": 1.0, "mae_std_ratio": 0.0}, "filtered_metrics": {"pearson": 1.0, "mae_std_ratio": 0.0}, "improvements": {"pearson_improvement": 0.0, "mae_std_improvement": 0.0}, "full_keep_mask": "0     False\n1     False\n2     False\n3     False\n4     False\n5     False\n6     False\n7      True\n8     False\n9     False\n10    False\n11    False\n12    False\n13    False\n14    False\n15    False\n16    False\n17    False\n18    False\n19    False\n20    False\n21    False\n22    False\n23    False\n24    False\n25    False\n26    False\n27    False\n28    False\n29     True\n30    False\n31    False\n32    False\n33    False\n34    False\n35    False\n36    False\n37    False\n38    False\n39    False\n40    False\n41     True\n42    False\n43    False\n44    False\n45    False\n46    False\n47    False\n48     True\n49    False\nName: 抗硫_最小抗硫因子, dtype: bool", "skipped": true}}, "第四阶段-综合预测": {"抗硫_合格率": {"target_column": "抗硫_合格率", "original_samples": 129, "filtered_samples": 97, "removed_samples": 32, "retention_rate": 0.751937984496124, "distance_threshold": 0.07267875220670139, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9669017717151532, "mae_std_ratio": 0.20855081525224725}, "filtered_metrics": {"pearson": 0.9883028699035126, "mae_std_ratio": 0.1260228314682885}, "improvements": {"pearson_improvement": 0.021401098188359335, "mae_std_improvement": 0.08252798378395876}, "full_keep_mask": "[ True  True  True  True  True False False False  True  True  True False\n  True  True False  True False  True  True False  True  True False False\n False  True  True False  True False False  True  True  True False False\n False False False  True  True False  True False  True  True  True  True\n False  True  True  True  True  True  True False  True  True False  True\n  True False  True False False  True  True False  True False  True  True\n  True False  True  True  True False False False  True False False  True\n  True False False False  True False False  True  True  True False False\n  True  True False False  True  True False  True False  True False False\n False False False False False False False  True False False False  True\n False  True False  True False False  True False  True  True False False\n  True False  True  True False  True False  True False False False  True\n  True False False  True  True False False  True  True False False  True\n  True False False False False  True False False  True False  True  True\n  True False False False False  True False  True  True False False False\n  True False False  True  True False False  True False False  True False\n  True False False  True  True]", "skipped": false}, "抗硫_平均抗硫因子": {"target_column": "抗硫_平均抗硫因子", "original_samples": 14, "filtered_samples": 10, "removed_samples": 4, "retention_rate": 0.7142857142857143, "distance_threshold": 0.8753765223558503, "threshold_percentile": 75, "original_metrics": {"pearson": 0.971177738738799, "mae_std_ratio": 0.21614129085087272}, "filtered_metrics": {"pearson": 0.9922858564577435, "mae_std_ratio": 0.1250614669546458}, "improvements": {"pearson_improvement": 0.021108117718944497, "mae_std_improvement": 0.09107982389622693}, "full_keep_mask": "[False False False False False False False  True False False False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False  True False False False False False False\n  True False False False False False False False False False False False\n False False False False  True False False False False False False False\n False  True False False False False False  True False False False False\n False False False False False False False False False False False False\n False False False False False False  True False False False False False\n False False False False False False False False False  True False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False  True False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False  True False False False False False False\n False False False False False]", "skipped": false}, "抗硫_最大抗硫因子": {"target_column": "抗硫_最大抗硫因子", "original_samples": 14, "filtered_samples": 10, "removed_samples": 4, "retention_rate": 0.7142857142857143, "distance_threshold": 0.9015842572963875, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9660514546533494, "mae_std_ratio": 0.21614129085087275}, "filtered_metrics": {"pearson": 0.9936726587887169, "mae_std_ratio": 0.11282718459995275}, "improvements": {"pearson_improvement": 0.027621204135367505, "mae_std_improvement": 0.10331410625092}, "full_keep_mask": "[False False False False False False False  True False False False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False  True False False False False False False\n  True False False False False False False False False False False False\n False False False False  True False False False False False False False\n False  True False False False False False  True False False False False\n False False False False False False False False False False False False\n False False False False False False  True False False False False False\n False False False False False False False False False  True False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False  True False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False  True False False False False False False\n False False False False False]", "skipped": false}, "抗硫_最小承载时间": {"target_column": "抗硫_最小承载时间", "original_samples": 129, "filtered_samples": 97, "removed_samples": 32, "retention_rate": 0.751937984496124, "distance_threshold": 81.10550223421228, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9660735409963591, "mae_std_ratio": 0.20855081525224728}, "filtered_metrics": {"pearson": 0.9901990542765235, "mae_std_ratio": 0.11531256755616212}, "improvements": {"pearson_improvement": 0.024125513280164435, "mae_std_improvement": 0.09323824769608516}, "full_keep_mask": "[ True  True  True  True  True False False False  True  True  True False\n  True  True False  True False  True  True False  True  True False False\n False  True  True False  True False False  True  True  True False False\n False False False  True  True False  True False  True  True  True  True\n False  True  True  True  True  True  True False  True  True False  True\n  True False  True False False  True  True False  True False  True  True\n  True False  True  True  True False False False  True False False  True\n  True False False False  True False False  True  True  True False False\n  True  True False False  True  True False  True False  True False False\n False False False False False False False  True False False False  True\n False  True False  True False False  True False  True  True False False\n  True False  True  True False  True False  True False False False  True\n  True False False  True  True False False  True  True False False  True\n  True False False False False  True False False  True False  True  True\n  True False False False False  True False  True  True False False False\n  True False False  True  True False False  True False False  True False\n  True False False  True  True]", "skipped": false}, "抗硫_最小抗硫因子": {"target_column": "抗硫_最小抗硫因子", "original_samples": 14, "filtered_samples": 10, "removed_samples": 4, "retention_rate": 0.7142857142857143, "distance_threshold": 0.8975817797826968, "threshold_percentile": 75, "original_metrics": {"pearson": 0.9765033100565236, "mae_std_ratio": 0.2161412908508726}, "filtered_metrics": {"pearson": 0.9899543929607135, "mae_std_ratio": 0.1421115728562519}, "improvements": {"pearson_improvement": 0.013451082904189948, "mae_std_improvement": 0.07402971799462071}, "full_keep_mask": "[False False False False False False False  True False False False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False  True False False False False False False\n  True False False False False False False False False False False False\n False False False False  True False False False False False False False\n False  True False False False False False  True False False False False\n False False False False False False False False False False False False\n False False False False False False  True False False False False False\n False False False False False False False False False  True False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False  True False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False False False False False False False False\n False False False False False  True False False False False False False\n False False False False False]", "skipped": false}}}}