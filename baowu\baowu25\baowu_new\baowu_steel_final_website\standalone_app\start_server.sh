#!/bin/bash

echo "========================================"
echo "🔬 耐应力腐蚀油套管智能预测系统"
echo "📊 服务器端启动脚本"
echo "🏭 宝武钢铁集团"
echo "========================================"
echo

echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3环境"
    echo
    echo "💡 请安装Python 3.8或更高版本"
    exit 1
fi

echo "✅ Python环境检查通过"
echo "🔍 Python版本: $(python3 --version)"

echo
echo "🔍 检查依赖包..."
python3 -c "import flask, pandas, numpy" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "❌ 缺少基础依赖包"
    echo "正在安装依赖包..."
    pip3 install flask flask-cors pandas numpy
fi

echo
echo "🔍 检查AutoGluon..."
python3 -c "from autogluon.tabular import TabularPredictor" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "⚠️ 未找到AutoGluon"
    echo
    read -p "是否安装AutoGluon？(y/N): " install_ag
    if [[ $install_ag =~ ^[Yy]$ ]]; then
        echo "正在安装AutoGluon..."
        pip3 install autogluon
    else
        echo "将使用演示模式运行"
    fi
fi

echo
echo "🚀 启动服务器..."
echo "📡 服务器将在5000端口启动"
echo "🔧 按 Ctrl+C 停止服务"
echo

python3 server_backend.py
