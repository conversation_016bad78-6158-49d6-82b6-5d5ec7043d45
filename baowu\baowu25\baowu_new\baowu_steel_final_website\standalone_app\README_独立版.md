# 🔬 耐应力腐蚀油套管智能预测系统 - 独立版

## 📋 项目简介
这是一个完全独立运行的版本，无需安装Python环境或任何依赖包，可以直接在Windows系统上运行。

## ✨ 独立版特点
- 🚀 **零依赖**：无需安装Python或任何第三方库
- 📦 **单文件**：所有功能打包在一个可执行文件中
- 🌐 **内置服务器**：集成HTTP服务器，自动打开浏览器
- 💾 **便携式**：可以放在U盘中随时使用
- 🔒 **安全可靠**：不会修改系统文件或注册表

## 🚀 快速开始

### 方法1：使用预构建的可执行文件（推荐）
1. 下载发布包中的 `dist` 文件夹
2. 双击运行 `耐应力腐蚀油套管智能预测系统.exe`
3. 系统会自动打开浏览器访问应用
4. 开始使用系统进行预测分析

### 方法2：从源码构建
如果您需要自定义或重新构建：

```bash
# 1. 进入独立版目录
cd standalone_app

# 2. 运行构建脚本
build_executable.bat

# 3. 等待构建完成
# 构建成功后会在 dist 目录生成可执行文件
```

## 📁 文件结构
```
standalone_app/
├── standalone_app.py          # 主应用程序
├── build_spec.py             # PyInstaller构建脚本
├── build_executable.bat      # Windows构建脚本
├── start_standalone.bat      # 开发环境启动脚本
├── web/                      # Web资源文件
│   ├── index.html           # 主页面
│   ├── static/              # 静态资源
│   │   ├── css/            # 样式文件
│   │   └── js/             # JavaScript文件
│   └── test_data/          # 测试数据
└── dist/                    # 构建输出目录（构建后生成）
    └── 耐应力腐蚀油套管智能预测系统.exe
```

## 🔧 技术架构

### 核心组件
- **HTTP服务器**：基于Python内置的`http.server`
- **API处理器**：自定义请求处理器，支持RESTful API
- **演示模式**：内置模拟预测算法，无需真实模型文件
- **文件服务**：支持静态文件和测试数据下载

### 支持的功能
- ✅ 模型选择和信息展示
- ✅ 数据文件上传和验证
- ✅ 演示预测功能
- ✅ 测试数据下载
- ✅ 操作引导系统
- ✅ 结果可视化展示
- ✅ 响应式界面设计

## 🎯 使用指南

### 1. 启动系统
- 双击 `耐应力腐蚀油套管智能预测系统.exe`
- 等待控制台显示启动信息
- 浏览器会自动打开并访问系统

### 2. 系统操作
1. **选择模型**：从5个预测模型中选择合适的模型
2. **准备数据**：上传CSV/Excel文件或使用测试数据
3. **数据验证**：系统自动验证数据格式和兼容性
4. **执行预测**：运行预测算法获得结果
5. **导出结果**：下载预测结果和分析报告

### 3. 测试数据
系统内置了各阶段的测试数据：
- `test_stage_1.csv` - 硬度预测测试数据
- `test_stage_2.csv` - 拉伸强度测试数据
- `test_stage_3_A.csv` - A法抗硫测试数据
- `test_stage_3_D.csv` - D法抗硫测试数据
- `test_stage_4.csv` - 综合质量测试数据

## ⚙️ 配置说明

### 默认配置
- **服务器地址**：localhost:8080
- **演示模式**：启用（生成模拟预测结果）
- **自动打开浏览器**：启用
- **文件大小限制**：50MB

### 修改配置
如需修改配置，请编辑 `standalone_app.py` 中的 `CONFIG` 字典：

```python
CONFIG = {
    'HOST': 'localhost',    # 服务器地址
    'PORT': 8080,          # 服务器端口
    'APP_NAME': '耐应力腐蚀油套管智能预测系统',
    'VERSION': '2.0 独立版'
}
```

## 🛠️ 故障排除

### 常见问题

1. **无法启动**
   - 检查是否有杀毒软件阻止
   - 确保端口8080未被占用
   - 以管理员身份运行

2. **浏览器未自动打开**
   - 手动访问 http://localhost:8080
   - 检查防火墙设置
   - 尝试使用其他浏览器

3. **功能异常**
   - 检查控制台错误信息
   - 重启应用程序
   - 清除浏览器缓存

4. **文件上传失败**
   - 检查文件格式（支持CSV、Excel）
   - 确保文件大小不超过50MB
   - 检查文件编码（推荐UTF-8）

### 日志信息
应用运行时会在控制台显示详细的日志信息：
- 🚀 启动信息
- 🌐 服务器状态
- 📡 API请求日志
- ❌ 错误信息

## 📞 技术支持

### 系统要求
- **操作系统**：Windows 7/8/10/11
- **内存**：至少512MB可用内存
- **磁盘空间**：至少100MB可用空间
- **网络**：无需网络连接（本地运行）

### 兼容性
- ✅ Windows 10/11（推荐）
- ✅ Windows 8/8.1
- ✅ Windows 7 SP1
- ⚠️ Windows XP（可能需要额外配置）

### 浏览器支持
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Edge 80+
- ✅ Safari 13+
- ⚠️ IE 11（功能受限）

## 📝 更新日志

### v2.0 独立版
- ✅ 完全独立运行，无需Python环境
- ✅ 内置HTTP服务器和API处理
- ✅ 演示模式支持，无需真实模型
- ✅ 一键启动，自动打开浏览器
- ✅ 完整的用户界面和引导系统
- ✅ 测试数据内置，即开即用

## 📄 许可证
© 2024 宝武钢铁集团 - 耐应力腐蚀油套管智能预测系统

---

**注意**：此独立版本为演示版本，使用模拟算法生成预测结果。如需使用真实的机器学习模型，请联系技术支持团队获取完整版本。
