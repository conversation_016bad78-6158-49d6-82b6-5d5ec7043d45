#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自动化客户端测试
自动寻找可用端口并连接到本地服务器
"""

import os
import sys
import time
import requests
import webbrowser
import threading
import subprocess
from pathlib import Path

def test_server_connection(server_url):
    """测试服务器连接"""
    try:
        response = requests.get(f"{server_url}/api/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 服务器连接成功: {data.get('message', '未知状态')}")
            print(f"📊 服务器版本: {data.get('version', '未知版本')}")
            print(f"🤖 AutoGluon状态: {'可用' if data.get('autogluon_available') else '不可用'}")
            return True
        else:
            print(f"❌ 服务器响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

def start_auto_client():
    """启动自动化客户端"""
    print("🔬 耐应力腐蚀油套管智能预测系统")
    print("🧪 自动化客户端测试")
    print("=" * 50)
    
    # 默认服务器地址
    server_url = "http://127.0.0.1:5000"
    
    print(f"🔍 测试服务器连接: {server_url}")
    
    # 测试服务器连接
    if not test_server_connection(server_url):
        print("\n❌ 无法连接到服务器")
        print("💡 请确保服务器端已启动:")
        print("   python server_backend.py")
        return False
    
    print("\n✅ 服务器连接正常，启动客户端...")
    
    # 设置环境变量，避免交互式输入
    os.environ['SERVER_HOST'] = '127.0.0.1'
    os.environ['SERVER_PORT'] = '5000'
    
    # 导入并启动客户端
    try:
        import client_frontend
        
        # 直接设置配置，避免用户输入
        client_frontend.CONFIG['SERVER_HOST'] = '127.0.0.1'
        client_frontend.CONFIG['SERVER_PORT'] = 5000
        client_frontend.CONFIG['SERVER_URL'] = server_url
        
        print("🚀 启动客户端...")
        client_frontend.start_client()
        
    except KeyboardInterrupt:
        print("\n👋 客户端已停止")
    except Exception as e:
        print(f"❌ 客户端启动失败: {e}")
        return False
    
    return True

if __name__ == '__main__':
    start_auto_client()
