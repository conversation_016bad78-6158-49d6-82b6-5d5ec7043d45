@echo off
chcp 65001 >nul
title 一键构建完整版发布包

echo.
echo ========================================
echo 📦 耐应力腐蚀油套管智能预测系统
echo 🏗️ 完整版发布包构建工具
echo ========================================
echo.

echo 📋 完整版特点：
echo ✅ 包含真实的AutoGluon机器学习模型
echo ✅ 包含完整的前端资源（CSS、JS）
echo ✅ 修复了前端渲染问题
echo ✅ 支持真实的预测功能
echo ✅ 无需Python环境即可运行
echo.

echo 🔍 检查Python环境...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 🔍 检查依赖包...
python -c "import pandas, numpy" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 缺少基础依赖包
    echo 正在安装基础依赖...
    pip install pandas numpy
)

echo.
echo 🔍 检查AutoGluon...
python -c "from autogluon.tabular import TabularPredictor" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ 未找到AutoGluon
    echo.
    set /p install_ag="是否安装AutoGluon？这将需要较长时间 (Y/N): "
    if /i "%install_ag%"=="Y" (
        echo 正在安装AutoGluon...
        pip install autogluon
    ) else (
        echo 将构建演示版本（不包含真实模型）
    )
)

echo.
echo 🔍 检查PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
)

echo.
echo 🚀 开始构建完整版发布包...
python build_complete_distribution.py

echo.
echo 📁 检查构建结果...
if exist "complete_distribution" (
    echo ✅ 完整版发布包构建成功！
    echo 📂 发布目录: complete_distribution\
    echo.
    
    if exist "complete_distribution\完整版可执行文件" (
        echo ✅ 完整版可执行文件: 已创建
        echo 📏 文件大小: 约500MB-1GB
        echo 🎯 特点: 包含真实AutoGluon模型
    ) else (
        echo ⚠️ 完整版可执行文件: 构建失败
    )
    
    if exist "complete_distribution\完整版便携版" (
        echo ✅ 完整版便携版: 已创建
        echo 📏 文件大小: 约200-500MB
        echo 🎯 特点: 包含模型文件，需要Python环境
    )
    
    if exist "耐应力腐蚀油套管智能预测系统_完整版发布包.zip" (
        echo ✅ 完整版ZIP压缩包: 已创建
    )
    
    echo.
    echo 🎉 构建完成！
    echo.
    echo 📋 使用说明：
    echo 1. 将发布包复制到目标计算机
    echo 2. 解压ZIP文件
    echo 3. 根据环境选择合适的版本：
    echo    - 完整版可执行文件：无需Python环境
    echo    - 完整版便携版：需要Python + AutoGluon
    echo 4. 双击运行对应的启动文件
    echo.
    echo 🔧 技术特色：
    echo ✅ 真实的机器学习预测
    echo ✅ 完整的前端界面
    echo ✅ 专业的数据验证
    echo ✅ 准确的预测结果
    echo.
    
    set /p choice="是否打开发布目录？(Y/N): "
    if /i "%choice%"=="Y" (
        explorer complete_distribution
    )
    
) else (
    echo ❌ 构建失败，请检查错误信息
    echo.
    echo 💡 可能的解决方案：
    echo 1. 确保安装了所有依赖包
    echo 2. 检查模型文件是否存在
    echo 3. 确保有足够的磁盘空间
    echo 4. 以管理员身份运行
)

echo.
echo 📞 如需技术支持，请联系开发团队
echo © 2024 宝武钢铁集团

pause
