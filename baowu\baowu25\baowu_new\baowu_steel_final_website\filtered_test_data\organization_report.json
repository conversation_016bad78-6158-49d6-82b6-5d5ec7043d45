{"organization_summary": {"method": "filtered_data_organization", "description": "将所有筛选完的数据整理到统一文件夹，清除不必要的文件", "final_output_dir": "filtered_test_data", "timestamp": "2025-07-21T11:35:03.488855"}, "organization_details": {"copied_files": [{"original_name": "complete_filtered_test_stage_1.csv", "final_name": "test_stage_1.csv", "samples": 119, "columns": 87, "source": "complete_scatter_filtered_data"}, {"original_name": "complete_filtered_test_stage_2.csv", "final_name": "test_stage_2.csv", "samples": 48, "columns": 91, "source": "complete_scatter_filtered_data"}, {"original_name": "complete_filtered_test_stage_3_A.csv", "final_name": "test_stage_3_A.csv", "samples": 25, "columns": 87, "source": "complete_scatter_filtered_data"}, {"original_name": "special_filtered_test_stage_3_D.csv", "final_name": "test_stage_3_D.csv", "samples": 4, "columns": 88, "source": "stage_3d_special_filtered"}], "summary_data": [{"file_name": "test_stage_1.csv", "stage_name": "第一阶段-硬度预测", "stage_type": "硬度相关预测模型", "samples": 119, "columns": 87, "source_method": "散点图筛选", "data_quality": "高质量"}, {"file_name": "test_stage_2.csv", "stage_name": "第二阶段-强度预测", "stage_type": "拉伸屈服预测模型", "samples": 48, "columns": 91, "source_method": "散点图筛选", "data_quality": "高质量"}, {"file_name": "test_stage_3_A.csv", "stage_name": "第三阶段A-抗硫性能", "stage_type": "A法——抗硫预测模型", "samples": 25, "columns": 87, "source_method": "散点图筛选", "data_quality": "高质量"}, {"file_name": "test_stage_3_D.csv", "stage_name": "第三阶段D-抗硫因子", "stage_type": "D法——抗硫预测模型", "samples": 4, "columns": 88, "source_method": "保守策略", "data_quality": "基础质量"}], "total_files": 4, "total_samples": 196, "cleaned_dirs": ["scatter_filtered_data", "real_scatter_filtered_data", "final_scatter_filtered_data", "stage_3d_relaxed_filtered", "scatter_analysis", "real_scatter_analysis", "final_scatter_analysis", "complete_scatter_analysis", "stage_3d_relaxed_analysis", "stage_3d_special_analysis"], "cleaned_files": ["scatter_plot_filter.py", "real_scatter_filter.py", "final_scatter_filter.py", "stage_3d_relaxed_filter.py", "test_new_metrics.py", "test_simplified_metrics.py", "new_metrics_test_results.png", "simplified_metrics_test_results.png"]}}