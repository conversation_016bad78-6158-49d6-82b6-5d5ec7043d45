#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本
"""

import sys
import time
import threading
import subprocess

def test_server_startup():
    """测试服务器启动"""
    print("🔍 测试服务器启动...")
    
    try:
        # 导入服务器模块
        sys.path.append('.')
        import server_backend
        
        print("✅ 服务器模块导入成功")
        
        # 测试配置
        print(f"📊 服务器配置: {server_backend.CONFIG}")
        
        # 测试模型扫描
        models_root = server_backend.get_models_root()
        print(f"📁 模型目录: {models_root}")
        
        if models_root.exists():
            model_count = sum(1 for _ in models_root.rglob("*_model"))
            print(f"🤖 找到 {model_count} 个模型文件")
        else:
            print("⚠️ 模型目录不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_app():
    """测试完整版应用"""
    print("\n🔍 测试本地完整版...")
    
    try:
        import complete_standalone_app
        
        print("✅ 完整版模块导入成功")
        
        # 测试端口分配
        port = complete_standalone_app.find_available_port()
        print(f"🔌 可用端口: {port}")
        
        # 测试模型目录
        models_root = complete_standalone_app.get_models_root()
        print(f"📁 模型目录: {models_root}")
        
        if models_root.exists():
            model_count = sum(1 for _ in models_root.rglob("*_model"))
            print(f"🤖 找到 {model_count} 个模型文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整版测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_client_app():
    """测试客户端应用"""
    print("\n🔍 测试客户端...")
    
    try:
        import client_frontend
        
        print("✅ 客户端模块导入成功")
        
        # 测试端口分配
        port = client_frontend.find_available_port()
        print(f"🔌 可用端口: {port}")
        
        # 测试配置
        print(f"📊 客户端配置: {client_frontend.CONFIG}")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔬 耐应力腐蚀油套管智能预测系统")
    print("⚡ 快速测试程序")
    print("=" * 50)
    
    results = []
    
    # 测试服务器
    result1 = test_server_startup()
    results.append(("服务器端", result1))
    
    # 测试完整版
    result2 = test_complete_app()
    results.append(("本地完整版", result2))
    
    # 测试客户端
    result3 = test_client_app()
    results.append(("客户端", result3))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 测试结果")
    print("=" * 50)
    
    passed = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:<15} {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有组件测试通过！")
        return True
    else:
        print("⚠️ 部分组件测试失败")
        return False

if __name__ == '__main__':
    main()
