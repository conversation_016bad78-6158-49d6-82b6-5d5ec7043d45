#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建两套完整版本
1. 客户端-服务器分离版
2. 本地完整版
"""

import os
import sys
import shutil
import zipfile
import subprocess
from pathlib import Path
import tempfile

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'pandas', 
        'numpy',
        'flask',
        'flask-cors',
        'requests',
        'pyinstaller'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'flask-cors':
                from flask_cors import CORS
            elif package == 'pyinstaller':
                import PyInstaller
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        for pkg in missing_packages:
            print(f"pip install {pkg}")
        return False
    
    return True

def create_server_spec():
    """创建服务器端spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

datas = [
    ('../models', 'models'),
]

a = Analysis(
    ['server_backend.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'flask',
        'flask_cors',
        'autogluon.tabular',
        'autogluon.core',
        'autogluon.common',
        'autogluon.features',
        'pandas',
        'numpy',
        'sklearn',
        'lightgbm',
        'xgboost',
        'catboost'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='预测系统服务器端',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('server_build.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

def create_client_spec():
    """创建客户端spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

datas = [
    ('client_web', 'web'),
]

a = Analysis(
    ['client_frontend.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'requests',
        'http.server',
        'socketserver',
        'urllib.parse',
        'json',
        'threading',
        'webbrowser'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
        'autogluon',
        'sklearn',
        'lightgbm',
        'xgboost',
        'catboost'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='预测系统客户端',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('client_build.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

def create_local_complete_spec():
    """创建本地完整版spec文件"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

datas = [
    ('complete_web', 'web'),
    ('../models', 'models'),
]

a = Analysis(
    ['complete_standalone_app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'autogluon.tabular',
        'autogluon.core',
        'autogluon.common',
        'autogluon.features',
        'pandas',
        'numpy',
        'sklearn',
        'lightgbm',
        'xgboost',
        'catboost',
        'http.server',
        'socketserver',
        'urllib.parse',
        'json',
        'threading',
        'webbrowser'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='预测系统本地完整版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('local_complete_build.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)

def build_version(version_type):
    """构建指定版本"""
    print(f"🔨 构建{version_type}...")
    
    if version_type == "客户端-服务器分离版":
        # 准备客户端Web文件
        if not Path('client_web').exists():
            print("📁 准备客户端Web文件...")
            os.makedirs('client_web', exist_ok=True)
            # 复制Web资源
            source_dir = Path(__file__).parent.parent
            if (source_dir / 'backend' / 'static').exists():
                shutil.copytree(source_dir / 'backend' / 'static', 'client_web/static')
            if (source_dir / 'backend' / 'templates' / 'index.html').exists():
                shutil.copy2(source_dir / 'backend' / 'templates' / 'index.html', 'client_web/')
            if (source_dir / 'test_data').exists():
                shutil.copytree(source_dir / 'test_data', 'client_web/test_data')
        
        # 构建服务器端
        create_server_spec()
        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--clean", "--noconfirm", "server_build.spec"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 服务器端构建失败: {result.stderr}")
            return False
        
        # 构建客户端
        create_client_spec()
        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--clean", "--noconfirm", "client_build.spec"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 客户端构建失败: {result.stderr}")
            return False
        
        print("✅ 客户端-服务器分离版构建成功")
        return True
        
    elif version_type == "本地完整版":
        # 确保complete_web存在
        if not Path('complete_web').exists():
            print("❌ complete_web目录不存在，请先运行complete_standalone_app.py生成")
            return False
        
        # 构建本地完整版
        create_local_complete_spec()
        result = subprocess.run([sys.executable, "-m", "PyInstaller", "--clean", "--noconfirm", "local_complete_build.spec"], 
                              capture_output=True, text=True)
        if result.returncode != 0:
            print(f"❌ 本地完整版构建失败: {result.stderr}")
            return False
        
        print("✅ 本地完整版构建成功")
        return True
    
    return False

def create_distribution():
    """创建发布包"""
    print("📦 创建两套完整版发布包")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 设置路径
    current_dir = Path(__file__).parent
    dist_dir = current_dir / "two_versions_distribution"
    
    # 清理旧的发布目录
    if dist_dir.exists():
        print("🧹 清理旧的发布目录...")
        shutil.rmtree(dist_dir)
    
    # 创建发布目录结构
    print("📁 创建发布目录结构...")
    dist_dir.mkdir(exist_ok=True)
    
    # 构建客户端-服务器分离版
    print("\n🔨 构建客户端-服务器分离版...")
    if build_version("客户端-服务器分离版"):
        cs_dir = dist_dir / "客户端-服务器分离版"
        cs_dir.mkdir(exist_ok=True)
        
        # 服务器端
        server_dir = cs_dir / "服务器端(Linux)"
        server_dir.mkdir(exist_ok=True)
        if Path("dist/预测系统服务器端").exists():
            shutil.move("dist/预测系统服务器端", server_dir / "预测系统服务器端")
        
        # 客户端
        client_dir = cs_dir / "客户端(Windows)"
        client_dir.mkdir(exist_ok=True)
        if Path("dist/预测系统客户端.exe").exists():
            shutil.move("dist/预测系统客户端.exe", client_dir / "预测系统客户端.exe")
        
        # 创建说明文档
        create_cs_documentation(cs_dir)
    
    # 构建本地完整版
    print("\n🔨 构建本地完整版...")
    if build_version("本地完整版"):
        local_dir = dist_dir / "本地完整版(Windows)"
        local_dir.mkdir(exist_ok=True)
        if Path("dist/预测系统本地完整版.exe").exists():
            shutil.move("dist/预测系统本地完整版.exe", local_dir / "预测系统本地完整版.exe")
        
        # 创建说明文档
        create_local_documentation(local_dir)
    
    # 创建总体说明
    create_main_documentation(dist_dir)
    
    # 创建压缩包
    create_zip_package(dist_dir)
    
    # 清理临时文件
    cleanup_temp_files()
    
    print("\n✅ 两套完整版发布包创建完成！")
    print(f"📁 发布目录: {dist_dir}")
    
    return True

def create_cs_documentation(cs_dir):
    """创建客户端-服务器分离版文档"""
    readme_content = """# 客户端-服务器分离版使用说明

## 部署架构
- 服务器端：运行在Linux服务器上，提供API服务
- 客户端：运行在Windows上，提供Web界面

## 部署步骤

### 1. 服务器端部署（Linux）
1. 将"服务器端(Linux)"文件夹上传到Linux服务器
2. 给予执行权限：chmod +x 预测系统服务器端
3. 运行服务器：./预测系统服务器端
4. 服务器将在5000端口启动

### 2. 客户端部署（Windows）
1. 双击运行"预测系统客户端.exe"
2. 输入服务器IP地址和端口
3. 系统会自动测试连接并打开浏览器

## 优势
- 服务器集中管理模型和计算资源
- 客户端轻量化，启动快速
- 支持多客户端同时连接
- 便于维护和升级

## 注意事项
- 确保服务器5000端口开放
- 客户端需要网络连接到服务器
- 服务器需要安装AutoGluon等依赖
"""
    
    with open(cs_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)

def create_local_documentation(local_dir):
    """创建本地完整版文档"""
    readme_content = """# 本地完整版使用说明

## 特点
- 前后端完全集成在一个程序中
- 包含所有AutoGluon模型
- 无需网络连接，完全本地运行
- 双击即可启动

## 使用方法
1. 双击"预测系统本地完整版.exe"
2. 等待程序启动（首次启动可能需要30-60秒）
3. 浏览器会自动打开并显示系统界面
4. 开始使用预测功能

## 系统要求
- Windows 7/8/10/11
- 至少2GB可用内存
- 至少1GB可用磁盘空间

## 优势
- 无需任何配置
- 完全离线运行
- 数据安全性高
- 适合单机使用
"""
    
    with open(local_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)

def create_main_documentation(dist_dir):
    """创建主要文档"""
    readme_content = """# 耐应力腐蚀油套管智能预测系统 - 两套完整版

## 方案选择

### 客户端-服务器分离版
适用场景：
- 多用户环境
- 集中管理需求
- 服务器资源充足
- 需要远程访问

特点：
- 服务器端运行在Linux上
- 客户端运行在Windows上
- 支持多客户端同时连接
- 便于维护和升级

### 本地完整版
适用场景：
- 单用户环境
- 数据安全要求高
- 无网络连接需求
- 简单部署需求

特点：
- 完全本地运行
- 无需网络连接
- 双击即可使用
- 数据完全保密

## 技术支持
如需帮助，请联系技术支持团队

---
© 2024 宝武钢铁集团
"""
    
    with open(dist_dir / "README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)

def create_zip_package(dist_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_path = dist_dir.parent / "耐应力腐蚀油套管智能预测系统_两套完整版.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(dist_dir)
                zipf.write(file_path, arc_path)
    
    print(f"✅ ZIP包已创建: {zip_path}")

def cleanup_temp_files():
    """清理临时文件"""
    temp_files = [
        "server_build.spec",
        "client_build.spec", 
        "local_complete_build.spec"
    ]
    
    for temp_file in temp_files:
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    for temp_dir in ["build", "dist"]:
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)

if __name__ == '__main__':
    try:
        create_distribution()
        print("\n🎉 两套完整版发布包创建完成！")
        input("按回车键退出...")
    except Exception as e:
        print(f"\n❌ 创建发布包失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
