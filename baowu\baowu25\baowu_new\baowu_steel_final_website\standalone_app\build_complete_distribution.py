#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
构建完整的独立发布包
包含真实的AutoGluon模型和所有前端资源
"""

import os
import sys
import shutil
import zipfile
import subprocess
from pathlib import Path
import tempfile

def check_dependencies():
    """检查必要的依赖包"""
    required_packages = [
        'autogluon.tabular',
        'pandas', 
        'numpy',
        'pyinstaller'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'autogluon.tabular':
                from autogluon.tabular import TabularPredictor
            elif package == 'pyinstaller':
                import PyInstaller
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n缺少以下依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        if 'autogluon.tabular' in missing_packages:
            print("pip install autogluon")
        if 'pyinstaller' in missing_packages:
            print("pip install pyinstaller")
        for pkg in missing_packages:
            if pkg not in ['autogluon.tabular', 'pyinstaller']:
                print(f"pip install {pkg}")
        return False
    
    return True

def create_complete_spec_file():
    """创建完整的PyInstaller spec文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件列表
datas = [
    ('complete_web', 'web'),
    ('../models', 'models'),
]

a = Analysis(
    ['complete_standalone_app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'autogluon.tabular',
        'autogluon.core',
        'autogluon.common',
        'autogluon.features',
        'pandas',
        'numpy',
        'sklearn',
        'lightgbm',
        'xgboost',
        'catboost',
        'http.server',
        'socketserver',
        'urllib.parse',
        'json',
        'threading',
        'webbrowser',
        'tempfile',
        'shutil',
        'pathlib',
        'logging'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'scipy',
        'PIL',
        'cv2',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython',
        'pytest',
        'sphinx'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='耐应力腐蚀油套管智能预测系统_完整版',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    with open('complete_build.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ PyInstaller spec文件已创建")

def build_complete_executable():
    """构建完整的可执行文件"""
    print("🔨 开始构建完整版可执行文件...")
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 确保Web文件存在
    if not Path('complete_web').exists():
        print("❌ complete_web目录不存在，请先运行程序生成Web文件")
        return False
    
    # 检查模型文件
    models_dir = Path('../models')
    if not models_dir.exists():
        print("❌ 模型目录不存在")
        return False
    
    model_count = sum(1 for _ in models_dir.rglob("*_model"))
    print(f"📊 找到 {model_count} 个模型文件")
    
    # 创建spec文件
    create_complete_spec_file()
    
    # 运行PyInstaller
    cmd = [sys.executable, "-m", "PyInstaller", "--clean", "--noconfirm", "complete_build.spec"]
    print(f"🚀 执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ 构建成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def create_complete_distribution():
    """创建完整的发布包"""
    print("📦 创建完整发布包")
    print("=" * 60)
    
    # 设置路径
    current_dir = Path(__file__).parent
    dist_dir = current_dir / "complete_distribution"
    
    # 清理旧的发布目录
    if dist_dir.exists():
        print("🧹 清理旧的发布目录...")
        shutil.rmtree(dist_dir)
    
    # 创建发布目录结构
    print("📁 创建发布目录结构...")
    dist_dir.mkdir(exist_ok=True)
    
    # 构建可执行文件版本
    print("🔨 构建可执行文件版本...")
    if build_complete_executable():
        # 移动可执行文件到发布目录
        exe_dir = dist_dir / "完整版可执行文件"
        exe_dir.mkdir(exist_ok=True)
        
        built_exe = Path("dist") / "耐应力腐蚀油套管智能预测系统_完整版.exe"
        if built_exe.exists():
            shutil.move(str(built_exe), exe_dir / "耐应力腐蚀油套管智能预测系统_完整版.exe")
            
            # 创建启动说明
            readme_content = """# 完整版可执行文件使用说明

## 特点
- 包含真实的AutoGluon机器学习模型
- 包含完整的前端资源（CSS、JS）
- 无需安装Python或任何依赖包
- 双击即可运行

## 启动方法
双击 "耐应力腐蚀油套管智能预测系统_完整版.exe" 即可启动

## 注意事项
- 首次运行可能需要30-60秒加载时间（模型较大）
- 文件大小约500MB-1GB（包含完整模型）
- 如果杀毒软件报警，请添加信任
- 确保8080端口未被占用
- 需要至少2GB可用内存

## 功能特色
- 真实的机器学习预测（非演示模式）
- 完整的前端界面和交互功能
- 支持所有5个阶段的预测模型
- 专业的数据验证和结果分析
"""
            
            with open(exe_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
                f.write(readme_content)
            
            print("✅ 完整版可执行文件已创建")
        else:
            print("❌ 可执行文件构建失败")
    
    # 创建便携版（包含模型）
    print("📋 创建完整便携版...")
    portable_dir = dist_dir / "完整版便携版"
    portable_dir.mkdir(exist_ok=True)
    
    # 复制主程序
    shutil.copy2("complete_standalone_app.py", portable_dir / "启动程序.py")
    
    # 复制Web资源
    if Path("complete_web").exists():
        shutil.copytree("complete_web", portable_dir / "complete_web")
    
    # 复制模型文件
    models_src = Path("../models")
    models_dst = portable_dir / "models"
    if models_src.exists():
        shutil.copytree(models_src, models_dst)
        print("✅ 模型文件已复制")
    
    # 创建启动脚本
    create_complete_portable_scripts(portable_dir)
    
    # 创建文档
    create_complete_documentation(dist_dir)
    
    # 创建压缩包
    create_complete_zip_package(dist_dir)
    
    print("\n✅ 完整发布包创建完成！")
    print(f"📁 发布目录: {dist_dir}")
    
    return True

def create_complete_portable_scripts(portable_dir):
    """创建完整便携版启动脚本"""
    
    # Windows批处理文件
    bat_content = '''@echo off
chcp 65001 >nul
title 耐应力腐蚀油套管智能预测系统 - 完整版

echo.
echo ========================================
echo 🔬 耐应力腐蚀油套管智能预测系统
echo 📊 完整版 - 包含真实AutoGluon模型
echo 🏭 宝武钢铁集团
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Python环境
    echo.
    echo 💡 请安装Python 3.8或更高版本
    echo 📥 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo 🔍 检查AutoGluon...
python -c "from autogluon.tabular import TabularPredictor" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到AutoGluon
    echo.
    echo 💡 请安装AutoGluon:
    echo pip install autogluon
    echo.
    pause
    exit /b 1
)

echo ✅ AutoGluon检查通过
echo 🚀 正在启动系统...
echo ⏳ 加载模型可能需要较长时间，请耐心等待...
echo.

python "启动程序.py"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 启动失败，请检查错误信息
    echo.
)

pause
'''
    
    with open(portable_dir / "启动系统.bat", 'w', encoding='utf-8') as f:
        f.write(bat_content)

def create_complete_documentation(dist_dir):
    """创建完整版文档"""
    
    readme_content = """# 耐应力腐蚀油套管智能预测系统 - 完整版发布包

## 📋 包含内容

### 1. 完整版可执行文件（推荐）
- 📁 位置: `完整版可执行文件/` 文件夹
- 🔧 要求: 无需Python环境
- 🚀 启动: 双击可执行文件
- 💾 大小: 约500MB-1GB
- ⭐ 特点: 包含真实AutoGluon模型

### 2. 完整版便携版
- 📁 位置: `完整版便携版/` 文件夹  
- 🔧 要求: 需要Python 3.8+ 和 AutoGluon
- 🚀 启动: 双击启动脚本
- 💾 大小: 约200-500MB
- ⭐ 特点: 包含真实模型文件

## 🎯 主要改进

### 真实模型集成
- ✅ 包含所有5个阶段的AutoGluon模型
- ✅ 真实的机器学习预测（非演示模式）
- ✅ 专业的预测精度和可靠性

### 完整前端资源
- ✅ 所有CSS样式文件已内置
- ✅ 所有JavaScript文件已内置
- ✅ 完整的响应式界面
- ✅ 修复了渲染问题

### 增强功能
- ✅ 真实的数据验证
- ✅ 准确的特征匹配检查
- ✅ 专业的预测结果分析
- ✅ 完整的错误处理

## 🔧 系统要求

### 完整版可执行文件
- Windows 7/8/10/11
- 至少2GB可用内存
- 至少1GB可用磁盘空间

### 完整版便携版
- Python 3.8或更高版本
- AutoGluon框架
- pandas, numpy等依赖包
- 至少2GB可用内存

## 📞 技术支持
如遇问题，请联系技术支持团队

---
© 2024 宝武钢铁集团 - 完整版
"""
    
    with open(dist_dir / "README_完整版.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)

def create_complete_zip_package(dist_dir):
    """创建完整版ZIP压缩包"""
    print("📦 创建完整版ZIP压缩包...")
    
    zip_path = dist_dir.parent / "耐应力腐蚀油套管智能预测系统_完整版发布包.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(dist_dir)
                zipf.write(file_path, arc_path)
    
    print(f"✅ 完整版ZIP包已创建: {zip_path}")

if __name__ == '__main__':
    try:
        create_complete_distribution()
        print("\n🎉 完整版发布包创建完成！")
        input("按回车键退出...")
    except Exception as e:
        print(f"\n❌ 创建完整版发布包失败: {e}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
