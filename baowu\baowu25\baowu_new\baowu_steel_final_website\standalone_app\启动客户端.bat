@echo off
chcp 65001 >nul
title 预测系统客户端

echo.
echo ========================================
echo 🔬 耐应力腐蚀油套管智能预测系统
echo 💻 客户端启动程序
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Python环境
    echo 💡 请安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过

echo.
echo 🔍 检查依赖包...
python -c "import requests" >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo 正在安装requests...
    pip install requests
)

echo.
echo 📡 默认服务器地址: http://127.0.0.1:5000
echo 🔧 如需修改，请设置环境变量 SERVER_HOST 和 SERVER_PORT

echo.
echo 🚀 启动客户端...
echo 💡 客户端将自动寻找可用端口启动
echo 🌐 浏览器会自动打开
echo.

python client_frontend.py

pause
