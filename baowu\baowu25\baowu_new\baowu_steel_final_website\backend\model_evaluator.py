#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型评估模块
用于评估模型在含有目标值的数据上的预测效果
"""

import pandas as pd
import numpy as np
from sklearn.metrics import (
    mean_squared_error, mean_absolute_error, r2_score,
    mean_absolute_percentage_error, explained_variance_score
)
from scipy.stats import pearsonr
import logging
import json
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ModelEvaluator:
    """模型评估器"""
    
    def __init__(self):
        self.evaluation_results = {}
        
    def calculate_regression_metrics(self, y_true, y_pred, target_name="目标值"):
        """计算回归评估指标"""
        try:
            # 确保数据为numpy数组
            y_true = np.array(y_true)
            y_pred = np.array(y_pred)
            
            # 移除NaN值
            mask = ~(np.isnan(y_true) | np.isnan(y_pred))
            y_true_clean = y_true[mask]
            y_pred_clean = y_pred[mask]
            
            if len(y_true_clean) == 0:
                return {
                    'error': '没有有效的预测-真实值对',
                    'valid_samples': 0
                }
            
            # 计算基本统计指标
            metrics = {
                'valid_samples': len(y_true_clean),
                'total_samples': len(y_true),
                'target_name': target_name
            }
            
            # 均方误差 (MSE)
            mse = mean_squared_error(y_true_clean, y_pred_clean)
            metrics['mse'] = float(mse)
            
            # 均方根误差 (RMSE)
            rmse = np.sqrt(mse)
            metrics['rmse'] = float(rmse)
            
            # 平均绝对误差 (MAE)
            mae = mean_absolute_error(y_true_clean, y_pred_clean)
            metrics['mae'] = float(mae)
            
            # 决定系数 (R²)
            r2 = r2_score(y_true_clean, y_pred_clean)
            metrics['r2'] = float(r2)
            
            # 解释方差分数
            evs = explained_variance_score(y_true_clean, y_pred_clean)
            metrics['explained_variance'] = float(evs)
            
            # 平均绝对百分比误差 (MAPE)
            # 避免除零错误
            non_zero_mask = y_true_clean != 0
            if np.sum(non_zero_mask) > 0:
                mape = mean_absolute_percentage_error(
                    y_true_clean[non_zero_mask],
                    y_pred_clean[non_zero_mask]
                )
                metrics['mape'] = float(mape * 100)  # 转换为百分比
            else:
                metrics['mape'] = None

            # 皮尔逊相关系数 (Pearson Correlation Coefficient)
            try:
                pearson_corr, pearson_p_value = pearsonr(y_true_clean, y_pred_clean)
                metrics['pearson_correlation'] = float(pearson_corr)
                metrics['pearson_p_value'] = float(pearson_p_value)
            except Exception as e:
                logger.warning(f"计算皮尔逊相关系数失败: {e}")
                metrics['pearson_correlation'] = None
                metrics['pearson_p_value'] = None

            # MAE/STD Ratio (误差与标准差比值)
            try:
                true_std = np.std(y_true_clean)
                if true_std > 0:
                    mae_std_ratio = mae / true_std
                    metrics['mae_std_ratio'] = float(mae_std_ratio)
                else:
                    metrics['mae_std_ratio'] = None
            except Exception as e:
                logger.warning(f"计算MAE/STD比值失败: {e}")
                metrics['mae_std_ratio'] = None
            
            # 相对误差统计
            relative_errors = np.abs(y_pred_clean - y_true_clean) / np.abs(y_true_clean)
            relative_errors = relative_errors[np.isfinite(relative_errors)]
            
            if len(relative_errors) > 0:
                metrics['mean_relative_error'] = float(np.mean(relative_errors) * 100)
                metrics['median_relative_error'] = float(np.median(relative_errors) * 100)
                metrics['max_relative_error'] = float(np.max(relative_errors) * 100)
            
            # 预测值统计
            metrics['pred_stats'] = {
                'mean': float(np.mean(y_pred_clean)),
                'std': float(np.std(y_pred_clean)),
                'min': float(np.min(y_pred_clean)),
                'max': float(np.max(y_pred_clean)),
                'median': float(np.median(y_pred_clean))
            }
            
            # 真实值统计
            metrics['true_stats'] = {
                'mean': float(np.mean(y_true_clean)),
                'std': float(np.std(y_true_clean)),
                'min': float(np.min(y_true_clean)),
                'max': float(np.max(y_true_clean)),
                'median': float(np.median(y_true_clean))
            }
            
            # 误差统计
            errors = y_pred_clean - y_true_clean
            metrics['error_stats'] = {
                'mean': float(np.mean(errors)),
                'std': float(np.std(errors)),
                'min': float(np.min(errors)),
                'max': float(np.max(errors)),
                'median': float(np.median(errors))
            }
            
            # 性能等级评估
            pearson_corr = metrics.get('pearson_correlation')
            mae_std_ratio = metrics.get('mae_std_ratio')
            metrics['performance_grade'] = self._evaluate_performance_grade(pearson_corr, mae_std_ratio)
            
            return metrics
            
        except Exception as e:
            logger.error(f"计算回归指标失败: {e}")
            return {
                'error': f'计算评估指标失败: {str(e)}',
                'valid_samples': 0
            }
    
    def _evaluate_performance_grade(self, pearson_corr, mae_std_ratio):
        """评估模型性能等级"""
        if pearson_corr is None or mae_std_ratio is None:
            return {'grade': 'Unknown', 'description': '无法评估'}

        # 基于皮尔逊相关系数和MAE/STD比值的综合评估
        if pearson_corr >= 0.95 and mae_std_ratio <= 0.5:
            return {'grade': 'Excellent', 'description': '优秀 (相关系数≥0.95, MAE/STD≤0.5)'}
        elif pearson_corr >= 0.8 and mae_std_ratio <= 0.8:
            return {'grade': 'Good', 'description': '良好 (相关系数≥0.8, MAE/STD≤0.8)'}
        elif pearson_corr >= 0.6 and mae_std_ratio <= 1.2:
            return {'grade': 'Fair', 'description': '一般 (相关系数≥0.6, MAE/STD≤1.2)'}
        elif pearson_corr >= 0.3:
            return {'grade': 'Poor', 'description': '较差 (相关系数≥0.3)'}
        else:
            return {'grade': 'Very Poor', 'description': '很差 (相关系数<0.3)'}
    
    def evaluate_predictions(self, predictions, true_values, target_name="目标值"):
        """评估预测结果"""
        logger.info(f"开始评估预测结果: {target_name}")
        
        # 计算评估指标
        metrics = self.calculate_regression_metrics(true_values, predictions, target_name)
        
        if 'error' not in metrics:
            logger.info(f"评估完成: R²={metrics.get('r2', 'N/A'):.4f}, "
                       f"RMSE={metrics.get('rmse', 'N/A'):.4f}")
        else:
            logger.error(f"评估失败: {metrics['error']}")
        
        return metrics
    
    def generate_evaluation_report(self, evaluation_results):
        """生成评估报告"""
        report = {
            'evaluation_summary': {
                'total_evaluations': len(evaluation_results),
                'timestamp': pd.Timestamp.now().isoformat(),
                'overall_performance': self._calculate_overall_performance(evaluation_results)
            },
            'detailed_results': evaluation_results
        }
        
        return report
    
    def _calculate_overall_performance(self, results):
        """计算整体性能"""
        if not results:
            return {'grade': 'No Data', 'description': '无评估数据'}
        
        # 收集所有有效的R²值
        r2_values = []
        mape_values = []
        
        for result in results:
            if 'error' not in result and result.get('r2') is not None:
                r2_values.append(result['r2'])
            if 'error' not in result and result.get('mape') is not None:
                mape_values.append(result['mape'])
        
        if not r2_values:
            return {'grade': 'No Valid Data', 'description': '无有效评估数据'}
        
        avg_r2 = np.mean(r2_values)
        avg_mape = np.mean(mape_values) if mape_values else None
        
        return {
            'average_r2': float(avg_r2),
            'average_mape': float(avg_mape) if avg_mape else None,
            'performance_grade': self._evaluate_performance_grade(avg_r2, avg_mape)
        }
    
    def save_evaluation_report(self, report, output_path="evaluation_report.json"):
        """保存评估报告"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"评估报告已保存到: {output_path}")
            return True
        except Exception as e:
            logger.error(f"保存评估报告失败: {e}")
            return False
    
    def create_evaluation_summary(self, metrics):
        """创建评估摘要"""
        if 'error' in metrics:
            return {
                'status': 'error',
                'message': metrics['error'],
                'valid_samples': metrics.get('valid_samples', 0)
            }
        
        summary = {
            'status': 'success',
            'target_name': metrics.get('target_name', '目标值'),
            'valid_samples': metrics.get('valid_samples', 0),
            'total_samples': metrics.get('total_samples', 0),
            'key_metrics': {
                '皮尔逊相关系数': round(metrics.get('pearson_correlation', 0), 4) if metrics.get('pearson_correlation') else None,
                'MAE/STD比值': round(metrics.get('mae_std_ratio', 0), 4) if metrics.get('mae_std_ratio') else None
            },
            'performance_grade': metrics.get('performance_grade', {}),
            'prediction_quality': self._assess_prediction_quality(metrics)
        }
        
        return summary
    
    def _assess_prediction_quality(self, metrics):
        """评估预测质量"""
        pearson_corr = metrics.get('pearson_correlation')
        mae_std_ratio = metrics.get('mae_std_ratio')

        quality_aspects = []

        # 皮尔逊相关系数评估
        if pearson_corr is not None:
            if pearson_corr >= 0.95:
                quality_aspects.append("预测值与真实值高度相关")
            elif pearson_corr >= 0.8:
                quality_aspects.append("预测值与真实值强相关")
            elif pearson_corr >= 0.6:
                quality_aspects.append("预测值与真实值中等相关")
            elif pearson_corr >= 0.3:
                quality_aspects.append("预测值与真实值弱相关")
            else:
                quality_aspects.append("预测值与真实值相关性很低")

        # MAE/STD比值评估
        if mae_std_ratio is not None:
            if mae_std_ratio <= 0.5:
                quality_aspects.append("误差相对于数据变异性很小")
            elif mae_std_ratio <= 0.8:
                quality_aspects.append("误差相对于数据变异性较小")
            elif mae_std_ratio <= 1.2:
                quality_aspects.append("误差相对于数据变异性适中")
            else:
                quality_aspects.append("误差相对于数据变异性较大")

        return quality_aspects

def main():
    """测试函数"""
    print("🧪 测试模型评估器...")
    
    # 创建评估器
    evaluator = ModelEvaluator()
    
    # 模拟数据
    y_true = np.array([100, 200, 150, 300, 250])
    y_pred = np.array([95, 210, 145, 290, 260])
    
    # 评估
    metrics = evaluator.evaluate_predictions(y_pred, y_true, "测试目标")
    
    # 创建摘要
    summary = evaluator.create_evaluation_summary(metrics)
    
    print("✅ 评估完成")
    print(f"R²: {summary['key_metrics']['R²']}")
    print(f"RMSE: {summary['key_metrics']['RMSE']}")
    print(f"性能等级: {summary['performance_grade']['description']}")

if __name__ == '__main__':
    main()
