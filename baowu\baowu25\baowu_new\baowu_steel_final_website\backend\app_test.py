"""
基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统简化版测试应用
"""
from flask import Flask, request, jsonify
from flask_cors import CORS
import pandas as pd
import numpy as np
import os
import json
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

CORS(app, resources={
    r"/*": {
        "origins": ["http://localhost:3000", "http://127.0.0.1:3000",
                   "http://localhost:8080", "http://127.0.0.1:8080",
                   "file://", "*"],
        "methods": ["GET", "POST", "OPTIONS", "PUT", "DELETE"],
        "allow_headers": ["Content-Type", "Authorization", "Accept"],
        "supports_credentials": False
    }
})

MODEL_BASE_PATH = ["../models/stage_1", "../models/stage_2","../models/stage_3_A","../models/stage_3_D","../models/stage_4"]
Des_of_models = {
    "../models/stage_1": "硬度相关预测模型",
    "../models/stage_2": "拉伸屈服预测模型",
    "../models/stage_3_A": "A法——抗硫预测模型",
    "../models/stage_3_D": "D法——抗硫预测模型",
    "../models/stage_4": "硬度和拉伸预测抗硫模型"
}

AVAILABLE_MODELS = {}

def scan_model_directory(model_stage_path):
    """扫描模型目录（测试版本）"""
    result = {}
    path = Path(model_stage_path)
    if not path.exists():
        logger.warning(f"模型路径不存在: {model_stage_path}")
        return result
    
    for model_dir in path.iterdir():
        if model_dir.is_dir() and model_dir.name.endswith("_model"):
            model_name = model_dir.name.replace("_model", "")
            result[model_name] = {
                "path": str(model_dir),
                "target": model_name,
                "desc": f"{Des_of_models[model_stage_path]} - 预测 [{model_name}]"
            }
    return result

# 填充模型信息
for stage_path in MODEL_BASE_PATH:
    AVAILABLE_MODELS[stage_path] = scan_model_directory(stage_path)

print("发现的模型:")
print(AVAILABLE_MODELS)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'message': '基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统运行正常',
        'version': '1.0.0'
    })

@app.route('/api/models', methods=['GET', 'OPTIONS'])
def get_available_models():
    """获取所有阶段下的模型列表"""
    if request.method == 'OPTIONS':
        return '', 200
        
    response = []
    
    for stage, models in AVAILABLE_MODELS.items():
        stage_desc = Des_of_models.get(stage, "未知阶段")
        stage_models = []
        for model_name, config in models.items():
            exists = os.path.exists(config['path'])
            stage_models.append({
                'name': model_name,
                'path': config['path'],
                'target': config['target'],
                'stage': stage_desc,
                'available': exists
            })
        response.append({
            'stage': stage,
            'stage_desc': stage_desc,
            'models': stage_models
        })
    
    logger.info(f"返回模型列表: {len(response)} 个阶段")
    return jsonify(response)

@app.route('/api/predict', methods=['POST', 'OPTIONS'])
def predict():
    """预测接口（测试版本）"""
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        data = request.get_json()
        if not data or 'stage' not in data or 'model_name' not in data or 'data' not in data:
            return jsonify({'success': False, 'error': "参数缺失: stage、model_name 或 data"}), 400

        stage = data['stage']
        model_name = data['model_name']
        inputs = data['data']
        
        logger.info(f"预测请求: stage={stage}, model={model_name}, samples={len(inputs)}")
        
        # 模拟预测结果
        results = []
        predictions = []
        for i, input_data in enumerate(inputs):
            # 生成模拟预测值
            pred_value = np.random.uniform(10, 100)  # 模拟预测值
            results.append({
                'index': i,
                'pred': round(pred_value, 2),
                'input': input_data
            })
            predictions.append(pred_value)

        # 计算统计信息（与app.py保持一致）
        statistics = {
            '数量': len(predictions),
            '平均值': round(np.mean(predictions), 4),
            '标准差': round(np.std(predictions), 4),
            '最小值': round(np.min(predictions), 4),
            '最大值': round(np.max(predictions), 4),
            '中位数': round(np.median(predictions), 4)
        }

        return jsonify({
            'success': True,
            'results': results,
            'statistics': statistics,
            'stage': stage,
            'model': model_name,
            'target': model_name,  # 测试模式下使用模型名作为目标
            'processed_rows': len(inputs),
            'total_rows': len(inputs),
            'message': f'成功预测 {len(results)} 个样本 (测试模式)'
        })
        
    except Exception as e:
        logger.error(f"预测过程中发生错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'预测失败: {str(e)}'
        }), 500

@app.route('/api/validate_data', methods=['POST', 'OPTIONS'])
def validate_data():
    """数据验证接口"""
    if request.method == 'OPTIONS':
        return '', 200
        
    try:
        data = request.get_json()
        if not data or 'data' not in data:
            return jsonify({'success': False, 'error': "缺少数据"}), 400
        
        input_data = data['data']
        
        # 基本验证
        if not isinstance(input_data, list) or len(input_data) == 0:
            return jsonify({'success': False, 'error': "数据格式错误"}), 400
        
        return jsonify({
            'success': True,
            'message': f'数据验证通过，共 {len(input_data)} 条记录',
            'sample_count': len(input_data),
            'columns': list(input_data[0].keys()) if input_data else []
        })
        
    except Exception as e:
        logger.error(f"数据验证错误: {str(e)}")
        return jsonify({
            'success': False,
            'error': f'数据验证失败: {str(e)}'
        }), 500



@app.route('/api/model_info/<stage>/<model_name>', methods=['GET', 'OPTIONS'])
def get_model_info(stage, model_name):
    """获取模型详细信息（测试版本）"""
    if request.method == 'OPTIONS':
        return '', 200

    try:
        # 模拟模型信息
        return jsonify({
            'success': True,
            'model_name': model_name,
            'stage': stage,
            'target': model_name,
            'desc': f'测试模式 - {model_name}',
            'num_features': 10,  # 模拟特征数量
            'features': [f'feature_{i}' for i in range(1, 11)],  # 模拟特征列表
            'path': f'../models/{stage}/{model_name}_model',
            'is_special_model': False
        })

    except Exception as e:
        logger.error(f"获取模型信息失败: {str(e)}")
        return jsonify({'success': False, 'error': f"获取模型信息失败: {str(e)}"}), 500

if __name__ == '__main__':
    print("🚀 启动测试版预测服务...")
    print(f"📁 模型搜索路径: {MODEL_BASE_PATH}")
    
    # 检查模型可用性
    total_available = 0
    total_models = 0
    
    for stage_path, models in AVAILABLE_MODELS.items():
        stage_available = 0
        stage_total = len(models)
        total_models += stage_total
        
        print(f"\n📋 {Des_of_models.get(stage_path, stage_path)}:")
        for model_name, config in models.items():
            if os.path.exists(config['path']):
                print(f"  ✅ {model_name} - {config['target']}")
                stage_available += 1
                total_available += 1
            else:
                print(f"  ❌ {model_name} - 路径不存在: {config['path']}")
        
        print(f"  📊 本阶段: {stage_available}/{stage_total} 个模型可用")
    
    print(f"\n🎯 总计: {total_available}/{total_models} 个模型可用")
    print("⚠️  注意：这是测试版本，使用模拟预测结果")
    print(f"\n🌐 启动地址: http://localhost:5000")
    print("🔧 CORS已配置，支持跨域访问")
    
    app.run(debug=True, host='0.0.0.0', port=5000, threaded=True)
