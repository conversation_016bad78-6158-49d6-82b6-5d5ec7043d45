#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的评估指标：皮尔逊相关系数和MAE/STD比值
"""

import numpy as np
import pandas as pd
from backend.model_evaluator import ModelEvaluator
from scipy.stats import pearsonr
import matplotlib.pyplot as plt

def test_new_metrics():
    """测试新增的评估指标"""
    print("🔬 测试新增的评估指标...")
    
    # 创建模型评估器
    evaluator = ModelEvaluator()
    
    # 生成测试数据
    np.random.seed(42)
    n_samples = 100
    
    # 场景1：高相关性数据
    print("\n📊 场景1：高相关性数据测试")
    true_values_1 = np.random.normal(100, 20, n_samples)
    pred_values_1 = true_values_1 + np.random.normal(0, 5, n_samples)  # 添加少量噪声
    
    metrics_1 = evaluator.calculate_regression_metrics(true_values_1, pred_values_1, "高相关性测试")
    print_metrics_summary(metrics_1, "高相关性")
    
    # 场景2：中等相关性数据
    print("\n📊 场景2：中等相关性数据测试")
    true_values_2 = np.random.normal(100, 20, n_samples)
    pred_values_2 = true_values_2 + np.random.normal(0, 15, n_samples)  # 添加更多噪声
    
    metrics_2 = evaluator.calculate_regression_metrics(true_values_2, pred_values_2, "中等相关性测试")
    print_metrics_summary(metrics_2, "中等相关性")
    
    # 场景3：低相关性数据
    print("\n📊 场景3：低相关性数据测试")
    true_values_3 = np.random.normal(100, 20, n_samples)
    pred_values_3 = np.random.normal(100, 20, n_samples)  # 完全随机
    
    metrics_3 = evaluator.calculate_regression_metrics(true_values_3, pred_values_3, "低相关性测试")
    print_metrics_summary(metrics_3, "低相关性")
    
    # 场景4：完美预测
    print("\n📊 场景4：完美预测测试")
    true_values_4 = np.random.normal(100, 20, n_samples)
    pred_values_4 = true_values_4.copy()  # 完美预测
    
    metrics_4 = evaluator.calculate_regression_metrics(true_values_4, pred_values_4, "完美预测测试")
    print_metrics_summary(metrics_4, "完美预测")
    
    # 验证计算正确性
    print("\n🔍 验证计算正确性...")
    verify_calculations(true_values_1, pred_values_1, metrics_1)
    
    # 生成可视化
    create_visualization([
        (true_values_1, pred_values_1, "高相关性", metrics_1),
        (true_values_2, pred_values_2, "中等相关性", metrics_2),
        (true_values_3, pred_values_3, "低相关性", metrics_3),
        (true_values_4, pred_values_4, "完美预测", metrics_4)
    ])
    
    print("\n✅ 新指标测试完成!")

def print_metrics_summary(metrics, scenario_name):
    """打印指标摘要"""
    if 'error' in metrics:
        print(f"❌ {scenario_name}: {metrics['error']}")
        return
    
    print(f"📈 {scenario_name}:")
    print(f"  R² = {metrics.get('r2', 'N/A'):.4f}")
    print(f"  RMSE = {metrics.get('rmse', 'N/A'):.4f}")
    print(f"  MAE = {metrics.get('mae', 'N/A'):.4f}")
    print(f"  皮尔逊相关系数 = {metrics.get('pearson_correlation', 'N/A'):.4f}")
    print(f"  MAE/STD比值 = {metrics.get('mae_std_ratio', 'N/A'):.4f}")
    
    if metrics.get('pearson_p_value') is not None:
        print(f"  皮尔逊p值 = {metrics.get('pearson_p_value'):.6f}")

def verify_calculations(true_values, pred_values, metrics):
    """验证计算正确性"""
    print("🔍 手动验证计算...")
    
    # 验证皮尔逊相关系数
    manual_pearson, manual_p = pearsonr(true_values, pred_values)
    auto_pearson = metrics.get('pearson_correlation')
    
    print(f"皮尔逊相关系数:")
    print(f"  手动计算: {manual_pearson:.6f}")
    print(f"  自动计算: {auto_pearson:.6f}")
    print(f"  差异: {abs(manual_pearson - auto_pearson):.8f}")
    
    # 验证MAE/STD比值
    mae = np.mean(np.abs(pred_values - true_values))
    std = np.std(true_values)
    manual_mae_std = mae / std
    auto_mae_std = metrics.get('mae_std_ratio')
    
    print(f"MAE/STD比值:")
    print(f"  手动计算: {manual_mae_std:.6f}")
    print(f"  自动计算: {auto_mae_std:.6f}")
    print(f"  差异: {abs(manual_mae_std - auto_mae_std):.8f}")
    
    # 验证通过标准
    pearson_ok = abs(manual_pearson - auto_pearson) < 1e-6
    mae_std_ok = abs(manual_mae_std - auto_mae_std) < 1e-6
    
    if pearson_ok and mae_std_ok:
        print("✅ 计算验证通过!")
    else:
        print("❌ 计算验证失败!")

def create_visualization(scenarios):
    """创建可视化图表"""
    print("📊 生成可视化图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('新增评估指标测试结果', fontsize=16)
    
    for i, (true_vals, pred_vals, name, metrics) in enumerate(scenarios):
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        # 散点图
        ax.scatter(true_vals, pred_vals, alpha=0.6, s=30)
        
        # 理想线
        min_val = min(np.min(true_vals), np.min(pred_vals))
        max_val = max(np.max(true_vals), np.max(pred_vals))
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线')
        
        # 设置标题和标签
        r2 = metrics.get('r2', 0)
        pearson = metrics.get('pearson_correlation', 0)
        mae_std = metrics.get('mae_std_ratio', 0)
        
        ax.set_title(f'{name}\nR²={r2:.3f}, Pearson={pearson:.3f}, MAE/STD={mae_std:.3f}')
        ax.set_xlabel('真实值')
        ax.set_ylabel('预测值')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    output_path = 'new_metrics_test_results.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 可视化图表已保存: {output_path}")

def test_evaluation_summary():
    """测试评估摘要功能"""
    print("\n🔬 测试评估摘要功能...")
    
    evaluator = ModelEvaluator()
    
    # 生成测试数据
    np.random.seed(123)
    true_values = np.random.normal(100, 20, 50)
    pred_values = true_values + np.random.normal(0, 8, 50)
    
    # 计算指标
    metrics = evaluator.calculate_regression_metrics(true_values, pred_values, "测试模型")
    
    # 创建摘要
    summary = evaluator.create_evaluation_summary(metrics)
    
    print("📋 评估摘要:")
    print(f"  状态: {summary.get('status')}")
    print(f"  目标名称: {summary.get('target_name')}")
    print(f"  有效样本: {summary.get('valid_samples')}")
    
    print("📊 关键指标:")
    key_metrics = summary.get('key_metrics', {})
    for metric_name, value in key_metrics.items():
        if value is not None:
            print(f"  {metric_name}: {value}")
    
    print("🏆 性能等级:")
    grade = summary.get('performance_grade', {})
    print(f"  等级: {grade.get('grade')}")
    print(f"  描述: {grade.get('description')}")
    
    print("📈 质量评估:")
    quality = summary.get('prediction_quality', [])
    for aspect in quality:
        print(f"  • {aspect}")

def main():
    """主函数"""
    print("🚀 启动新增评估指标测试...")
    
    # 测试新指标
    test_new_metrics()
    
    # 测试评估摘要
    test_evaluation_summary()
    
    print("\n🎉 所有测试完成!")

if __name__ == '__main__':
    main()
