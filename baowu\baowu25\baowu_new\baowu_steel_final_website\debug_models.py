#!/usr/bin/env python3
"""调试模型加载问题"""

import sys
import os
sys.path.append('backend')

# 导入必要的模块
from pathlib import Path

# 检查AutoGluon可用性
try:
    import autogluon
    AUTOGLUON_AVAILABLE = True
    print("✅ AutoGluon可用")
except ImportError:
    AUTOGLUON_AVAILABLE = False
    print("❌ AutoGluon不可用，使用演示模式")

# 模型路径配置
MODEL_BASE_PATH = [
    "../models/stage_1",
    "../models/stage_2", 
    "../models/stage_3_A",
    "../models/stage_3_D",
    "../models/stage_4"
]

# 阶段描述
Des_of_models = {
    "../models/stage_1": "硬度相关预测模型",
    "../models/stage_2": "拉伸屈服预测模型",
    "../models/stage_3_A": "A法——抗硫预测模型",
    "../models/stage_3_D": "D法——抗硫预测模型",
    "../models/stage_4": "硬度和拉伸预测抗硫模型"
}

def scan_model_directory(model_stage_path):
    """自动扫描每个 stage 下的模型目录，并提取模型名、路径和目标"""
    result = {}
    print(f"\n🔍 扫描路径: {model_stage_path}")
    print(f"📊 AUTOGLUON_AVAILABLE: {AUTOGLUON_AVAILABLE}")

    if not AUTOGLUON_AVAILABLE:
        print("  → 使用演示模式")
        # 演示模式：创建虚拟模型
        demo_models = {
            "../models/stage_1": ["硬度预测模型"],
            "../models/stage_2": ["拉伸强度预测模型"],
            "../models/stage_3_A": ["A法抗硫预测模型"],
            "../models/stage_3_D": ["D法抗硫预测模型"],
            "../models/stage_4": ["综合质量预测模型"]
        }

        if model_stage_path in demo_models:
            for model_name in demo_models[model_stage_path]:
                result[model_name] = {
                    "path": f"{model_stage_path}/{model_name}_demo",
                    "target": model_name.replace("预测模型", ""),
                    "desc": f"{Des_of_models[model_stage_path]} - 预测 [{model_name.replace('预测模型', '')}] (演示模式)"
                }
                print(f"  ✅ 演示模型: {model_name}")
        return result

    # 真实模式：扫描实际的模型目录
    print("  → 使用真实模式")
    path = Path(model_stage_path)
    if not path.exists():
        print(f"  ❌ 路径不存在: {model_stage_path}")
        return result

    print(f"  📁 路径存在，开始扫描...")
    for model_dir in path.iterdir():
        if model_dir.is_dir() and model_dir.name.endswith("_model"):
            model_name = model_dir.name.replace("_model", "")
            result[model_name] = {
                "path": str(model_dir),
                "target": model_name,
                "desc": f"{Des_of_models[model_stage_path]} - 预测 [{model_name}]"
            }
            print(f"  ✅ 真实模型: {model_name}")
    return result

# 测试模型扫描
print("🚀 开始调试模型扫描...")
AVAILABLE_MODELS = {}

for stage_path in MODEL_BASE_PATH:
    AVAILABLE_MODELS[stage_path] = scan_model_directory(stage_path)

print("\n📋 最终结果:")
for stage, models in AVAILABLE_MODELS.items():
    print(f"\n{stage}:")
    for model_name, config in models.items():
        print(f"  - {model_name}: {config['target']}")

print("\n🎯 总结:")
print(f"阶段数: {len(AVAILABLE_MODELS)}")
total_models = sum(len(models) for models in AVAILABLE_MODELS.values())
print(f"模型总数: {total_models}")
