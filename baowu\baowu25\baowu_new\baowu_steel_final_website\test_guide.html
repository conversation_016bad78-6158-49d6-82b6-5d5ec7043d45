<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引导功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .guide-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }
        .guide-modal.hidden {
            display: none;
        }
        .guide-modal-content {
            background: white;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80%;
            overflow-y: auto;
        }
        .guide-modal-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .guide-modal-body {
            padding: 20px;
        }
        .close-guide {
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
        }
        .guide-step-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>引导功能测试</h1>
        <p>这是一个简化的测试页面，用于验证引导功能的JavaScript代码。</p>
        
        <button id="testGuideBtn" class="btn btn-primary">测试引导功能</button>
        <button id="testStepBtn" class="btn btn-secondary">测试步骤切换</button>
        
        <div id="log" style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 5px;">
            <h3>日志输出：</h3>
            <div id="logContent"></div>
        </div>
    </div>

    <!-- 引导模态框 -->
    <div id="guideModal" class="guide-modal hidden">
        <div class="guide-modal-content">
            <div class="guide-modal-header">
                <h2>🎯 系统操作引导</h2>
                <button class="close-guide" onclick="closeGuideModal()">&times;</button>
            </div>
            <div class="guide-modal-body">
                <div id="guideSteps">
                    <!-- 引导步骤将动态加载 -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟引导数据
        const guideData = {
            guide_steps: [
                {
                    step: 1,
                    title: '第一步测试',
                    description: '这是第一步的描述',
                    details: ['详细信息1', '详细信息2'],
                    tips: '这是第一步的提示'
                },
                {
                    step: 2,
                    title: '第二步测试',
                    description: '这是第二步的描述',
                    details: ['详细信息A', '详细信息B'],
                    tips: '这是第二步的提示'
                },
                {
                    step: 3,
                    title: '第三步测试',
                    description: '这是第三步的描述',
                    details: ['详细信息X', '详细信息Y'],
                    tips: '这是第三步的提示'
                }
            ]
        };

        let currentGuideStep = 0;

        function log(message) {
            const logContent = document.getElementById('logContent');
            const time = new Date().toLocaleTimeString();
            logContent.innerHTML += `<div>[${time}] ${message}</div>`;
            logContent.scrollTop = logContent.scrollHeight;
        }

        function showGuideModal() {
            log('显示引导模态框');
            const modal = document.getElementById('guideModal');
            modal.classList.remove('hidden');
            currentGuideStep = 0;
            showGuideStep(currentGuideStep);
        }

        function closeGuideModal() {
            log('关闭引导模态框');
            document.getElementById('guideModal').classList.add('hidden');
        }

        function showGuideStep(stepIndex) {
            log(`显示引导步骤: ${stepIndex}`);
            
            if (!guideData || !guideData.guide_steps) {
                log('错误: 引导数据未加载');
                return;
            }
            
            const steps = guideData.guide_steps;
            if (stepIndex >= steps.length) {
                log('引导完成');
                closeGuideModal();
                return;
            }

            const step = steps[stepIndex];
            const stepsDiv = document.getElementById('guideSteps');
            
            if (!stepsDiv) {
                log('错误: 找不到引导步骤容器');
                return;
            }

            let html = `
                <div class="guide-step">
                    <h3>步骤 ${step.step}: ${step.title}</h3>
                    <p>${step.description}</p>
                    <ul>
                        ${step.details.map(detail => `<li>${detail}</li>`).join('')}
                    </ul>
                    <p><strong>提示:</strong> ${step.tips}</p>
                    <div class="guide-step-actions">
                        ${stepIndex > 0 ? `<button class="btn btn-secondary" data-step="${stepIndex - 1}">上一步</button>` : ''}
                        <button class="btn btn-primary" data-step="${stepIndex + 1}">
                            ${stepIndex === steps.length - 1 ? '完成引导' : '下一步'}
                        </button>
                        <button class="btn btn-secondary" onclick="closeGuideModal()">跳过</button>
                    </div>
                </div>
            `;

            stepsDiv.innerHTML = html;

            // 添加按钮事件监听器
            const stepButtons = stepsDiv.querySelectorAll('button[data-step]');
            log(`找到 ${stepButtons.length} 个步骤按钮`);
            
            stepButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    try {
                        const nextStep = parseInt(button.dataset.step);
                        log(`点击按钮，下一步: ${nextStep}`);
                        showGuideStep(nextStep);
                    } catch (error) {
                        log(`按钮点击处理错误: ${error.message}`);
                    }
                });
            });
        }

        // 测试按钮事件
        document.getElementById('testGuideBtn').addEventListener('click', showGuideModal);
        document.getElementById('testStepBtn').addEventListener('click', () => {
            log('测试步骤切换功能');
            showGuideStep(1);
        });

        log('测试页面加载完成');
    </script>
</body>
</html>
