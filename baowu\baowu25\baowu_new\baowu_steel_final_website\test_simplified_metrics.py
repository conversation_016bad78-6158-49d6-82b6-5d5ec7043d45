#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的评估指标：只计算皮尔逊相关系数和MAE/STD比值
"""

import numpy as np
import pandas as pd
from backend.model_evaluator import ModelEvaluator
from scipy.stats import pearsonr
import matplotlib.pyplot as plt

def test_simplified_metrics():
    """测试简化的评估指标"""
    print("🔬 测试简化的评估指标（只显示皮尔逊相关系数和MAE/STD比值）...")
    
    # 创建模型评估器
    evaluator = ModelEvaluator()
    
    # 生成测试数据
    np.random.seed(42)
    n_samples = 100
    
    # 场景1：高质量预测
    print("\n📊 场景1：高质量预测")
    true_values_1 = np.random.normal(100, 20, n_samples)
    pred_values_1 = true_values_1 + np.random.normal(0, 5, n_samples)  # 添加少量噪声
    
    metrics_1 = evaluator.calculate_regression_metrics(true_values_1, pred_values_1, "高质量预测")
    summary_1 = evaluator.create_evaluation_summary(metrics_1)
    print_simplified_summary(summary_1, "高质量预测")
    
    # 场景2：中等质量预测
    print("\n📊 场景2：中等质量预测")
    true_values_2 = np.random.normal(100, 20, n_samples)
    pred_values_2 = true_values_2 + np.random.normal(0, 15, n_samples)  # 添加更多噪声
    
    metrics_2 = evaluator.calculate_regression_metrics(true_values_2, pred_values_2, "中等质量预测")
    summary_2 = evaluator.create_evaluation_summary(metrics_2)
    print_simplified_summary(summary_2, "中等质量预测")
    
    # 场景3：低质量预测
    print("\n📊 场景3：低质量预测")
    true_values_3 = np.random.normal(100, 20, n_samples)
    pred_values_3 = np.random.normal(100, 20, n_samples)  # 完全随机
    
    metrics_3 = evaluator.calculate_regression_metrics(true_values_3, pred_values_3, "低质量预测")
    summary_3 = evaluator.create_evaluation_summary(metrics_3)
    print_simplified_summary(summary_3, "低质量预测")
    
    # 场景4：完美预测
    print("\n📊 场景4：完美预测")
    true_values_4 = np.random.normal(100, 20, n_samples)
    pred_values_4 = true_values_4.copy()  # 完美预测
    
    metrics_4 = evaluator.calculate_regression_metrics(true_values_4, pred_values_4, "完美预测")
    summary_4 = evaluator.create_evaluation_summary(metrics_4)
    print_simplified_summary(summary_4, "完美预测")
    
    # 生成可视化
    create_simplified_visualization([
        (true_values_1, pred_values_1, "高质量预测", summary_1),
        (true_values_2, pred_values_2, "中等质量预测", summary_2),
        (true_values_3, pred_values_3, "低质量预测", summary_3),
        (true_values_4, pred_values_4, "完美预测", summary_4)
    ])
    
    print("\n✅ 简化指标测试完成!")

def print_simplified_summary(summary, scenario_name):
    """打印简化的指标摘要"""
    if summary['status'] != 'success':
        print(f"❌ {scenario_name}: {summary.get('message', '评估失败')}")
        return
    
    print(f"📈 {scenario_name}:")
    
    # 只显示两个核心指标
    key_metrics = summary.get('key_metrics', {})
    pearson = key_metrics.get('皮尔逊相关系数')
    mae_std = key_metrics.get('MAE/STD比值')
    
    print(f"  皮尔逊相关系数: {pearson if pearson is not None else 'N/A'}")
    print(f"  MAE/STD比值: {mae_std if mae_std is not None else 'N/A'}")
    
    # 性能等级
    grade = summary.get('performance_grade', {})
    print(f"  性能等级: {grade.get('description', 'N/A')}")
    
    # 质量评估
    quality = summary.get('prediction_quality', [])
    if quality:
        print(f"  质量评估: {', '.join(quality)}")

def create_simplified_visualization(scenarios):
    """创建简化的可视化图表"""
    print("📊 生成简化可视化图表...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('简化评估指标测试结果\n（只显示皮尔逊相关系数和MAE/STD比值）', fontsize=16)
    
    for i, (true_vals, pred_vals, name, summary) in enumerate(scenarios):
        row = i // 2
        col = i % 2
        ax = axes[row, col]
        
        # 散点图
        ax.scatter(true_vals, pred_vals, alpha=0.6, s=30)
        
        # 理想线
        min_val = min(np.min(true_vals), np.min(pred_vals))
        max_val = max(np.max(true_vals), np.max(pred_vals))
        ax.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线')
        
        # 设置标题和标签
        key_metrics = summary.get('key_metrics', {})
        pearson = key_metrics.get('皮尔逊相关系数', 0)
        mae_std = key_metrics.get('MAE/STD比值', 0)

        # 处理None值
        pearson_str = f"{pearson:.3f}" if pearson is not None else "N/A"
        mae_std_str = f"{mae_std:.3f}" if mae_std is not None else "N/A"

        ax.set_title(f'{name}\n皮尔逊相关系数={pearson_str}, MAE/STD比值={mae_std_str}')
        ax.set_xlabel('真实值')
        ax.set_ylabel('预测值')
        ax.legend()
        ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    output_path = 'simplified_metrics_test_results.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"📊 简化可视化图表已保存: {output_path}")

def test_evaluation_api_format():
    """测试评估API返回格式"""
    print("\n🔬 测试评估API返回格式...")
    
    evaluator = ModelEvaluator()
    
    # 生成测试数据
    np.random.seed(123)
    true_values = np.random.normal(100, 20, 50)
    pred_values = true_values + np.random.normal(0, 8, 50)
    
    # 计算指标
    metrics = evaluator.calculate_regression_metrics(true_values, pred_values, "API测试模型")
    
    # 创建摘要
    summary = evaluator.create_evaluation_summary(metrics)
    
    print("📋 API返回格式预览:")
    print(f"状态: {summary.get('status')}")
    print(f"目标名称: {summary.get('target_name')}")
    print(f"有效样本: {summary.get('valid_samples')}")
    
    print("\n📊 关键指标（只显示两个）:")
    key_metrics = summary.get('key_metrics', {})
    for metric_name, value in key_metrics.items():
        if value is not None:
            print(f"  {metric_name}: {value}")
    
    print(f"\n🏆 性能等级: {summary.get('performance_grade', {}).get('description', 'N/A')}")
    
    print("\n📈 质量评估:")
    quality = summary.get('prediction_quality', [])
    for aspect in quality:
        print(f"  • {aspect}")
    
    # 模拟前端接收到的数据格式
    print("\n🌐 前端接收格式:")
    frontend_data = {
        'evaluation': {
            'summary': summary,
            'target_column': 'API测试模型',
            'evaluation_samples': summary.get('valid_samples')
        }
    }
    
    print("evaluation.summary.key_metrics:")
    for key, value in key_metrics.items():
        print(f"  metrics['{key}'] = {value}")

def compare_with_manual_calculation():
    """与手动计算对比验证"""
    print("\n🔍 与手动计算对比验证...")
    
    # 生成简单的测试数据
    true_vals = np.array([10, 20, 30, 40, 50])
    pred_vals = np.array([12, 18, 32, 38, 52])
    
    print("测试数据:")
    print(f"真实值: {true_vals}")
    print(f"预测值: {pred_vals}")
    
    # 手动计算
    manual_pearson, _ = pearsonr(true_vals, pred_vals)
    manual_mae = np.mean(np.abs(pred_vals - true_vals))
    manual_std = np.std(true_vals)
    manual_mae_std = manual_mae / manual_std
    
    print(f"\n手动计算:")
    print(f"  皮尔逊相关系数: {manual_pearson:.6f}")
    print(f"  MAE: {manual_mae:.6f}")
    print(f"  STD: {manual_std:.6f}")
    print(f"  MAE/STD比值: {manual_mae_std:.6f}")
    
    # 使用评估器计算
    evaluator = ModelEvaluator()
    metrics = evaluator.calculate_regression_metrics(true_vals, pred_vals, "验证测试")
    
    auto_pearson = metrics.get('pearson_correlation')
    auto_mae_std = metrics.get('mae_std_ratio')
    
    print(f"\n评估器计算:")
    print(f"  皮尔逊相关系数: {auto_pearson:.6f}")
    print(f"  MAE/STD比值: {auto_mae_std:.6f}")
    
    # 验证结果
    pearson_diff = abs(manual_pearson - auto_pearson)
    mae_std_diff = abs(manual_mae_std - auto_mae_std)
    
    print(f"\n验证结果:")
    print(f"  皮尔逊相关系数差异: {pearson_diff:.8f}")
    print(f"  MAE/STD比值差异: {mae_std_diff:.8f}")
    
    if pearson_diff < 1e-6 and mae_std_diff < 1e-6:
        print("✅ 计算验证通过!")
    else:
        print("❌ 计算验证失败!")

def main():
    """主函数"""
    print("🚀 启动简化评估指标测试...")
    
    # 测试简化指标
    test_simplified_metrics()
    
    # 测试API格式
    test_evaluation_api_format()
    
    # 验证计算正确性
    compare_with_manual_calculation()
    
    print("\n🎉 所有测试完成!")
    print("\n📋 总结:")
    print("✅ 只计算和显示皮尔逊相关系数和MAE/STD比值")
    print("✅ 移除了传统指标（R²、RMSE、MAE、MAPE）")
    print("✅ 基于新指标重新设计了性能等级评估")
    print("✅ 简化了质量评估逻辑")
    print("✅ 前端界面只显示两个核心指标")

if __name__ == '__main__':
    main()
