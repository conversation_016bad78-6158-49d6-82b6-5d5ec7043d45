@echo off
chcp 65001 >nul
title 构建独立可执行文件

echo.
echo ========================================
echo 🏗️ 耐应力腐蚀油套管智能预测系统
echo 📦 独立可执行文件构建工具
echo ========================================
echo.

echo 📋 构建步骤：
echo 1. 检查Python环境
echo 2. 安装PyInstaller
echo 3. 创建构建配置
echo 4. 编译可执行文件
echo 5. 打包发布文件
echo.

echo 🔍 检查Python环境...
python --version
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

echo.
echo 🚀 开始构建...
python build_spec.py

echo.
echo 📦 构建完成！检查结果...
if exist "dist\耐应力腐蚀油套管智能预测系统.exe" (
    echo ✅ 可执行文件构建成功！
    echo 📁 位置: dist\耐应力腐蚀油套管智能预测系统.exe
    echo.
    echo 📋 发布说明：
    echo 1. 将整个 dist 文件夹复制到目标计算机
    echo 2. 双击运行可执行文件即可启动系统
    echo 3. 无需安装Python或其他依赖
    echo 4. 系统会自动打开浏览器
    echo.
) else (
    echo ❌ 构建失败，请检查错误信息
)

pause
