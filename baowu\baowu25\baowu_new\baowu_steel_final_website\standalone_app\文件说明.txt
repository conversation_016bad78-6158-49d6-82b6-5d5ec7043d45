📁 standalone_app/ - 两套完整版系统

🔹 核心程序文件
├── server_backend.py              # 服务器端程序（Linux/Windows）
├── client_frontend.py             # 客户端程序（Windows）
├── complete_standalone_app.py     # 本地完整版程序（Windows）

🔹 启动脚本
├── 启动服务器.bat                 # Windows服务器启动脚本
├── 启动客户端.bat                 # Windows客户端启动脚本
├── start_server.sh                # Linux服务器启动脚本

🔹 构建工具
├── build_two_versions.py          # 两套版本构建脚本
├── build_complete_distribution.py # 单版本构建脚本
├── 构建两套完整版.bat             # 一键构建两套版本
├── 构建发布包.bat                 # 单版本构建工具

🔹 Web资源
├── client_web/                    # 客户端Web资源
│   ├── index.html                # 主页面
│   ├── static/                   # 静态资源（CSS、JS）
│   └── test_data/                # 测试数据
├── complete_web/                  # 完整版Web资源
│   ├── index.html                # 主页面
│   ├── static/                   # 静态资源（CSS、JS）
│   └── test_data/                # 测试数据

🔹 文档
├── README.md                      # 使用说明
└── 文件说明.txt                  # 本文件

🚀 快速使用：

方案一（分离版）：
1. 服务器端：双击 启动服务器.bat 或 python server_backend.py
2. 客户端：双击 启动客户端.bat 或 python client_frontend.py

方案二（本地版）：
1. 直接运行：python complete_standalone_app.py

构建可执行文件：
双击 构建两套完整版.bat

✨ 主要特色：
- 智能端口自动分配
- 支持多实例并发
- 18个真实AutoGluon模型
- 完整前端资源
- 两套灵活部署方案
