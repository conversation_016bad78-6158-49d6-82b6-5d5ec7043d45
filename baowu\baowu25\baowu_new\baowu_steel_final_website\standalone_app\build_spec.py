#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller构建脚本
用于将应用打包成独立的可执行文件
"""

import os
import sys
from pathlib import Path

def create_spec_file():
    """创建PyInstaller的spec文件"""
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

# 数据文件列表
datas = [
    ('web', 'web'),
]

a = Analysis(
    ['standalone_app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'http.server',
        'socketserver',
        'urllib.parse',
        'json',
        'threading',
        'webbrowser',
        'tempfile',
        'shutil',
        'pathlib',
        'random'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'sklearn',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='耐应力腐蚀油套管智能预测系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='app_icon.ico' if os.path.exists('app_icon.ico') else None,
)
'''
    
    with open('standalone_app.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ PyInstaller spec文件已创建")

def install_pyinstaller():
    """安装PyInstaller"""
    try:
        import PyInstaller
        print("✅ PyInstaller已安装")
        return True
    except ImportError:
        print("📦 正在安装PyInstaller...")
        os.system('pip install pyinstaller')
        try:
            import PyInstaller
            print("✅ PyInstaller安装成功")
            return True
        except ImportError:
            print("❌ PyInstaller安装失败")
            return False

def build_executable():
    """构建可执行文件"""
    print("🔨 开始构建独立可执行文件...")
    
    # 检查并安装PyInstaller
    if not install_pyinstaller():
        return False
    
    # 创建spec文件
    create_spec_file()
    
    # 运行PyInstaller
    cmd = 'pyinstaller --clean --noconfirm standalone_app.spec'
    print(f"🚀 执行命令: {cmd}")
    
    result = os.system(cmd)
    
    if result == 0:
        print("✅ 构建成功！")
        print("📁 可执行文件位置: dist/耐应力腐蚀油套管智能预测系统.exe")
        return True
    else:
        print("❌ 构建失败")
        return False

if __name__ == '__main__':
    print("🏗️ 耐应力腐蚀油套管智能预测系统 - 独立版本构建工具")
    print("=" * 60)
    
    # 切换到正确的目录
    os.chdir(Path(__file__).parent)
    
    if build_executable():
        print("\n🎉 构建完成！")
        print("📋 使用说明:")
        print("1. 将 dist 文件夹复制到目标计算机")
        print("2. 双击运行 '耐应力腐蚀油套管智能预测系统.exe'")
        print("3. 系统会自动打开浏览器访问应用")
        print("4. 无需安装Python或其他依赖")
    else:
        print("\n❌ 构建失败，请检查错误信息")
    
    input("\n按回车键退出...")
