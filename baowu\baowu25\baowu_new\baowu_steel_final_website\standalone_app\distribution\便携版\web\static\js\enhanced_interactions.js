/**
 * 增强交互效果模块
 */

setTimeout(function() {
    initializeEnhancedEffects();
}, 1500);

function initializeEnhancedEffects() {
    // 添加页面加载动画
    addPageLoader();
    
    // 初始化滚动动画
    initializeScrollAnimations();
    
    // 添加鼠标跟踪效果
    addMouseTrackingEffects();
    
    // 初始化键盘快捷键
    initializeKeyboardShortcuts();
    
    // 添加性能监控
    addPerformanceMonitoring();
    
    // 初始化主题切换
    initializeThemeToggle();
}

// 页面加载动画
function addPageLoader() {
    const loader = document.createElement('div');
    loader.className = 'page-loader';
    loader.innerHTML = `
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <h3>正在加载智能预测系统...</h3>
            <p>请稍候，系统正在初始化</p>
        </div>
    `;
    
    document.body.appendChild(loader);
    
    // 模拟加载过程
    setTimeout(() => {
        loader.classList.add('hidden');
        setTimeout(() => loader.remove(), 500);
    }, 2000);
}

// 滚动动画
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    // 观察所有步骤元素
    document.querySelectorAll('.step').forEach(step => {
        observer.observe(step);
    });
}

// 鼠标跟踪效果
function addMouseTrackingEffects() {
    let mouseX = 0;
    let mouseY = 0;
    
    document.addEventListener('mousemove', (e) => {
        mouseX = e.clientX;
        mouseY = e.clientY;
        
        // 更新背景渐变位置
        updateBackgroundGradient(mouseX, mouseY);
    });
    
    function updateBackgroundGradient(x, y) {
        const xPercent = (x / window.innerWidth) * 100;
        const yPercent = (y / window.innerHeight) * 100;
        
        document.body.style.background = `
            radial-gradient(circle at ${xPercent}% ${yPercent}%, 
                rgba(102, 126, 234, 0.1) 0%, 
                transparent 50%),
            linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%)
        `;
    }
}

// 键盘快捷键
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
        // Ctrl/Cmd + Enter: 开始预测
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            const predictBtn = document.getElementById('predictBtn');
            if (predictBtn && !predictBtn.disabled) {
                predictBtn.click();
                showNotification('快捷键触发预测', 'info');
            }
        }
        
        // Ctrl/Cmd + U: 上传文件
        if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
            e.preventDefault();
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                fileInput.click();
                showNotification('快捷键触发文件上传', 'info');
            }
        }
        
        // Ctrl/Cmd + P: 预览数据
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
            e.preventDefault();
            const previewBtn = document.getElementById('previewBtn');
            if (previewBtn && !previewBtn.disabled) {
                previewBtn.click();
                showNotification('快捷键触发数据预览', 'info');
            }
        }
        
        // Esc: 关闭通知
        if (e.key === 'Escape') {
            document.querySelectorAll('.notification').forEach(notification => {
                notification.remove();
            });
        }
    });
}

// 性能监控
function addPerformanceMonitoring() {
    // 监控页面性能
    if ('performance' in window) {
        window.addEventListener('load', () => {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                
                if (loadTime > 3000) {
                    console.warn('页面加载时间较长:', loadTime + 'ms');
                }
                
                // 添加性能指标到控制台
                console.log('页面性能指标:', {
                    '页面加载时间': loadTime + 'ms',
                    'DOM构建时间': (perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart) + 'ms',
                    '首次内容绘制': performance.getEntriesByName('first-contentful-paint')[0]?.startTime + 'ms'
                });
            }, 0);
        });
    }
}

// 主题切换功能
function initializeThemeToggle() {
    // 检测系统主题偏好
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)');
    
    // 创建主题切换按钮
    const themeToggle = document.createElement('button');
    themeToggle.className = 'theme-toggle';
    themeToggle.innerHTML = '🌙';
    themeToggle.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        border: none;
        background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
        color: white;
        font-size: 20px;
        cursor: pointer;
        z-index: 1000;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    `;
    
    document.body.appendChild(themeToggle);
    
    themeToggle.addEventListener('click', toggleTheme);
    
    function toggleTheme() {
        document.body.classList.toggle('dark-theme');
        themeToggle.innerHTML = document.body.classList.contains('dark-theme') ? '☀️' : '🌙';
        
        // 保存主题偏好
        localStorage.setItem('theme', document.body.classList.contains('dark-theme') ? 'dark' : 'light');
    }
    
    // 加载保存的主题
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark' || (!savedTheme && prefersDark.matches)) {
        document.body.classList.add('dark-theme');
        themeToggle.innerHTML = '☀️';
    }
}

// 增强的通知系统
function showEnhancedNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    
    notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
            <span style="font-size: 20px;">${icons[type]}</span>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" 
                    style="margin-left: auto; background: none; border: none; color: white; font-size: 18px; cursor: pointer;">×</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.opacity = '0';
            setTimeout(() => notification.remove(), 300);
        }
    }, duration);
    
    return notification;
}

// 数据验证增强
function enhanceDataValidation() {
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file) {
                // 文件类型验证
                const allowedTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
                if (!allowedTypes.includes(file.type)) {
                    showEnhancedNotification('请选择CSV或Excel文件', 'error');
                    fileInput.value = '';
                    return;
                }
                
                // 文件大小验证
                const maxSize = 50 * 1024 * 1024; // 50MB
                if (file.size > maxSize) {
                    showEnhancedNotification('文件大小不能超过50MB', 'error');
                    fileInput.value = '';
                    return;
                }
                
                showEnhancedNotification('文件验证通过', 'success');
            }
        });
    }
}

// 进度指示器
function createProgressIndicator(container, progress = 0) {
    const progressContainer = document.createElement('div');
    progressContainer.className = 'progress-indicator';
    
    const progressBar = document.createElement('div');
    progressBar.className = 'progress-bar';
    progressBar.style.width = progress + '%';
    
    progressContainer.appendChild(progressBar);
    container.appendChild(progressContainer);
    
    return {
        update: (newProgress) => {
            progressBar.style.width = Math.min(100, Math.max(0, newProgress)) + '%';
        },
        remove: () => {
            progressContainer.remove();
        }
    };
}

// 工具提示增强
function addTooltips() {
    document.querySelectorAll('[data-tooltip]').forEach(element => {
        element.classList.add('tooltip');
    });
}

// 无障碍功能增强
function enhanceAccessibility() {
    // 添加键盘导航支持
    document.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
            document.body.classList.add('keyboard-navigation');
        }
    });
    
    document.addEventListener('mousedown', () => {
        document.body.classList.remove('keyboard-navigation');
    });
    
    // 添加屏幕阅读器支持
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.style.cssText = 'position: absolute; left: -10000px; width: 1px; height: 1px; overflow: hidden;';
    document.body.appendChild(announcer);
    
    window.announceToScreenReader = (message) => {
        announcer.textContent = message;
    };
}

// 导出增强功能
window.EnhancedUI = {
    showNotification: showEnhancedNotification,
    createProgressIndicator,
    addTooltips,
    enhanceAccessibility,
    enhanceDataValidation
};

// 初始化所有增强功能
document.addEventListener('DOMContentLoaded', () => {
    enhanceDataValidation();
    addTooltips();
    enhanceAccessibility();
});
