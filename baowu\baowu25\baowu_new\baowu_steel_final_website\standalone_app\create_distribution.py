#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建完整的发布包
包含可执行文件和所有必要的资源
"""

import os
import sys
import shutil
import zipfile
from pathlib import Path
import subprocess

def create_distribution():
    """创建完整的发布包"""
    print("📦 创建耐应力腐蚀油套管智能预测系统发布包")
    print("=" * 60)
    
    # 设置路径
    current_dir = Path(__file__).parent
    dist_dir = current_dir / "distribution"
    
    # 清理旧的发布目录
    if dist_dir.exists():
        print("🧹 清理旧的发布目录...")
        shutil.rmtree(dist_dir)
    
    # 创建发布目录结构
    print("📁 创建发布目录结构...")
    dist_dir.mkdir(exist_ok=True)
    
    # 方法1: 创建便携版（Python脚本版）
    portable_dir = dist_dir / "便携版"
    portable_dir.mkdir(exist_ok=True)
    
    print("📋 复制便携版文件...")
    
    # 复制主程序
    shutil.copy2(current_dir / "standalone_app.py", portable_dir / "启动程序.py")
    
    # 复制Web资源
    web_src = current_dir / "web"
    web_dst = portable_dir / "web"
    if web_src.exists():
        shutil.copytree(web_src, web_dst)
    
    # 创建启动脚本
    create_portable_scripts(portable_dir)
    
    # 方法2: 尝试创建可执行文件版本
    try:
        print("🔨 尝试构建可执行文件版本...")
        exe_success = build_executable_version(dist_dir)
    except Exception as e:
        print(f"⚠️ 可执行文件构建失败: {e}")
        exe_success = False
    
    # 创建说明文档
    create_documentation(dist_dir)
    
    # 创建压缩包
    create_zip_package(dist_dir)
    
    print("\n✅ 发布包创建完成！")
    print(f"📁 发布目录: {dist_dir}")
    
    if exe_success:
        print("🎉 包含可执行文件版本和便携版")
    else:
        print("📝 仅包含便携版（需要Python环境）")
    
    return True

def create_portable_scripts(portable_dir):
    """创建便携版启动脚本"""
    
    # Windows批处理文件
    bat_content = '''@echo off
chcp 65001 >nul
title 耐应力腐蚀油套管智能预测系统

echo.
echo ========================================
echo 🔬 耐应力腐蚀油套管智能预测系统
echo 📊 基于机器学习的质量预测平台  
echo 🏭 宝武钢铁集团
echo ========================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Python环境
    echo.
    echo 💡 请安装Python 3.8或更高版本
    echo 📥 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo 🚀 正在启动系统...
echo.

python "启动程序.py"

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 启动失败，请检查错误信息
    echo.
)

pause
'''
    
    with open(portable_dir / "启动系统.bat", 'w', encoding='utf-8') as f:
        f.write(bat_content)
    
    # Linux/Mac shell脚本
    sh_content = '''#!/bin/bash

echo "========================================"
echo "🔬 耐应力腐蚀油套管智能预测系统"
echo "📊 基于机器学习的质量预测平台"
echo "🏭 宝武钢铁集团"
echo "========================================"
echo

echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 未找到Python3环境"
    echo
    echo "💡 请安装Python 3.8或更高版本"
    exit 1
fi

echo "✅ Python环境检查通过"
echo "🚀 正在启动系统..."
echo

python3 "启动程序.py"
'''
    
    sh_file = portable_dir / "启动系统.sh"
    with open(sh_file, 'w', encoding='utf-8') as f:
        f.write(sh_content)
    
    # 设置执行权限
    try:
        os.chmod(sh_file, 0o755)
    except:
        pass

def build_executable_version(dist_dir):
    """构建可执行文件版本"""
    try:
        # 检查PyInstaller
        try:
            import PyInstaller
        except ImportError:
            print("📦 安装PyInstaller...")
            subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], 
                         check=True, capture_output=True)
        
        # 创建spec文件
        spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

datas = [
    ('web', 'web'),
]

a = Analysis(
    ['standalone_app.py'],
    pathex=[],
    binaries=[],
    datas=datas,
    hiddenimports=[
        'http.server',
        'socketserver', 
        'urllib.parse',
        'json',
        'threading',
        'webbrowser',
        'tempfile',
        'shutil',
        'pathlib',
        'random'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'sklearn',
        'tensorflow',
        'torch',
        'jupyter',
        'IPython'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='耐应力腐蚀油套管智能预测系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
        
        with open('temp_build.spec', 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # 运行PyInstaller
        result = subprocess.run([
            sys.executable, "-m", "PyInstaller",
            "--clean", "--noconfirm", "temp_build.spec"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            # 移动可执行文件到发布目录
            exe_dir = dist_dir / "可执行文件版"
            exe_dir.mkdir(exist_ok=True)
            
            built_exe = Path("dist") / "耐应力腐蚀油套管智能预测系统.exe"
            if built_exe.exists():
                shutil.move(str(built_exe), exe_dir / "耐应力腐蚀油套管智能预测系统.exe")
                
                # 创建启动说明
                readme_content = """# 可执行文件版使用说明

## 启动方法
双击 "耐应力腐蚀油套管智能预测系统.exe" 即可启动

## 特点
- 无需安装Python环境
- 无需安装任何依赖包
- 双击即可运行
- 自动打开浏览器

## 注意事项
- 首次运行可能需要几秒钟加载时间
- 如果杀毒软件报警，请添加信任
- 确保8080端口未被占用
"""
                
                with open(exe_dir / "使用说明.txt", 'w', encoding='utf-8') as f:
                    f.write(readme_content)
                
                print("✅ 可执行文件版本构建成功")
                return True
        
        print(f"❌ PyInstaller构建失败: {result.stderr}")
        return False
        
    except Exception as e:
        print(f"❌ 构建可执行文件时出错: {e}")
        return False
    
    finally:
        # 清理临时文件
        for temp_file in ["temp_build.spec"]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        for temp_dir in ["build", "dist", "__pycache__"]:
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)

def create_documentation(dist_dir):
    """创建说明文档"""
    
    readme_content = """# 耐应力腐蚀油套管智能预测系统 - 发布包

## 📋 包含内容

### 1. 便携版（推荐）
- 📁 位置: `便携版/` 文件夹
- 🔧 要求: 需要Python 3.8+环境
- 🚀 启动: 双击 `启动系统.bat` (Windows) 或 `启动系统.sh` (Linux/Mac)
- 💾 大小: 约5MB

### 2. 可执行文件版（如果可用）
- 📁 位置: `可执行文件版/` 文件夹  
- 🔧 要求: 无需Python环境
- 🚀 启动: 双击 `耐应力腐蚀油套管智能预测系统.exe`
- 💾 大小: 约50-100MB

## 🎯 推荐使用方式

### 对于有Python环境的用户
使用便携版，启动速度快，占用空间小

### 对于没有Python环境的用户  
使用可执行文件版，无需安装任何软件

## 🔧 系统要求
- Windows 7/8/10/11
- 至少512MB可用内存
- 至少100MB可用磁盘空间

## 📞 技术支持
如遇问题，请联系技术支持团队

---
© 2024 宝武钢铁集团
"""
    
    with open(dist_dir / "README.txt", 'w', encoding='utf-8') as f:
        f.write(readme_content)

def create_zip_package(dist_dir):
    """创建ZIP压缩包"""
    print("📦 创建ZIP压缩包...")
    
    zip_path = dist_dir.parent / "耐应力腐蚀油套管智能预测系统_发布包.zip"
    
    with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(dist_dir):
            for file in files:
                file_path = Path(root) / file
                arc_path = file_path.relative_to(dist_dir)
                zipf.write(file_path, arc_path)
    
    print(f"✅ ZIP包已创建: {zip_path}")

if __name__ == '__main__':
    try:
        create_distribution()
        print("\n🎉 发布包创建完成！")
        input("按回车键退出...")
    except Exception as e:
        print(f"\n❌ 创建发布包失败: {e}")
        input("按回车键退出...")
