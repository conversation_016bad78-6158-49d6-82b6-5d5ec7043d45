#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "========================================"
echo -e "${BLUE}🚀 基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统启动器${NC}"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 错误：未找到Python，请先安装Python 3.8+${NC}"
        echo "Ubuntu/Debian: sudo apt-get install python3 python3-pip python3-venv"
        echo "CentOS/RHEL: sudo yum install python3 python3-pip"
        echo "macOS: brew install python3"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ Python已安装${NC}"
$PYTHON_CMD --version

# 检查是否存在虚拟环境
if [ ! -d "venv" ]; then
    echo -e "${YELLOW}📦 创建虚拟环境...${NC}"
    $PYTHON_CMD -m venv venv
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 创建虚拟环境失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 虚拟环境创建成功${NC}"
fi

# 激活虚拟环境
echo -e "${YELLOW}🔧 激活虚拟环境...${NC}"
source venv/bin/activate
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 激活虚拟环境失败${NC}"
    exit 1
fi

# 检查并安装依赖
echo -e "${YELLOW}📋 检查依赖包...${NC}"
if ! pip list | grep -q Flask; then
    echo -e "${YELLOW}📦 安装依赖包...${NC}"
    pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 安装依赖包失败${NC}"
        exit 1
    fi
    echo -e "${GREEN}✅ 依赖包安装完成${NC}"
else
    echo -e "${GREEN}✅ 依赖包已安装${NC}"
fi

# 检查模型文件
echo -e "${YELLOW}🔍 检查模型文件...${NC}"
if [ ! -d "models/stage_1" ]; then
    echo -e "${RED}❌ 警告：未找到模型文件，请确保models目录下有stage_1到stage_4的模型文件${NC}"
fi

# 启动后端服务
echo -e "${BLUE}🌐 启动后端服务...${NC}"
echo "服务地址：http://localhost:5000"
echo "前端页面：请在浏览器中打开 frontend/index.html"
echo
echo "选择启动模式："
echo "1. 测试模式（不需要AutoGluon，使用模拟预测）"
echo "2. 完整模式（需要AutoGluon，真实预测）"
echo
read -p "请输入选择 (1 或 2，默认为1): " mode
mode=${mode:-1}

if [ "$mode" = "2" ]; then
    echo -e "${GREEN}启动完整模式...${NC}"
    cd backend
    $PYTHON_CMD app.py
else
    echo -e "${GREEN}启动测试模式...${NC}"
    cd backend
    $PYTHON_CMD app_test.py
fi
