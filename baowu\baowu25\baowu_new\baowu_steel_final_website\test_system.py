#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统测试脚本
"""

import os
import sys
import time
import threading
import webbrowser

def test_system():
    """测试系统"""
    print("🔬 耐应力腐蚀油套管智能预测系统")
    print("🧪 系统测试")
    print("=" * 50)
    
    # 检查Python版本
    print(f"🐍 Python版本: {sys.version}")
    
    # 检查依赖包
    print("\n🔍 检查依赖包...")
    try:
        import flask
        print(f"✅ Flask: {flask.__version__}")
    except ImportError:
        print("❌ Flask未安装")
        return False
    
    try:
        import pandas
        print(f"✅ Pandas: {pandas.__version__}")
    except ImportError:
        print("❌ Pandas未安装")
        return False
    
    try:
        import numpy
        print(f"✅ NumPy: {numpy.__version__}")
    except ImportError:
        print("❌ NumPy未安装")
        return False
    
    # 检查AutoGluon
    try:
        from autogluon.tabular import TabularPredictor
        print("✅ AutoGluon: 已安装")
        autogluon_available = True
    except ImportError:
        print("⚠️ AutoGluon: 未安装（将使用演示模式）")
        autogluon_available = False
    
    # 检查模型文件
    print("\n🔍 检查模型文件...")
    models_dir = "models"
    if os.path.exists(models_dir):
        model_count = 0
        for stage in ["stage_1", "stage_2", "stage_3_A", "stage_3_D", "stage_4"]:
            stage_path = os.path.join(models_dir, stage)
            if os.path.exists(stage_path):
                stage_models = [d for d in os.listdir(stage_path) if d.endswith("_model")]
                model_count += len(stage_models)
        
        if model_count > 0:
            print(f"✅ 找到 {model_count} 个模型文件")
            models_available = True
        else:
            print("⚠️ 模型目录存在但未找到模型文件")
            models_available = False
    else:
        print("⚠️ 模型目录不存在")
        models_available = False
    
    # 检查模板文件
    print("\n🔍 检查模板文件...")
    template_file = os.path.join("backend", "templates", "index.html")
    if os.path.exists(template_file):
        print("✅ 模板文件存在")
    else:
        print("❌ 模板文件不存在")
        return False
    
    # 启动Flask应用
    print("\n🚀 启动Flask应用...")
    
    def start_flask():
        os.chdir("backend")
        sys.path.append(".")
        import app
        app.app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
    
    # 在新线程中启动Flask
    flask_thread = threading.Thread(target=start_flask)
    flask_thread.daemon = True
    flask_thread.start()
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(5)
    
    # 测试服务
    try:
        import requests
        response = requests.get('http://127.0.0.1:5000', timeout=5)
        if response.status_code == 200:
            print("✅ Flask应用启动成功")
            print("🌐 服务地址: http://127.0.0.1:5000")
            
            # 打开浏览器
            def open_browser():
                time.sleep(2)
                webbrowser.open('http://127.0.0.1:5000')
                print("🌍 已自动打开浏览器")
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            print("\n🎉 系统测试成功！")
            print("=" * 50)
            print("📋 系统状态:")
            print(f"   AutoGluon: {'可用' if autogluon_available else '演示模式'}")
            print(f"   模型文件: {'可用' if models_available else '演示模式'}")
            print(f"   运行模式: {'真实预测' if (autogluon_available and models_available) else '演示模式'}")
            print("=" * 50)
            print("💡 按 Ctrl+C 停止服务")
            
            # 保持服务运行
            try:
                while True:
                    time.sleep(1)
            except KeyboardInterrupt:
                print("\n👋 服务已停止")
                return True
                
        else:
            print(f"❌ Flask应用响应异常: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 无法连接到Flask应用: {e}")
        return False

if __name__ == '__main__':
    try:
        test_system()
    except KeyboardInterrupt:
        print("\n👋 测试已中断")
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
