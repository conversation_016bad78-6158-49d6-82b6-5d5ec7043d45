#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证评估功能
"""

import sys
import os
sys.path.append('backend')

def verify_evaluation_functionality():
    """验证评估功能"""
    print("🧪 验证评估功能")
    print("=" * 50)
    
    # 1. 测试模型评估器
    print("1️⃣ 测试模型评估器")
    try:
        from model_evaluator import ModelEvaluator
        
        evaluator = ModelEvaluator()
        print("✅ 模型评估器导入成功")
        
        # 测试评估功能
        import numpy as np
        y_true = np.array([100, 200, 150, 300, 250])
        y_pred = np.array([95, 210, 145, 290, 260])
        
        metrics = evaluator.evaluate_predictions(y_pred, y_true, "测试目标")
        
        if 'error' not in metrics:
            print("✅ 评估计算成功")
            print(f"   R²: {metrics.get('r2', 'N/A'):.4f}")
            print(f"   RMSE: {metrics.get('rmse', 'N/A'):.4f}")
            print(f"   MAE: {metrics.get('mae', 'N/A'):.4f}")
            print(f"   MAPE: {metrics.get('mape', 'N/A'):.2f}%" if metrics.get('mape') else "   MAPE: N/A")
            
            grade = metrics.get('performance_grade', {})
            print(f"   性能等级: {grade.get('description', 'N/A')}")
        else:
            print(f"❌ 评估计算失败: {metrics['error']}")
            
    except Exception as e:
        print(f"❌ 模型评估器测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 2. 测试app模块集成
    print(f"\n2️⃣ 测试app模块集成")
    try:
        import app
        
        print("✅ app模块导入成功")
        
        # 检查评估器是否已初始化
        if hasattr(app, 'model_evaluator') and app.model_evaluator:
            print("✅ 模型评估器已在app中初始化")
        else:
            print("⚠️ 模型评估器未在app中初始化")
        
        # 检查模式配置
        print("✅ 检查模式配置...")
        
        # 模拟请求测试
        with app.app.test_client() as client:
            # 测试模式接口
            response = client.get('/api/modes')
            if response.status_code == 200:
                data = response.get_json()
                if 'evaluation' in data.get('modes', {}):
                    print("✅ 评估模式已在API中配置")
                    eval_mode = data['modes']['evaluation']
                    print(f"   模式名称: {eval_mode['name']}")
                    print(f"   模式描述: {eval_mode['description']}")
                else:
                    print("❌ 评估模式未在API中找到")
            else:
                print(f"❌ 模式接口测试失败: {response.status_code}")
                
    except Exception as e:
        print(f"❌ app模块集成测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 3. 测试评估模式预测逻辑
    print(f"\n3️⃣ 测试评估模式预测逻辑")
    try:
        import app
        import pandas as pd
        import numpy as np
        
        # 创建测试数据
        test_data = {
            "外径": [88.9, 92.0, 85.5],
            "壁厚": [18.6, 20.0, 17.2],
            "硬度_油管硬度平均值": [25.5, 27.2, 24.8]  # 目标值
        }
        
        df = pd.DataFrame(test_data)
        print(f"✅ 创建测试数据: {df.shape}")
        
        # 模拟评估模式处理
        target_column = "硬度_油管硬度平均值"
        true_values = df[target_column].values
        
        print(f"✅ 提取目标值: {len(true_values)} 个")
        print(f"   目标值: {true_values}")
        
        # 模拟预测值（演示模式）
        predictions = true_values + np.random.normal(0, 1, len(true_values))
        print(f"✅ 生成预测值: {predictions}")
        
        # 使用评估器计算指标
        if app.model_evaluator:
            metrics = app.model_evaluator.evaluate_predictions(
                predictions, true_values, "硬度_油管硬度平均值"
            )
            
            if 'error' not in metrics:
                print("✅ 评估指标计算成功")
                print(f"   R²: {metrics.get('r2', 'N/A'):.4f}")
                print(f"   RMSE: {metrics.get('rmse', 'N/A'):.4f}")
                
                # 创建评估摘要
                summary = app.model_evaluator.create_evaluation_summary(metrics)
                print(f"✅ 评估摘要创建成功")
                print(f"   状态: {summary['status']}")
                print(f"   有效样本: {summary['valid_samples']}")
                
                if summary['status'] == 'success':
                    key_metrics = summary['key_metrics']
                    print(f"   关键指标: R²={key_metrics['R²']}, RMSE={key_metrics['RMSE']}")
                    
                    grade = summary['performance_grade']
                    print(f"   性能等级: {grade['description']}")
            else:
                print(f"❌ 评估指标计算失败: {metrics['error']}")
        else:
            print("⚠️ 模型评估器不可用")
            
    except Exception as e:
        print(f"❌ 评估模式预测逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    print(f"\n{'='*50}")
    print("🎉 评估功能验证完成！")

def main():
    """主函数"""
    print("🚀 开始评估功能验证...")
    verify_evaluation_functionality()

if __name__ == '__main__':
    main()
