#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据优化器
用于评估单阶段回归模型，剔除异常点，筛选出能够提高R²的数据点
"""

import pandas as pd
import numpy as np
import pickle
import json
import logging
from pathlib import Path
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 尝试导入AutoGluon
try:
    from autogluon.tabular import TabularPredictor
    AUTOGLUON_AVAILABLE = True
    print("✅ AutoGluon可用")
except ImportError:
    AUTOGLUON_AVAILABLE = False
    print("⚠️ AutoGluon未安装，将使用演示模式")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TestDataOptimizer:
    """测试数据优化器"""
    
    def __init__(self, models_dir="models", test_data_dir="test_data", output_dir="optimized_test_data"):
        self.models_dir = Path(models_dir)
        self.test_data_dir = Path(test_data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 单阶段回归模型配置
        self.stage_configs = {
            'stage_1': {
                'models': ['硬度_油井管硬度极差', '硬度_油管硬度平均值'],
                'test_file': 'test_stage_1.csv'
            },
            'stage_2': {
                'models': ['平均抗拉强度', '拉伸_平均屈服强度', '拉伸_最大屈服强度', 
                          '拉伸_最小屈服强度', '最大抗拉强度', '最小抗拉强度'],
                'test_file': 'test_stage_2.csv'
            },
            'stage_3_A': {
                'models': ['抗硫_合格率', '抗硫_最小承载时间'],
                'test_file': 'test_stage_3_A.csv'
            },
            'stage_3_D': {
                'models': ['抗硫_平均抗硫因子', '抗硫_最大抗硫因子', '抗硫_最小抗硫因子'],
                'test_file': 'test_stage_3_D.csv'
            },
            'stage_4': {
                'models': ['抗硫_合格率', '抗硫_平均抗硫因子', '抗硫_最大抗硫因子', 
                          '抗硫_最小承载时间', '抗硫_最小抗硫因子'],
                'test_file': 'test_stage_4.csv'
            }
        }
        
        self.evaluation_results = {}
        self.optimized_data = {}
    
    def load_model(self, model_path):
        """加载模型"""
        try:
            if AUTOGLUON_AVAILABLE:
                # 尝试加载AutoGluon模型
                if (model_path / 'predictor.pkl').exists():
                    model = TabularPredictor.load(str(model_path))
                    logger.info(f"成功加载AutoGluon模型: {model_path}")
                    return model
                else:
                    logger.warning(f"未找到AutoGluon模型文件: {model_path}")
                    return None
            else:
                # 演示模式：创建模拟模型
                logger.warning(f"演示模式：创建模拟模型 {model_path}")
                return self.create_mock_model()
        except Exception as e:
            logger.error(f"加载模型失败 {model_path}: {e}")
            return None

    def create_mock_model(self):
        """创建模拟模型用于演示"""
        class MockModel:
            def predict(self, X):
                # 简单的线性模拟预测
                np.random.seed(42)
                return np.random.normal(100, 20, len(X))

        return MockModel()
    
    def load_test_data(self, test_file):
        """加载测试数据"""
        try:
            test_path = self.test_data_dir / test_file
            data = pd.read_csv(test_path)
            logger.info(f"加载测试数据: {test_file}, 形状: {data.shape}")
            return data
        except Exception as e:
            logger.error(f"加载测试数据失败 {test_file}: {e}")
            return None
    
    def evaluate_model_on_test_data(self, model, test_data, target_column):
        """在测试数据上评估模型"""
        try:
            # 检查目标列是否存在
            if target_column not in test_data.columns:
                logger.warning(f"目标列 {target_column} 不存在于测试数据中")
                return None
            
            # 准备特征和目标
            y_true = test_data[target_column].values
            X_test = test_data.drop(columns=[target_column])
            
            # 移除包含NaN的行
            valid_mask = ~(pd.isna(y_true) | X_test.isna().any(axis=1))
            X_test_clean = X_test[valid_mask]
            y_true_clean = y_true[valid_mask]
            
            if len(y_true_clean) == 0:
                logger.warning(f"没有有效的测试数据用于评估 {target_column}")
                return None
            
            # 预测
            y_pred = model.predict(X_test_clean)
            
            # 计算评估指标
            r2 = r2_score(y_true_clean, y_pred)
            rmse = np.sqrt(mean_squared_error(y_true_clean, y_pred))
            mae = mean_absolute_error(y_true_clean, y_pred)
            
            # 计算残差
            residuals = y_pred - y_true_clean
            
            evaluation = {
                'target_column': target_column,
                'n_samples': len(y_true_clean),
                'r2': r2,
                'rmse': rmse,
                'mae': mae,
                'y_true': y_true_clean,
                'y_pred': y_pred,
                'residuals': residuals,
                'valid_indices': np.where(valid_mask)[0]
            }
            
            logger.info(f"模型评估完成 {target_column}: R²={r2:.4f}, RMSE={rmse:.4f}")
            return evaluation
            
        except Exception as e:
            logger.error(f"模型评估失败 {target_column}: {e}")
            return None
    
    def identify_outliers(self, evaluation, method='combined', threshold_factor=2.5):
        """识别异常点"""
        try:
            residuals = evaluation['residuals']
            y_true = evaluation['y_true']
            y_pred = evaluation['y_pred']
            
            outlier_indices = set()
            
            if method in ['residual', 'combined']:
                # 基于残差的异常点检测
                residual_threshold = threshold_factor * np.std(residuals)
                residual_outliers = np.where(np.abs(residuals) > residual_threshold)[0]
                outlier_indices.update(residual_outliers)
            
            if method in ['zscore', 'combined']:
                # 基于Z-score的异常点检测
                z_scores = np.abs(stats.zscore(residuals))
                zscore_outliers = np.where(z_scores > threshold_factor)[0]
                outlier_indices.update(zscore_outliers)
            
            if method in ['iqr', 'combined']:
                # 基于IQR的异常点检测
                Q1 = np.percentile(residuals, 25)
                Q3 = np.percentile(residuals, 75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                iqr_outliers = np.where((residuals < lower_bound) | (residuals > upper_bound))[0]
                outlier_indices.update(iqr_outliers)
            
            outlier_indices = list(outlier_indices)
            
            logger.info(f"识别到 {len(outlier_indices)} 个异常点 (总样本: {len(residuals)})")
            
            return {
                'outlier_indices': outlier_indices,
                'outlier_ratio': len(outlier_indices) / len(residuals),
                'method': method,
                'threshold_factor': threshold_factor
            }
            
        except Exception as e:
            logger.error(f"异常点识别失败: {e}")
            return {'outlier_indices': [], 'outlier_ratio': 0}
    
    def optimize_data_for_r2(self, evaluation, outlier_info, min_samples_ratio=0.7):
        """优化数据以提高R²"""
        try:
            outlier_indices = outlier_info['outlier_indices']
            total_samples = len(evaluation['y_true'])
            min_samples = int(total_samples * min_samples_ratio)
            
            # 如果异常点太多，只移除最严重的异常点
            if len(outlier_indices) > total_samples - min_samples:
                residuals = evaluation['residuals']
                # 按残差绝对值排序，只移除最严重的异常点
                sorted_outliers = sorted(outlier_indices, 
                                       key=lambda i: abs(residuals[i]), 
                                       reverse=True)
                outlier_indices = sorted_outliers[:total_samples - min_samples]
            
            # 创建优化后的数据索引
            all_indices = set(range(total_samples))
            optimized_indices = list(all_indices - set(outlier_indices))
            
            # 计算优化后的R²
            y_true_opt = evaluation['y_true'][optimized_indices]
            y_pred_opt = evaluation['y_pred'][optimized_indices]
            
            r2_original = evaluation['r2']
            r2_optimized = r2_score(y_true_opt, y_pred_opt)
            r2_improvement = r2_optimized - r2_original
            
            optimization_result = {
                'original_r2': r2_original,
                'optimized_r2': r2_optimized,
                'r2_improvement': r2_improvement,
                'original_samples': total_samples,
                'optimized_samples': len(optimized_indices),
                'removed_samples': len(outlier_indices),
                'removal_ratio': len(outlier_indices) / total_samples,
                'optimized_indices': optimized_indices,
                'removed_indices': outlier_indices
            }
            
            logger.info(f"数据优化完成: R²从 {r2_original:.4f} 提升到 {r2_optimized:.4f} "
                       f"(提升 {r2_improvement:.4f}), 移除 {len(outlier_indices)} 个样本")
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"数据优化失败: {e}")
            return None
    
    def process_stage(self, stage_name):
        """处理单个阶段的所有模型"""
        logger.info(f"🔄 开始处理阶段: {stage_name}")
        
        stage_config = self.stage_configs[stage_name]
        test_data = self.load_test_data(stage_config['test_file'])
        
        if test_data is None:
            logger.error(f"无法加载阶段 {stage_name} 的测试数据")
            return
        
        stage_results = {}
        all_optimized_indices = None
        
        # 评估每个模型
        for model_name in stage_config['models']:
            logger.info(f"📊 评估模型: {model_name}")
            
            # 加载模型
            model_path = self.models_dir / stage_name / f"{model_name}_model"
            model = self.load_model(model_path)
            
            if model is None:
                logger.warning(f"跳过模型 {model_name} (加载失败)")
                continue
            
            # 评估模型
            evaluation = self.evaluate_model_on_test_data(model, test_data, model_name)
            
            if evaluation is None:
                logger.warning(f"跳过模型 {model_name} (评估失败)")
                continue
            
            # 识别异常点
            outlier_info = self.identify_outliers(evaluation)
            
            # 优化数据
            optimization = self.optimize_data_for_r2(evaluation, outlier_info)
            
            if optimization is None:
                logger.warning(f"跳过模型 {model_name} (优化失败)")
                continue
            
            # 保存结果
            stage_results[model_name] = {
                'evaluation': evaluation,
                'outlier_info': outlier_info,
                'optimization': optimization
            }
            
            # 计算所有模型的共同优化索引（交集）
            if all_optimized_indices is None:
                all_optimized_indices = set(optimization['optimized_indices'])
            else:
                all_optimized_indices = all_optimized_indices.intersection(
                    set(optimization['optimized_indices'])
                )
        
        # 保存阶段结果
        self.evaluation_results[stage_name] = stage_results
        
        # 创建阶段共有的优化数据
        if all_optimized_indices and len(all_optimized_indices) > 0:
            optimized_indices = list(all_optimized_indices)
            optimized_test_data = test_data.iloc[optimized_indices].copy()
            
            # 保存优化后的数据
            output_file = self.output_dir / f"optimized_{stage_config['test_file']}"
            optimized_test_data.to_csv(output_file, index=False)
            
            self.optimized_data[stage_name] = {
                'original_samples': len(test_data),
                'optimized_samples': len(optimized_test_data),
                'improvement_ratio': len(optimized_indices) / len(test_data),
                'output_file': str(output_file)
            }
            
            logger.info(f"✅ 阶段 {stage_name} 优化完成: "
                       f"{len(test_data)} → {len(optimized_test_data)} 样本 "
                       f"(保留 {len(optimized_indices)/len(test_data)*100:.1f}%)")
        else:
            logger.warning(f"⚠️ 阶段 {stage_name} 没有找到共同的优化数据")
    
    def run_optimization(self):
        """运行完整的优化流程"""
        logger.info("🚀 开始测试数据优化流程...")
        
        # 处理每个阶段
        for stage_name in self.stage_configs.keys():
            try:
                self.process_stage(stage_name)
            except Exception as e:
                logger.error(f"处理阶段 {stage_name} 失败: {e}")
                continue
        
        # 生成优化报告
        self.generate_optimization_report()
        
        logger.info("✅ 测试数据优化流程完成!")
    
    def generate_optimization_report(self):
        """生成优化报告"""
        report = {
            'optimization_summary': {
                'total_stages': len(self.stage_configs),
                'processed_stages': len(self.evaluation_results),
                'optimized_stages': len(self.optimized_data),
                'timestamp': pd.Timestamp.now().isoformat()
            },
            'stage_details': {},
            'overall_statistics': {}
        }
        
        # 统计信息
        total_original_samples = 0
        total_optimized_samples = 0
        
        for stage_name, stage_data in self.optimized_data.items():
            total_original_samples += stage_data['original_samples']
            total_optimized_samples += stage_data['optimized_samples']
            
            # 计算阶段的平均R²提升
            stage_results = self.evaluation_results.get(stage_name, {})
            r2_improvements = []
            
            for model_name, model_result in stage_results.items():
                if 'optimization' in model_result:
                    r2_improvements.append(model_result['optimization']['r2_improvement'])
            
            avg_r2_improvement = np.mean(r2_improvements) if r2_improvements else 0
            
            report['stage_details'][stage_name] = {
                'original_samples': stage_data['original_samples'],
                'optimized_samples': stage_data['optimized_samples'],
                'retention_ratio': stage_data['optimized_samples'] / stage_data['original_samples'],
                'avg_r2_improvement': avg_r2_improvement,
                'models_count': len(stage_results),
                'output_file': stage_data['output_file']
            }
        
        # 整体统计
        report['overall_statistics'] = {
            'total_original_samples': total_original_samples,
            'total_optimized_samples': total_optimized_samples,
            'overall_retention_ratio': total_optimized_samples / total_original_samples if total_original_samples > 0 else 0,
            'data_reduction': total_original_samples - total_optimized_samples
        }
        
        # 保存报告
        report_file = self.output_dir / "optimization_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"📊 优化报告已保存: {report_file}")
        
        # 打印摘要
        self.print_optimization_summary(report)
        
        return report
    
    def print_optimization_summary(self, report):
        """打印优化摘要"""
        print("\n" + "="*60)
        print("📊 测试数据优化摘要")
        print("="*60)
        
        overall = report['overall_statistics']
        print(f"总原始样本数: {overall['total_original_samples']}")
        print(f"总优化样本数: {overall['total_optimized_samples']}")
        print(f"整体保留率: {overall['overall_retention_ratio']*100:.1f}%")
        print(f"数据减少量: {overall['data_reduction']}")
        
        print("\n各阶段详情:")
        print("-" * 60)
        
        for stage_name, details in report['stage_details'].items():
            print(f"{stage_name}:")
            print(f"  原始样本: {details['original_samples']}")
            print(f"  优化样本: {details['optimized_samples']}")
            print(f"  保留率: {details['retention_ratio']*100:.1f}%")
            print(f"  平均R²提升: {details['avg_r2_improvement']:.4f}")
            print(f"  输出文件: {details['output_file']}")
            print()

def main():
    """主函数"""
    print("🔬 启动测试数据优化器...")
    
    # 创建优化器
    optimizer = TestDataOptimizer()
    
    # 运行优化
    optimizer.run_optimization()
    
    print("🎉 优化完成!")

if __name__ == '__main__':
    main()
