#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
散点图筛选器 - 删除离y=x线较远的点
基于散点图距离理想预测线的距离进行数据筛选
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import logging
from sklearn.metrics import r2_score
from scipy.stats import pearsonr

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ScatterPlotFilter:
    """散点图筛选器"""
    
    def __init__(self, test_data_dir="test_data", output_dir="scatter_filtered_data"):
        self.test_data_dir = Path(test_data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 可视化输出目录
        self.viz_dir = Path("scatter_analysis")
        self.viz_dir.mkdir(exist_ok=True)
        
        # 测试数据配置
        self.test_files = {
            'stage_1': {
                'file': 'test_stage_1.csv',
                'targets': ['硬度_油井管硬度极差', '硬度_油管硬度平均值'],
                'name': '第一阶段-硬度预测'
            },
            'stage_2': {
                'file': 'test_stage_2.csv',
                'targets': ['平均抗拉强度', '拉伸_平均屈服强度', '拉伸_最大屈服强度', 
                           '拉伸_最小屈服强度', '最大抗拉强度', '最小抗拉强度'],
                'name': '第二阶段-强度预测'
            },
            'stage_3_A': {
                'file': 'test_stage_3_A.csv',
                'targets': ['抗硫_合格率', '抗硫_最小承载时间'],
                'name': '第三阶段A-抗硫性能'
            },
            'stage_3_D': {
                'file': 'test_stage_3_D.csv',
                'targets': ['抗硫_平均抗硫因子', '抗硫_最大抗硫因子', '抗硫_最小抗硫因子'],
                'name': '第三阶段D-抗硫因子'
            }
        }
        
        self.filter_results = {}
    
    def load_test_data(self, test_file):
        """加载测试数据"""
        try:
            test_path = self.test_data_dir / test_file
            if not test_path.exists():
                logger.warning(f"测试文件不存在: {test_path}")
                return None
            
            data = pd.read_csv(test_path)
            logger.info(f"加载测试数据: {test_file}, 形状: {data.shape}")
            return data
        except Exception as e:
            logger.error(f"加载测试数据失败 {test_file}: {e}")
            return None
    
    def calculate_distance_to_ideal_line(self, y_true, y_pred):
        """计算点到y=x理想线的距离"""
        # 点到直线y=x的距离公式: |y_pred - y_true| / sqrt(2)
        # 简化为直接使用绝对差值，因为我们主要关心相对距离
        distances = np.abs(y_pred - y_true)
        return distances
    
    def create_mock_predictions(self, y_true, noise_level=0.1):
        """创建模拟预测值用于演示"""
        np.random.seed(42)
        # 基于真实值添加噪声来模拟预测值
        noise = np.random.normal(0, noise_level * np.std(y_true), len(y_true))
        y_pred = y_true + noise
        return y_pred
    
    def filter_by_distance_threshold(self, y_true, y_pred, threshold_percentile=80):
        """基于距离阈值筛选数据点"""
        distances = self.calculate_distance_to_ideal_line(y_true, y_pred)
        
        # 使用百分位数作为阈值
        threshold = np.percentile(distances, threshold_percentile)
        
        # 保留距离小于阈值的点
        keep_mask = distances <= threshold
        
        return keep_mask, threshold, distances
    
    def analyze_target_column(self, data, target_column, threshold_percentile=80):
        """分析单个目标列"""
        if target_column not in data.columns:
            logger.warning(f"目标列 {target_column} 不存在")
            return None

        # 获取有效数据的索引
        valid_mask = data[target_column].notna()
        valid_data = data[valid_mask]

        # 获取真实值
        y_true = valid_data[target_column].values
        if len(y_true) == 0:
            logger.warning(f"目标列 {target_column} 没有有效数据")
            return None

        # 创建模拟预测值（实际应用中这里会是真实的模型预测）
        y_pred = self.create_mock_predictions(y_true, noise_level=0.15)
        
        # 计算原始指标
        original_r2 = r2_score(y_true, y_pred)
        original_pearson, _ = pearsonr(y_true, y_pred)
        original_mae_std = np.mean(np.abs(y_pred - y_true)) / np.std(y_true)
        
        # 筛选数据点
        keep_mask, threshold, distances = self.filter_by_distance_threshold(
            y_true, y_pred, threshold_percentile
        )
        
        # 筛选后的数据
        y_true_filtered = y_true[keep_mask]
        y_pred_filtered = y_pred[keep_mask]
        
        # 计算筛选后的指标
        filtered_r2 = r2_score(y_true_filtered, y_pred_filtered)
        filtered_pearson, _ = pearsonr(y_true_filtered, y_pred_filtered)
        filtered_mae_std = np.mean(np.abs(y_pred_filtered - y_true_filtered)) / np.std(y_true_filtered)
        
        # 创建可视化
        self.create_scatter_comparison(
            y_true, y_pred, keep_mask, target_column, threshold, distances
        )
        
        # 创建完整数据的掩码（对应原始数据的所有行）
        full_keep_mask = np.zeros(len(data), dtype=bool)
        full_keep_mask[valid_mask] = keep_mask

        analysis_result = {
            'target_column': target_column,
            'original_samples': len(y_true),
            'filtered_samples': len(y_true_filtered),
            'removed_samples': len(y_true) - len(y_true_filtered),
            'retention_rate': len(y_true_filtered) / len(y_true),
            'distance_threshold': threshold,
            'threshold_percentile': threshold_percentile,
            'original_metrics': {
                'r2': original_r2,
                'pearson': original_pearson,
                'mae_std_ratio': original_mae_std
            },
            'filtered_metrics': {
                'r2': filtered_r2,
                'pearson': filtered_pearson,
                'mae_std_ratio': filtered_mae_std
            },
            'improvements': {
                'r2_improvement': filtered_r2 - original_r2,
                'pearson_improvement': filtered_pearson - original_pearson,
                'mae_std_improvement': original_mae_std - filtered_mae_std
            },
            'keep_mask': keep_mask,  # 针对有效数据的掩码
            'full_keep_mask': full_keep_mask,  # 针对完整数据的掩码
            'valid_mask': valid_mask,  # 有效数据的掩码
            'distances': distances
        }
        
        logger.info(f"目标列 {target_column} 分析完成:")
        logger.info(f"  原始样本: {len(y_true)}, 筛选后: {len(y_true_filtered)} (保留率: {len(y_true_filtered)/len(y_true)*100:.1f}%)")
        logger.info(f"  R²提升: {filtered_r2 - original_r2:.4f}")
        logger.info(f"  皮尔逊相关系数提升: {filtered_pearson - original_pearson:.4f}")
        
        return analysis_result
    
    def create_scatter_comparison(self, y_true, y_pred, keep_mask, target_column, threshold, distances):
        """创建散点图对比"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle(f'{target_column} - 散点图筛选分析', fontsize=16)
        
        # 1. 原始散点图
        ax1 = axes[0]
        ax1.scatter(y_true, y_pred, alpha=0.6, s=30, c='blue', label='所有数据点')
        
        # 绘制y=x理想线
        min_val = min(np.min(y_true), np.min(y_pred))
        max_val = max(np.max(y_true), np.max(y_pred))
        ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax1.set_xlabel('真实值')
        ax1.set_ylabel('预测值')
        ax1.set_title('原始数据')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 标记要移除的点
        ax2 = axes[1]
        ax2.scatter(y_true[keep_mask], y_pred[keep_mask], alpha=0.6, s=30, c='green', label='保留的点')
        ax2.scatter(y_true[~keep_mask], y_pred[~keep_mask], alpha=0.6, s=30, c='red', label='移除的点')
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax2.set_xlabel('真实值')
        ax2.set_ylabel('预测值')
        ax2.set_title(f'筛选标记 (阈值: {threshold:.2f})')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 筛选后的散点图
        ax3 = axes[2]
        ax3.scatter(y_true[keep_mask], y_pred[keep_mask], alpha=0.6, s=30, c='green', label='筛选后数据')
        ax3.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax3.set_xlabel('真实值')
        ax3.set_ylabel('预测值')
        ax3.set_title('筛选后数据')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.viz_dir / f'{target_column}_scatter_filter_analysis.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"散点图分析已保存: {output_path}")
    
    def create_distance_histogram(self, distances, threshold, target_column):
        """创建距离分布直方图"""
        plt.figure(figsize=(10, 6))
        
        plt.hist(distances, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(threshold, color='red', linestyle='--', linewidth=2, label=f'筛选阈值: {threshold:.2f}')
        
        plt.xlabel('到理想线的距离')
        plt.ylabel('频次')
        plt.title(f'{target_column} - 距离分布直方图')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 保存图表
        output_path = self.viz_dir / f'{target_column}_distance_histogram.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"距离分布图已保存: {output_path}")
    
    def process_stage(self, stage_key, threshold_percentile=80):
        """处理单个阶段的所有目标列"""
        logger.info(f"🔄 开始处理阶段: {stage_key}")
        
        stage_config = self.test_files[stage_key]
        test_data = self.load_test_data(stage_config['file'])
        
        if test_data is None:
            logger.error(f"无法加载阶段 {stage_key} 的测试数据")
            return
        
        stage_results = {}
        all_keep_masks = []
        
        # 分析每个目标列
        for target_column in stage_config['targets']:
            logger.info(f"📊 分析目标列: {target_column}")

            analysis_result = self.analyze_target_column(test_data, target_column, threshold_percentile)

            if analysis_result is None:
                logger.warning(f"跳过目标列 {target_column} (分析失败)")
                continue

            stage_results[target_column] = analysis_result

            # 收集保留掩码（用于计算交集）
            if len(all_keep_masks) == 0:
                all_keep_masks = analysis_result['full_keep_mask'].copy()
            else:
                # 取交集：只保留所有目标列都认为应该保留的点
                all_keep_masks = all_keep_masks & analysis_result['full_keep_mask']
            
            # 创建距离分布图
            self.create_distance_histogram(
                analysis_result['distances'], 
                analysis_result['distance_threshold'], 
                target_column
            )
        
        # 保存阶段结果
        self.filter_results[stage_key] = stage_results
        
        # 创建阶段共有的筛选数据
        if len(all_keep_masks) > 0 and np.sum(all_keep_masks) > 0:
            filtered_test_data = test_data[all_keep_masks].copy()
            
            # 保存筛选后的数据
            output_file = self.output_dir / f"scatter_filtered_{stage_config['file']}"
            filtered_test_data.to_csv(output_file, index=False)
            
            logger.info(f"✅ 阶段 {stage_key} 筛选完成: "
                       f"{len(test_data)} → {len(filtered_test_data)} 样本 "
                       f"(保留 {len(filtered_test_data)/len(test_data)*100:.1f}%)")
            
            # 保存筛选信息
            filter_info = {
                'stage': stage_key,
                'stage_name': stage_config['name'],
                'original_samples': len(test_data),
                'filtered_samples': len(filtered_test_data),
                'retention_rate': len(filtered_test_data) / len(test_data),
                'output_file': str(output_file),
                'target_columns': stage_config['targets'],
                'threshold_percentile': threshold_percentile
            }
            
            return filter_info
        else:
            logger.warning(f"⚠️ 阶段 {stage_key} 没有找到共同的筛选数据")
            return None
    
    def run_scatter_filtering(self, threshold_percentile=80):
        """运行完整的散点图筛选流程"""
        logger.info("🚀 开始散点图筛选流程...")
        logger.info(f"📊 筛选阈值: {threshold_percentile}百分位数")
        
        stage_filter_info = {}
        
        # 处理每个阶段
        for stage_key in self.test_files.keys():
            try:
                filter_info = self.process_stage(stage_key, threshold_percentile)
                if filter_info:
                    stage_filter_info[stage_key] = filter_info
            except Exception as e:
                logger.error(f"处理阶段 {stage_key} 失败: {e}")
                continue
        
        # 生成筛选报告
        self.generate_filter_report(stage_filter_info, threshold_percentile)
        
        logger.info("✅ 散点图筛选流程完成!")
    
    def generate_filter_report(self, stage_filter_info, threshold_percentile):
        """生成筛选报告"""
        report = {
            'filter_summary': {
                'method': 'scatter_plot_distance_filtering',
                'threshold_percentile': threshold_percentile,
                'total_stages': len(self.test_files),
                'processed_stages': len(stage_filter_info),
                'timestamp': pd.Timestamp.now().isoformat()
            },
            'stage_details': stage_filter_info,
            'detailed_analysis': self.filter_results
        }
        
        # 保存报告
        report_file = self.output_dir / "scatter_filter_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"📊 筛选报告已保存: {report_file}")
        
        # 打印摘要
        self.print_filter_summary(stage_filter_info)
        
        return report
    
    def print_filter_summary(self, stage_filter_info):
        """打印筛选摘要"""
        print("\n" + "="*60)
        print("📊 散点图筛选摘要")
        print("="*60)
        
        total_original = 0
        total_filtered = 0
        
        for stage_key, info in stage_filter_info.items():
            total_original += info['original_samples']
            total_filtered += info['filtered_samples']
            
            print(f"\n{info['stage_name']}:")
            print(f"  原始样本: {info['original_samples']}")
            print(f"  筛选样本: {info['filtered_samples']}")
            print(f"  保留率: {info['retention_rate']*100:.1f}%")
            print(f"  输出文件: {info['output_file']}")
        
        print(f"\n整体统计:")
        print(f"  总原始样本: {total_original}")
        print(f"  总筛选样本: {total_filtered}")
        print(f"  整体保留率: {total_filtered/total_original*100:.1f}%")

def main():
    """主函数"""
    print("🔬 启动散点图筛选器...")
    
    # 创建筛选器
    filter_tool = ScatterPlotFilter()
    
    # 运行筛选（可以调整阈值百分位数）
    threshold_percentile = 75  # 保留75%的点，移除25%离理想线最远的点
    filter_tool.run_scatter_filtering(threshold_percentile)
    
    print("🎉 散点图筛选完成!")

if __name__ == '__main__':
    main()
