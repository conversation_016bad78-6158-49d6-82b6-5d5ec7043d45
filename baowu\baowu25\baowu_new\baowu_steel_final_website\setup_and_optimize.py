#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装AutoGluon并运行真实模型优化
"""

import subprocess
import sys
import os
from pathlib import Path

def install_autogluon():
    """安装AutoGluon"""
    print("🔄 正在安装AutoGluon...")
    try:
        # 安装AutoGluon
        subprocess.check_call([sys.executable, "-m", "pip", "install", "autogluon"])
        print("✅ AutoGluon安装成功!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ AutoGluon安装失败: {e}")
        return False

def check_autogluon():
    """检查AutoGluon是否可用"""
    try:
        import autogluon.tabular
        print("✅ AutoGluon已安装并可用")
        return True
    except ImportError:
        print("⚠️ AutoGluon未安装")
        return False

def run_real_optimization():
    """运行真实模型优化"""
    print("🚀 启动真实模型优化...")
    try:
        # 导入并运行真实模型优化器
        from real_model_optimizer import RealModelOptimizer
        
        optimizer = RealModelOptimizer()
        optimizer.run_optimization()
        
        print("✅ 真实模型优化完成!")
        return True
    except Exception as e:
        print(f"❌ 真实模型优化失败: {e}")
        return False

def main():
    """主函数"""
    print("🔬 测试数据优化设置程序")
    print("="*50)
    
    # 检查AutoGluon
    if not check_autogluon():
        print("需要安装AutoGluon...")
        if not install_autogluon():
            print("❌ 无法安装AutoGluon，退出程序")
            return
    
    # 运行真实模型优化
    if not run_real_optimization():
        print("❌ 真实模型优化失败")
        return
    
    print("🎉 所有任务完成!")

if __name__ == '__main__':
    main()
