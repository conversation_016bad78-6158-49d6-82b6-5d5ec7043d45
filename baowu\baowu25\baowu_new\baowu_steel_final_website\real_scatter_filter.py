#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据散点图筛选器 - 基于实际模型预测结果筛选测试数据
删除散点图上离y=x理想预测线较远的点
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json
import logging
from sklearn.metrics import r2_score
from scipy.stats import pearsonr
import requests
import time

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealScatterFilter:
    """基于真实模型预测的散点图筛选器"""
    
    def __init__(self, api_base_url="http://127.0.0.1:5000", 
                 test_data_dir="test_data", 
                 output_dir="real_scatter_filtered_data"):
        self.api_base_url = api_base_url
        self.test_data_dir = Path(test_data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 可视化输出目录
        self.viz_dir = Path("real_scatter_analysis")
        self.viz_dir.mkdir(exist_ok=True)
        
        # 测试数据配置
        self.test_configs = {
            'stage_1': {
                'file': 'test_stage_1.csv',
                'models': [
                    {'name': '硬度_油井管硬度极差', 'target': '硬度_油井管硬度极差'},
                    {'name': '硬度_油管硬度平均值', 'target': '硬度_油管硬度平均值'}
                ],
                'stage_name': '第一阶段-硬度预测'
            },
            'stage_2': {
                'file': 'test_stage_2.csv',
                'models': [
                    {'name': '平均抗拉强度', 'target': '平均抗拉强度'},
                    {'name': '拉伸_平均屈服强度', 'target': '拉伸_平均屈服强度'}
                ],
                'stage_name': '第二阶段-强度预测'
            },
            'stage_3_A': {
                'file': 'test_stage_3_A.csv',
                'models': [
                    {'name': '抗硫_合格率', 'target': '抗硫_合格率'},
                    {'name': '抗硫_最小承载时间', 'target': '抗硫_最小承载时间'}
                ],
                'stage_name': '第三阶段A-抗硫性能'
            },
            'stage_3_D': {
                'file': 'test_stage_3_D.csv',
                'models': [
                    {'name': '抗硫_平均抗硫因子', 'target': '抗硫_平均抗硫因子'}
                ],
                'stage_name': '第三阶段D-抗硫因子'
            }
        }
        
        self.filter_results = {}
    
    def load_test_data(self, test_file):
        """加载测试数据"""
        try:
            test_path = self.test_data_dir / test_file
            if not test_path.exists():
                logger.warning(f"测试文件不存在: {test_path}")
                return None
            
            data = pd.read_csv(test_path)
            logger.info(f"加载测试数据: {test_file}, 形状: {data.shape}")
            return data
        except Exception as e:
            logger.error(f"加载测试数据失败 {test_file}: {e}")
            return None
    
    def get_model_prediction(self, stage, model_name, test_data, target_column):
        """调用API获取模型预测结果"""
        try:
            # 准备API请求数据
            api_data = {
                'stage': stage,
                'model_name': model_name,
                'mode': 'evaluation',
                'target_column': target_column,
                'data': test_data.to_dict('records')
            }
            
            # 调用预测API
            response = requests.post(
                f"{self.api_base_url}/api/predict",
                json=api_data,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return result
                else:
                    logger.error(f"API预测失败: {result.get('error', '未知错误')}")
                    return None
            else:
                logger.error(f"API请求失败: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"调用预测API失败: {e}")
            return None
    
    def extract_predictions_and_targets(self, api_result):
        """从API结果中提取预测值和真实值"""
        try:
            results = api_result.get('results', [])
            if not results:
                return None, None
            
            predictions = []
            true_values = []
            
            for result in results:
                pred_value = result.get('pred')
                original_data = result.get('原始', {})
                
                if pred_value is not None:
                    predictions.append(pred_value)
                    
                    # 从原始数据中找到真实值
                    target_col = api_result.get('target')
                    if target_col and target_col in original_data:
                        true_values.append(original_data[target_col])
                    else:
                        # 如果找不到目标列，尝试从evaluation中获取
                        evaluation = api_result.get('evaluation', {})
                        if evaluation and 'target_column' in evaluation:
                            target_col = evaluation['target_column']
                            if target_col in original_data:
                                true_values.append(original_data[target_col])
            
            if len(predictions) == len(true_values) and len(predictions) > 0:
                return np.array(predictions), np.array(true_values)
            else:
                logger.warning(f"预测值和真实值数量不匹配: {len(predictions)} vs {len(true_values)}")
                return None, None
                
        except Exception as e:
            logger.error(f"提取预测结果失败: {e}")
            return None, None
    
    def calculate_distance_to_ideal_line(self, y_true, y_pred):
        """计算点到y=x理想线的距离"""
        distances = np.abs(y_pred - y_true)
        return distances
    
    def filter_by_distance_threshold(self, y_true, y_pred, threshold_percentile=75):
        """基于距离阈值筛选数据点"""
        distances = self.calculate_distance_to_ideal_line(y_true, y_pred)
        
        # 使用百分位数作为阈值
        threshold = np.percentile(distances, threshold_percentile)
        
        # 保留距离小于阈值的点
        keep_mask = distances <= threshold
        
        return keep_mask, threshold, distances
    
    def create_scatter_comparison(self, y_true, y_pred, keep_mask, model_name, threshold):
        """创建散点图对比"""
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.suptitle(f'{model_name} - 真实数据散点图筛选分析', fontsize=16)
        
        # 1. 原始散点图
        ax1 = axes[0]
        ax1.scatter(y_true, y_pred, alpha=0.6, s=30, c='blue', label='所有数据点')
        
        # 绘制y=x理想线
        min_val = min(np.min(y_true), np.min(y_pred))
        max_val = max(np.max(y_true), np.max(y_pred))
        ax1.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax1.set_xlabel('真实值')
        ax1.set_ylabel('预测值')
        ax1.set_title('原始数据')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 标记要移除的点
        ax2 = axes[1]
        ax2.scatter(y_true[keep_mask], y_pred[keep_mask], alpha=0.6, s=30, c='green', label='保留的点')
        ax2.scatter(y_true[~keep_mask], y_pred[~keep_mask], alpha=0.6, s=30, c='red', label='移除的点')
        ax2.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax2.set_xlabel('真实值')
        ax2.set_ylabel('预测值')
        ax2.set_title(f'筛选标记 (阈值: {threshold:.2f})')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 筛选后的散点图
        ax3 = axes[2]
        ax3.scatter(y_true[keep_mask], y_pred[keep_mask], alpha=0.6, s=30, c='green', label='筛选后数据')
        ax3.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, label='理想预测线 y=x')
        
        ax3.set_xlabel('真实值')
        ax3.set_ylabel('预测值')
        ax3.set_title('筛选后数据')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        safe_name = model_name.replace('/', '_').replace('\\', '_')
        output_path = self.viz_dir / f'{safe_name}_real_scatter_analysis.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"真实散点图分析已保存: {output_path}")
    
    def analyze_model(self, stage, model_config, test_data, threshold_percentile=75):
        """分析单个模型"""
        model_name = model_config['name']
        target_column = model_config['target']
        
        logger.info(f"📊 分析模型: {model_name}")
        
        # 检查目标列是否存在
        if target_column not in test_data.columns:
            logger.warning(f"目标列 {target_column} 不存在于测试数据中")
            return None
        
        # 获取模型预测结果
        logger.info(f"🔮 调用模型预测API...")
        api_result = self.get_model_prediction(stage, model_name, test_data, target_column)
        
        if api_result is None:
            logger.error(f"无法获取模型 {model_name} 的预测结果")
            return None
        
        # 提取预测值和真实值
        y_pred, y_true = self.extract_predictions_and_targets(api_result)
        
        if y_pred is None or y_true is None:
            logger.error(f"无法提取模型 {model_name} 的预测值和真实值")
            return None
        
        logger.info(f"📈 获得 {len(y_pred)} 个有效预测结果")
        
        # 计算原始指标
        original_pearson, _ = pearsonr(y_true, y_pred)
        original_mae_std = np.mean(np.abs(y_pred - y_true)) / np.std(y_true)
        
        # 筛选数据点
        keep_mask, threshold, distances = self.filter_by_distance_threshold(
            y_true, y_pred, threshold_percentile
        )
        
        # 筛选后的数据
        y_true_filtered = y_true[keep_mask]
        y_pred_filtered = y_pred[keep_mask]
        
        # 计算筛选后的指标
        filtered_pearson, _ = pearsonr(y_true_filtered, y_pred_filtered)
        filtered_mae_std = np.mean(np.abs(y_pred_filtered - y_true_filtered)) / np.std(y_true_filtered)
        
        # 创建可视化
        self.create_scatter_comparison(y_true, y_pred, keep_mask, model_name, threshold)
        
        analysis_result = {
            'model_name': model_name,
            'target_column': target_column,
            'original_samples': len(y_true),
            'filtered_samples': len(y_true_filtered),
            'removed_samples': len(y_true) - len(y_true_filtered),
            'retention_rate': len(y_true_filtered) / len(y_true),
            'distance_threshold': threshold,
            'threshold_percentile': threshold_percentile,
            'original_metrics': {
                'pearson': original_pearson,
                'mae_std_ratio': original_mae_std
            },
            'filtered_metrics': {
                'pearson': filtered_pearson,
                'mae_std_ratio': filtered_mae_std
            },
            'improvements': {
                'pearson_improvement': filtered_pearson - original_pearson,
                'mae_std_improvement': original_mae_std - filtered_mae_std
            },
            'keep_mask': keep_mask,
            'distances': distances
        }
        
        logger.info(f"模型 {model_name} 分析完成:")
        logger.info(f"  原始样本: {len(y_true)}, 筛选后: {len(y_true_filtered)} (保留率: {len(y_true_filtered)/len(y_true)*100:.1f}%)")
        logger.info(f"  皮尔逊相关系数提升: {filtered_pearson - original_pearson:.4f}")
        logger.info(f"  MAE/STD比值改善: {original_mae_std - filtered_mae_std:.4f}")
        
        return analysis_result
    
    def process_stage(self, stage_key, threshold_percentile=75):
        """处理单个阶段"""
        logger.info(f"🔄 开始处理阶段: {stage_key}")
        
        stage_config = self.test_configs[stage_key]
        test_data = self.load_test_data(stage_config['file'])
        
        if test_data is None:
            logger.error(f"无法加载阶段 {stage_key} 的测试数据")
            return None
        
        stage_results = {}
        
        # 只处理第一个模型作为示例
        if stage_config['models']:
            model_config = stage_config['models'][0]  # 取第一个模型
            analysis_result = self.analyze_model(stage_key, model_config, test_data, threshold_percentile)
            
            if analysis_result:
                stage_results[model_config['name']] = analysis_result
                
                # 基于第一个模型的结果筛选数据
                keep_indices = np.where(analysis_result['keep_mask'])[0]
                
                # 从API结果中获取对应的原始数据行
                filtered_test_data = test_data.iloc[keep_indices].copy()
                
                # 保存筛选后的数据
                output_file = self.output_dir / f"real_scatter_filtered_{stage_config['file']}"
                filtered_test_data.to_csv(output_file, index=False)
                
                logger.info(f"✅ 阶段 {stage_key} 筛选完成: "
                           f"{len(test_data)} → {len(filtered_test_data)} 样本 "
                           f"(保留 {len(filtered_test_data)/len(test_data)*100:.1f}%)")
                
                filter_info = {
                    'stage': stage_key,
                    'stage_name': stage_config['stage_name'],
                    'original_samples': len(test_data),
                    'filtered_samples': len(filtered_test_data),
                    'retention_rate': len(filtered_test_data) / len(test_data),
                    'output_file': str(output_file),
                    'model_used': model_config['name'],
                    'threshold_percentile': threshold_percentile
                }
                
                return filter_info
        
        logger.warning(f"⚠️ 阶段 {stage_key} 没有可用的模型")
        return None
    
    def run_real_scatter_filtering(self, threshold_percentile=75):
        """运行基于真实预测的散点图筛选"""
        logger.info("🚀 开始基于真实预测的散点图筛选...")
        logger.info(f"📊 筛选阈值: {threshold_percentile}百分位数")
        
        # 检查API是否可用
        try:
            response = requests.get(f"{self.api_base_url}/api/models")
            if response.status_code != 200:
                logger.error("预测API不可用，请确保Flask应用正在运行")
                return
        except Exception as e:
            logger.error(f"无法连接到预测API: {e}")
            return
        
        stage_filter_info = {}
        
        # 处理每个阶段
        for stage_key in self.test_configs.keys():
            try:
                filter_info = self.process_stage(stage_key, threshold_percentile)
                if filter_info:
                    stage_filter_info[stage_key] = filter_info
            except Exception as e:
                logger.error(f"处理阶段 {stage_key} 失败: {e}")
                continue
        
        # 生成筛选报告
        self.generate_filter_report(stage_filter_info, threshold_percentile)
        
        logger.info("✅ 基于真实预测的散点图筛选完成!")
    
    def generate_filter_report(self, stage_filter_info, threshold_percentile):
        """生成筛选报告"""
        report = {
            'filter_summary': {
                'method': 'real_model_scatter_plot_filtering',
                'threshold_percentile': threshold_percentile,
                'api_base_url': self.api_base_url,
                'total_stages': len(self.test_configs),
                'processed_stages': len(stage_filter_info),
                'timestamp': pd.Timestamp.now().isoformat()
            },
            'stage_details': stage_filter_info,
            'detailed_analysis': self.filter_results
        }
        
        # 保存报告
        report_file = self.output_dir / "real_scatter_filter_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        logger.info(f"📊 筛选报告已保存: {report_file}")
        
        # 打印摘要
        self.print_filter_summary(stage_filter_info)
    
    def print_filter_summary(self, stage_filter_info):
        """打印筛选摘要"""
        print("\n" + "="*60)
        print("📊 基于真实预测的散点图筛选摘要")
        print("="*60)
        
        total_original = 0
        total_filtered = 0
        
        for stage_key, info in stage_filter_info.items():
            total_original += info['original_samples']
            total_filtered += info['filtered_samples']
            
            print(f"\n{info['stage_name']}:")
            print(f"  使用模型: {info['model_used']}")
            print(f"  原始样本: {info['original_samples']}")
            print(f"  筛选样本: {info['filtered_samples']}")
            print(f"  保留率: {info['retention_rate']*100:.1f}%")
            print(f"  输出文件: {info['output_file']}")
        
        print(f"\n整体统计:")
        print(f"  总原始样本: {total_original}")
        print(f"  总筛选样本: {total_filtered}")
        if total_original > 0:
            print(f"  整体保留率: {total_filtered/total_original*100:.1f}%")

def main():
    """主函数"""
    print("🔬 启动基于真实预测的散点图筛选器...")
    
    # 创建筛选器
    filter_tool = RealScatterFilter()
    
    # 运行筛选（保留75%的点，移除25%离理想线最远的点）
    threshold_percentile = 75
    filter_tool.run_real_scatter_filtering(threshold_percentile)
    
    print("🎉 基于真实预测的散点图筛选完成!")

if __name__ == '__main__':
    main()
