#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
耐应力腐蚀油套管智能预测系统 - 服务器端
运行在Linux服务器上，提供API服务
"""

import os
import sys
import json
import logging
from pathlib import Path
import pandas as pd
import numpy as np
from flask import Flask, request, jsonify
from flask_cors import CORS

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 尝试导入AutoGluon
try:
    from autogluon.tabular import TabularPredictor
    AUTOGLUON_AVAILABLE = True
    logger.info("✅ AutoGluon已加载")
except ImportError:
    AUTOGLUON_AVAILABLE = False
    logger.warning("⚠️ AutoGluon未安装，将使用演示模式")

# 创建Flask应用
app = Flask(__name__)
CORS(app)  # 允许跨域请求

# 全局配置
CONFIG = {
    'HOST': '0.0.0.0',  # 监听所有IP
    'PORT': 5000,
    'APP_NAME': '耐应力腐蚀油套管智能预测系统',
    'VERSION': '2.0 服务器版'
}

# 模型缓存
loaded_models = {}

# 模型阶段描述
Des_of_models = {
    "models/stage_1": "硬度相关预测模型",
    "models/stage_2": "拉伸屈服预测模型", 
    "models/stage_3_A": "A法——抗硫预测模型",
    "models/stage_3_D": "D法——抗硫预测模型",
    "models/stage_4": "硬度和拉伸预测抗硫模型"
}

def get_models_root():
    """获取模型根目录"""
    return Path(__file__).parent.parent / 'models'

def scan_model_directory(model_stage_path):
    """扫描模型目录"""
    result = {}
    models_root = get_models_root()
    path = models_root / model_stage_path.replace("models/", "")
    
    if not path.exists():
        logger.warning(f"模型路径不存在: {path}")
        return result
    
    for model_dir in path.iterdir():
        if model_dir.is_dir() and model_dir.name.endswith("_model"):
            model_name = model_dir.name.replace("_model", "")
            result[model_name] = {
                "path": str(model_dir),
                "target": model_name,
                "desc": f"{Des_of_models[model_stage_path]} - 预测 [{model_name}]"
            }
    
    return result

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'ok',
        'message': '服务器运行正常',
        'version': CONFIG['VERSION'],
        'autogluon_available': AUTOGLUON_AVAILABLE
    })

@app.route('/api/models', methods=['GET'])
def get_available_models():
    """获取可用模型列表"""
    try:
        available_models = {}
        
        for stage_path in Des_of_models.keys():
            stage_name = stage_path.split('/')[-1]
            models = scan_model_directory(stage_path)
            if models:
                available_models[stage_name] = models
        
        return jsonify({
            'success': True,
            'models': available_models,
            'message': f'找到 {sum(len(models) for models in available_models.values())} 个可用模型'
        })
    
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/model_info/<stage>/<model_name>', methods=['GET'])
def get_model_info(stage, model_name):
    """获取模型信息"""
    try:
        stage_path = f"models/{stage}"
        models = scan_model_directory(stage_path)
        
        if model_name in models:
            model_info = models[model_name]
            
            # 获取模型特征信息
            if AUTOGLUON_AVAILABLE:
                try:
                    predictor = TabularPredictor.load(model_info['path'])
                    features = predictor.feature_metadata.get_features()
                    model_info['required_features'] = features
                    model_info['feature_count'] = len(features)
                except Exception as e:
                    logger.warning(f"无法加载模型特征信息: {e}")
                    model_info['required_features'] = []
                    model_info['feature_count'] = 0
            else:
                # 演示模式的特征列表
                demo_features = [
                    "C", "Si", "Mn", "P", "S", "Cr", "Ni", "Mo", "Cu", "Al",
                    "温度", "时间", "压力", "冷却速度", "厚度", "直径"
                ]
                model_info['required_features'] = demo_features
                model_info['feature_count'] = len(demo_features)
            
            return jsonify({'success': True, 'model_info': model_info})
        else:
            return jsonify({'success': False, 'error': '模型不存在'}), 404
    
    except Exception as e:
        logger.error(f"获取模型信息失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/predict', methods=['POST'])
def execute_prediction():
    """执行预测"""
    try:
        request_data = request.get_json()
        stage = request_data.get('stage')
        model_name = request_data.get('model_name')
        data = request_data.get('data', [])
        
        if not data:
            return jsonify({'success': False, 'error': '没有提供数据'}), 400
        
        # 转换为DataFrame
        df = pd.DataFrame(data)
        
        if AUTOGLUON_AVAILABLE:
            # 使用真实模型预测
            stage_path = f"models/{stage}"
            models = scan_model_directory(stage_path)
            
            if model_name not in models:
                return jsonify({'success': False, 'error': f'模型 {model_name} 不存在'}), 404
            
            model_path = models[model_name]['path']
            
            # 加载模型
            if stage not in loaded_models:
                loaded_models[stage] = {}
            
            if model_name not in loaded_models[stage]:
                logger.info(f"加载模型: {model_path}")
                loaded_models[stage][model_name] = TabularPredictor.load(model_path)
            
            predictor = loaded_models[stage][model_name]
            
            # 执行预测
            predictions = predictor.predict(df)
            predictions_list = predictions.tolist()
            
        else:
            # 演示模式
            predictions_list = generate_demo_predictions(model_name, len(data))
        
        # 计算统计信息
        predictions_array = np.array(predictions_list)
        stats = {
            'count': len(predictions_list),
            'mean': float(np.mean(predictions_array)),
            'std': float(np.std(predictions_array)),
            'min': float(np.min(predictions_array)),
            'max': float(np.max(predictions_array)),
            'median': float(np.median(predictions_array))
        }
        
        return jsonify({
            'success': True,
            'predictions': predictions_list,
            'statistics': stats,
            'message': f'成功预测 {len(predictions_list)} 个样本'
        })
    
    except Exception as e:
        logger.error(f"预测执行失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/validate_data', methods=['POST'])
def validate_data():
    """验证数据"""
    try:
        request_data = request.get_json()
        stage = request_data.get('stage')
        model_name = request_data.get('model_name')
        data = request_data.get('data', [])
        
        if not data:
            return jsonify({'success': False, 'error': '没有提供数据'}), 400
        
        # 获取模型信息
        stage_path = f"models/{stage}"
        models = scan_model_directory(stage_path)
        
        if model_name not in models:
            return jsonify({'success': False, 'error': f'模型 {model_name} 不存在'}), 404
        
        model_info = models[model_name]
        
        # 获取特征信息
        if AUTOGLUON_AVAILABLE:
            try:
                predictor = TabularPredictor.load(model_info['path'])
                required_features = predictor.feature_metadata.get_features()
            except:
                required_features = []
        else:
            required_features = [
                "C", "Si", "Mn", "P", "S", "Cr", "Ni", "Mo", "Cu", "Al",
                "温度", "时间", "压力", "冷却速度", "厚度", "直径"
            ]
        
        data_columns = list(data[0].keys()) if data else []
        
        # 检查特征匹配
        missing_features = [f for f in required_features if f not in data_columns]
        extra_features = [f for f in data_columns if f not in required_features]
        
        # 数据类型检查
        numeric_errors = []
        for i, row in enumerate(data[:10]):  # 只检查前10行
            for col, value in row.items():
                if col in required_features and value is not None and value != '':
                    try:
                        float(value)
                    except (ValueError, TypeError):
                        numeric_errors.append(f"第{i+1}行，列'{col}': 非数值数据")
        
        is_valid = len(missing_features) == 0 and len(numeric_errors) == 0
        
        return jsonify({
            'success': True,
            'validation_result': {
                'is_valid': is_valid,
                'row_count': len(data),
                'column_count': len(data_columns),
                'required_features': required_features,
                'missing_features': missing_features,
                'extra_features': extra_features,
                'numeric_errors': numeric_errors[:5],  # 只返回前5个错误
                'data_preview': data[:5]  # 返回前5行数据预览
            },
            'message': '数据验证完成' if is_valid else '数据验证发现问题'
        })
    
    except Exception as e:
        logger.error(f"数据验证失败: {e}")
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/guide', methods=['GET'])
def get_guide_info():
    """获取引导信息"""
    return jsonify({
        'success': True,
        'guide_steps': [
            {
                'step': 1,
                'title': '选择预测模型',
                'description': '根据您的预测需求选择合适的机器学习模型',
                'details': [
                    '硬度相关预测模型：用于预测材料硬度值',
                    '拉伸屈服预测模型：用于预测材料拉伸强度',
                    'A法抗硫预测模型：用于预测抗硫化氢腐蚀性能',
                    'D法抗硫预测模型：用于预测抗硫化氢腐蚀性能（D法标准）',
                    '硬度和拉伸预测抗硫模型：综合预测模型'
                ],
                'tips': '选择与您的测试目标最匹配的模型类型'
            },
            {
                'step': 2,
                'title': '准备数据文件',
                'description': '上传包含工艺参数的CSV或Excel文件',
                'details': [
                    '支持的文件格式：CSV (.csv)、Excel (.xlsx, .xls)',
                    '文件大小限制：最大50MB',
                    '数据要求：包含模型所需的特征列',
                    '数据质量：确保数值数据格式正确，避免空值过多'
                ],
                'tips': '建议先使用测试数据进行试用'
            },
            {
                'step': 3,
                'title': '数据验证与预览',
                'description': '系统会自动验证数据与模型的兼容性',
                'details': [
                    '特征匹配检查：验证数据列是否包含模型所需特征',
                    '数据类型检查：确保数值特征格式正确',
                    '缺失值处理：系统会自动处理部分缺失值',
                    '数据预览：查看前10行数据确认格式正确'
                ],
                'tips': '如果验证失败，请检查数据格式和特征名称'
            },
            {
                'step': 4,
                'title': '执行预测分析',
                'description': '使用训练好的机器学习模型进行预测',
                'details': [
                    '自动特征预处理：系统会对高偏度特征进行变换',
                    '批量预测：支持同时预测多个样本',
                    '结果统计：提供预测结果的统计分析',
                    '可视化展示：生成预测结果分布图表'
                ],
                'tips': '预测过程可能需要几分钟，请耐心等待'
            },
            {
                'step': 5,
                'title': '结果导出与报告',
                'description': '下载预测结果和生成专业分析报告',
                'details': [
                    'Excel导出：包含原始数据和预测结果的完整表格',
                    'CSV导出：便于进一步数据处理的格式',
                    '质量评估：基于预测结果的质量等级判断'
                ],
                'tips': '建议保存完整的分析报告用于质量控制记录'
            }
        ],
        'system_info': {
            'name': '基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统',
            'version': CONFIG['VERSION'],
            'developer': '宝武钢铁集团',
            'description': '专业的油套管质量预测平台，基于先进的机器学习算法',
            'autogluon_available': AUTOGLUON_AVAILABLE
        }
    })

def generate_demo_predictions(model_name, count):
    """生成演示预测结果"""
    import random
    
    predictions = []
    for _ in range(count):
        if "硬度" in model_name:
            pred = random.normalvariate(250, 30)
        elif "拉伸" in model_name or "抗拉" in model_name:
            pred = random.normalvariate(450, 50)
        elif "屈服" in model_name:
            pred = random.normalvariate(350, 40)
        elif "抗硫" in model_name:
            pred = random.normalvariate(500, 100)
        elif "合格率" in model_name:
            pred = random.uniform(0.8, 1.0)
        else:
            pred = random.normalvariate(300, 50)
        predictions.append(max(0, pred))
    
    return predictions

if __name__ == '__main__':
    try:
        # 检查模型文件
        models_root = get_models_root()
        if models_root.exists():
            logger.info(f"📁 模型目录: {models_root}")
            model_count = sum(1 for _ in models_root.rglob("*_model"))
            logger.info(f"🤖 找到 {model_count} 个模型文件")
        else:
            logger.warning("⚠️ 模型目录不存在，将使用演示模式")
        
        logger.info(f"🚀 {CONFIG['APP_NAME']} v{CONFIG['VERSION']}")
        logger.info(f"🌐 服务器启动: http://{CONFIG['HOST']}:{CONFIG['PORT']}")
        logger.info("🔧 按 Ctrl+C 停止服务")
        logger.info("=" * 60)
        
        # 启动Flask服务器
        app.run(
            host=CONFIG['HOST'],
            port=CONFIG['PORT'],
            debug=False,
            threaded=True
        )
        
    except KeyboardInterrupt:
        logger.info("\n👋 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务器启动失败: {e}")
        input("按回车键退出...")
