#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析优化后的测试数据
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedDataAnalyzer:
    """优化数据分析器"""
    
    def __init__(self, test_data_dir="test_data", optimized_data_dir="optimized_test_data"):
        self.test_data_dir = Path(test_data_dir)
        self.optimized_data_dir = Path(optimized_data_dir)
        self.analysis_dir = Path("data_analysis")
        self.analysis_dir.mkdir(exist_ok=True)
        
        # 阶段配置
        self.stages = {
            'stage_1': {
                'original': 'test_stage_1.csv',
                'optimized': 'optimized_test_stage_1.csv',
                'name': '第一阶段 - 硬度预测'
            },
            'stage_2': {
                'original': 'test_stage_2.csv',
                'optimized': 'optimized_test_stage_2.csv',
                'name': '第二阶段 - 强度预测'
            },
            'stage_3_A': {
                'original': 'test_stage_3_A.csv',
                'optimized': 'optimized_test_stage_3_A.csv',
                'name': '第三阶段A - 抗硫性能'
            },
            'stage_3_D': {
                'original': 'test_stage_3_D.csv',
                'optimized': 'optimized_test_stage_3_D.csv',
                'name': '第三阶段D - 抗硫因子'
            }
        }
    
    def load_data(self, stage_key):
        """加载原始和优化后的数据"""
        stage_info = self.stages[stage_key]
        
        # 加载原始数据
        original_path = self.test_data_dir / stage_info['original']
        optimized_path = self.optimized_data_dir / stage_info['optimized']
        
        original_data = None
        optimized_data = None
        
        try:
            if original_path.exists():
                original_data = pd.read_csv(original_path)
                print(f"✅ 加载原始数据: {stage_info['original']} ({original_data.shape})")
        except Exception as e:
            print(f"❌ 加载原始数据失败: {e}")
        
        try:
            if optimized_path.exists():
                optimized_data = pd.read_csv(optimized_path)
                print(f"✅ 加载优化数据: {stage_info['optimized']} ({optimized_data.shape})")
        except Exception as e:
            print(f"❌ 加载优化数据失败: {e}")
        
        return original_data, optimized_data
    
    def analyze_data_distribution(self, stage_key):
        """分析数据分布"""
        original_data, optimized_data = self.load_data(stage_key)
        
        if original_data is None or optimized_data is None:
            print(f"⚠️ 跳过 {stage_key} 的分布分析")
            return
        
        stage_info = self.stages[stage_key]
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle(f'{stage_info["name"]} - 数据分布对比', fontsize=16)
        
        # 数据量对比
        axes[0, 0].bar(['原始数据', '优化数据'], 
                      [len(original_data), len(optimized_data)],
                      color=['lightblue', 'lightgreen'])
        axes[0, 0].set_title('数据量对比')
        axes[0, 0].set_ylabel('样本数量')
        
        # 添加数值标签
        for i, v in enumerate([len(original_data), len(optimized_data)]):
            axes[0, 0].text(i, v + 1, str(v), ha='center', va='bottom')
        
        # 特征数量对比
        axes[0, 1].bar(['原始数据', '优化数据'], 
                      [original_data.shape[1], optimized_data.shape[1]],
                      color=['lightcoral', 'lightgreen'])
        axes[0, 1].set_title('特征数量对比')
        axes[0, 1].set_ylabel('特征数量')
        
        # 添加数值标签
        for i, v in enumerate([original_data.shape[1], optimized_data.shape[1]]):
            axes[0, 1].text(i, v + 0.5, str(v), ha='center', va='bottom')
        
        # 数据保留率
        retention_rate = len(optimized_data) / len(original_data) * 100
        axes[1, 0].pie([retention_rate, 100-retention_rate], 
                      labels=[f'保留 ({retention_rate:.1f}%)', f'移除 ({100-retention_rate:.1f}%)'],
                      colors=['lightgreen', 'lightcoral'],
                      autopct='%1.1f%%')
        axes[1, 0].set_title('数据保留率')
        
        # 数值特征分布对比（选择前几个数值特征）
        numeric_cols = original_data.select_dtypes(include=[np.number]).columns[:5]
        if len(numeric_cols) > 0:
            col = numeric_cols[0]
            axes[1, 1].hist(original_data[col].dropna(), alpha=0.7, label='原始数据', bins=20)
            axes[1, 1].hist(optimized_data[col].dropna(), alpha=0.7, label='优化数据', bins=20)
            axes[1, 1].set_title(f'特征分布对比: {col}')
            axes[1, 1].legend()
        else:
            axes[1, 1].text(0.5, 0.5, '无数值特征', ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('特征分布')
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.analysis_dir / f'{stage_key}_distribution_analysis.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 {stage_info['name']} 分布分析完成，保存至: {output_path}")
        
        return {
            'original_samples': len(original_data),
            'optimized_samples': len(optimized_data),
            'retention_rate': retention_rate,
            'original_features': original_data.shape[1],
            'optimized_features': optimized_data.shape[1]
        }
    
    def generate_comparison_report(self):
        """生成对比报告"""
        print("📊 生成数据优化对比报告...")
        
        report = {
            'analysis_summary': {
                'total_stages': len(self.stages),
                'timestamp': pd.Timestamp.now().isoformat()
            },
            'stage_analysis': {}
        }
        
        total_original = 0
        total_optimized = 0
        
        # 分析每个阶段
        for stage_key, stage_info in self.stages.items():
            print(f"\n🔍 分析阶段: {stage_info['name']}")
            
            analysis_result = self.analyze_data_distribution(stage_key)
            
            if analysis_result:
                report['stage_analysis'][stage_key] = {
                    'name': stage_info['name'],
                    **analysis_result
                }
                
                total_original += analysis_result['original_samples']
                total_optimized += analysis_result['optimized_samples']
        
        # 整体统计
        report['overall_statistics'] = {
            'total_original_samples': total_original,
            'total_optimized_samples': total_optimized,
            'overall_retention_rate': (total_optimized / total_original * 100) if total_original > 0 else 0,
            'total_removed_samples': total_original - total_optimized
        }
        
        # 保存报告
        report_path = self.analysis_dir / 'data_optimization_analysis.json'
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📋 对比报告已保存: {report_path}")
        
        # 打印摘要
        self.print_analysis_summary(report)
        
        return report
    
    def print_analysis_summary(self, report):
        """打印分析摘要"""
        print("\n" + "="*60)
        print("📊 数据优化分析摘要")
        print("="*60)
        
        overall = report['overall_statistics']
        print(f"总原始样本数: {overall['total_original_samples']}")
        print(f"总优化样本数: {overall['total_optimized_samples']}")
        print(f"整体保留率: {overall['overall_retention_rate']:.1f}%")
        print(f"移除样本数: {overall['total_removed_samples']}")
        
        print("\n各阶段详情:")
        print("-" * 60)
        
        for stage_key, analysis in report['stage_analysis'].items():
            print(f"{analysis['name']}:")
            print(f"  原始样本: {analysis['original_samples']}")
            print(f"  优化样本: {analysis['optimized_samples']}")
            print(f"  保留率: {analysis['retention_rate']:.1f}%")
            print(f"  特征数: {analysis['original_features']} → {analysis['optimized_features']}")
            print()
    
    def create_summary_visualization(self):
        """创建汇总可视化"""
        print("📈 创建汇总可视化...")
        
        # 收集数据
        stage_names = []
        original_counts = []
        optimized_counts = []
        retention_rates = []
        
        for stage_key, stage_info in self.stages.items():
            original_data, optimized_data = self.load_data(stage_key)
            
            if original_data is not None and optimized_data is not None:
                stage_names.append(stage_info['name'])
                original_counts.append(len(original_data))
                optimized_counts.append(len(optimized_data))
                retention_rates.append(len(optimized_data) / len(original_data) * 100)
        
        if not stage_names:
            print("⚠️ 没有可用数据进行可视化")
            return
        
        # 创建汇总图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('测试数据优化汇总分析', fontsize=16)
        
        # 1. 样本数量对比
        x = np.arange(len(stage_names))
        width = 0.35
        
        axes[0, 0].bar(x - width/2, original_counts, width, label='原始数据', color='lightblue')
        axes[0, 0].bar(x + width/2, optimized_counts, width, label='优化数据', color='lightgreen')
        axes[0, 0].set_xlabel('阶段')
        axes[0, 0].set_ylabel('样本数量')
        axes[0, 0].set_title('各阶段样本数量对比')
        axes[0, 0].set_xticks(x)
        axes[0, 0].set_xticklabels(stage_names, rotation=45, ha='right')
        axes[0, 0].legend()
        
        # 2. 保留率
        colors = ['lightgreen' if rate >= 50 else 'orange' if rate >= 30 else 'lightcoral' for rate in retention_rates]
        bars = axes[0, 1].bar(stage_names, retention_rates, color=colors)
        axes[0, 1].set_ylabel('保留率 (%)')
        axes[0, 1].set_title('各阶段数据保留率')
        axes[0, 1].set_xticklabels(stage_names, rotation=45, ha='right')
        
        # 添加数值标签
        for bar, rate in zip(bars, retention_rates):
            height = bar.get_height()
            axes[0, 1].text(bar.get_x() + bar.get_width()/2., height + 1,
                           f'{rate:.1f}%', ha='center', va='bottom')
        
        # 3. 移除样本数
        removed_counts = [orig - opt for orig, opt in zip(original_counts, optimized_counts)]
        axes[1, 0].bar(stage_names, removed_counts, color='lightcoral')
        axes[1, 0].set_ylabel('移除样本数')
        axes[1, 0].set_title('各阶段移除样本数')
        axes[1, 0].set_xticklabels(stage_names, rotation=45, ha='right')
        
        # 4. 整体统计饼图
        total_original = sum(original_counts)
        total_optimized = sum(optimized_counts)
        total_removed = total_original - total_optimized
        
        axes[1, 1].pie([total_optimized, total_removed], 
                      labels=[f'保留样本\n({total_optimized})', f'移除样本\n({total_removed})'],
                      colors=['lightgreen', 'lightcoral'],
                      autopct='%1.1f%%')
        axes[1, 1].set_title('整体数据分布')
        
        plt.tight_layout()
        
        # 保存图表
        output_path = self.analysis_dir / 'optimization_summary.png'
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 汇总可视化完成，保存至: {output_path}")
    
    def run_analysis(self):
        """运行完整分析"""
        print("🔬 启动优化数据分析...")
        
        # 生成对比报告
        report = self.generate_comparison_report()
        
        # 创建汇总可视化
        self.create_summary_visualization()
        
        print("✅ 优化数据分析完成!")
        
        return report

def main():
    """主函数"""
    print("📊 优化数据分析器")
    print("="*50)
    
    analyzer = OptimizedDataAnalyzer()
    analyzer.run_analysis()
    
    print("🎉 分析完成!")

if __name__ == '__main__':
    main()
