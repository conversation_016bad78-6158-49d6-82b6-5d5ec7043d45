#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖检查和安装脚本
"""

import sys
import subprocess
import importlib
import os
from pathlib import Path

# 必需的包列表
REQUIRED_PACKAGES = {
    'flask': 'Flask==2.3.3',
    'flask_cors': 'Flask-CORS==4.0.0',
    'pandas': 'pandas==2.0.3',
    'numpy': 'numpy==1.24.3',
    'sklearn': 'scikit-learn==1.3.0',
    'scipy': 'scipy==1.11.1',
    'openpyxl': 'openpyxl==3.1.2',
}

# 可选包列表
OPTIONAL_PACKAGES = {
    'autogluon': 'autogluon',
    'xgboost': 'xgboost==1.7.6',
    'lightgbm': 'lightgbm==4.0.0',
}

def print_colored(text, color='white'):
    """打印彩色文本"""
    colors = {
        'red': '\033[91m',
        'green': '\033[92m',
        'yellow': '\033[93m',
        'blue': '\033[94m',
        'purple': '\033[95m',
        'cyan': '\033[96m',
        'white': '\033[97m',
        'end': '\033[0m'
    }
    print(f"{colors.get(color, colors['white'])}{text}{colors['end']}")

def check_python_version():
    """检查Python版本"""
    print_colored("🐍 检查Python版本...", 'blue')
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print_colored(f"❌ Python版本过低: {version.major}.{version.minor}", 'red')
        print_colored("需要Python 3.8或更高版本", 'red')
        return False
    
    print_colored(f"✅ Python版本: {version.major}.{version.minor}.{version.micro}", 'green')
    return True

def check_package(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_spec):
    """安装包"""
    try:
        # 尝试用户安装模式
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--user', package_spec])
        return True
    except subprocess.CalledProcessError:
        try:
            # 如果用户安装失败，尝试普通安装
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package_spec])
            return True
        except subprocess.CalledProcessError:
            return False

def check_and_install_packages():
    """检查并安装必需的包"""
    print_colored("\n📦 检查必需的Python包...", 'blue')
    
    missing_packages = []
    
    # 检查必需包
    for import_name, package_spec in REQUIRED_PACKAGES.items():
        if check_package(import_name):
            print_colored(f"✅ {import_name} 已安装", 'green')
        else:
            print_colored(f"❌ {import_name} 未安装", 'red')
            missing_packages.append(package_spec)
    
    # 安装缺失的包
    if missing_packages:
        print_colored(f"\n🔧 安装 {len(missing_packages)} 个缺失的包...", 'yellow')
        for package_spec in missing_packages:
            print_colored(f"安装 {package_spec}...", 'yellow')
            if install_package(package_spec):
                print_colored(f"✅ {package_spec} 安装成功", 'green')
            else:
                print_colored(f"❌ {package_spec} 安装失败", 'red')
                return False
    
    return True

def check_optional_packages():
    """检查可选包"""
    print_colored("\n🔍 检查可选包...", 'blue')
    
    for import_name, package_spec in OPTIONAL_PACKAGES.items():
        if check_package(import_name):
            print_colored(f"✅ {import_name} 已安装", 'green')
        else:
            print_colored(f"⚠️  {import_name} 未安装 (可选)", 'yellow')

def check_model_files():
    """检查模型文件"""
    print_colored("\n🤖 检查模型文件...", 'blue')
    
    model_stages = ['stage_1', 'stage_2', 'stage_3_A', 'stage_3_D', 'stage_4']
    models_path = Path('models')
    
    if not models_path.exists():
        print_colored("❌ models目录不存在", 'red')
        return False
    
    missing_stages = []
    for stage in model_stages:
        stage_path = models_path / stage
        if stage_path.exists() and any(stage_path.iterdir()):
            print_colored(f"✅ {stage} 模型文件存在", 'green')
        else:
            print_colored(f"❌ {stage} 模型文件缺失", 'red')
            missing_stages.append(stage)
    
    if missing_stages:
        print_colored(f"⚠️  缺失 {len(missing_stages)} 个阶段的模型文件", 'yellow')
        print_colored("请确保models目录下包含所有必要的模型文件", 'yellow')
        return False
    
    return True

def check_project_structure():
    """检查项目结构"""
    print_colored("\n📁 检查项目结构...", 'blue')
    
    required_dirs = ['backend', 'frontend', 'models', 'config']
    required_files = ['backend/app.py', 'frontend/index.html', 'requirements.txt']
    
    all_good = True
    
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print_colored(f"✅ {dir_name}/ 目录存在", 'green')
        else:
            print_colored(f"❌ {dir_name}/ 目录缺失", 'red')
            all_good = False
    
    for file_name in required_files:
        if Path(file_name).exists():
            print_colored(f"✅ {file_name} 文件存在", 'green')
        else:
            print_colored(f"❌ {file_name} 文件缺失", 'red')
            all_good = False
    
    return all_good

def main():
    """主函数"""
    print_colored("=" * 50, 'cyan')
    print_colored("🚀 基于机器学习的耐应力腐蚀油套管过程数据处理智能预测系统", 'cyan')
    print_colored("   依赖检查和安装工具", 'cyan')
    print_colored("=" * 50, 'cyan')
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查项目结构
    if not check_project_structure():
        print_colored("\n❌ 项目结构不完整，请检查文件和目录", 'red')
        sys.exit(1)
    
    # 检查并安装包
    if not check_and_install_packages():
        print_colored("\n❌ 包安装失败，请检查网络连接和权限", 'red')
        sys.exit(1)
    
    # 检查可选包
    check_optional_packages()
    
    # 检查模型文件
    check_model_files()
    
    print_colored("\n" + "=" * 50, 'cyan')
    print_colored("🎉 系统检查完成！", 'green')
    print_colored("现在可以运行 start_app.bat (Windows) 或 ./start_app.sh (Linux/Mac) 启动系统", 'green')
    print_colored("=" * 50, 'cyan')

if __name__ == '__main__':
    main()
